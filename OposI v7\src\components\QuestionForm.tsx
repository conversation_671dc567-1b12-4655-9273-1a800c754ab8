import React, { useState, useRef, useEffect } from 'react';
import {
  Documento,
  Mensaje as MensajeDB,
  crearConversacion,
  guardarMensaje,
  obtenerMensajesPorConversacionId,
  obtenerConversacionPorId,
  actualizarConversacion,
  activarConversacion,
  obtenerConversacionActiva,
  desactivarTodasLasConversaciones
} from '../lib/supabase';
import ConversationHistory from './ConversationHistory';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { preguntaFormSchema } from '../lib/formSchemas';
import { z } from 'zod';

interface QuestionFormProps {
  documentosSeleccionados: Documento[];
}

interface Mensaje {
  tipo: 'usuario' | 'ia';
  contenido: string;
  timestamp: Date;
  id?: string;
}

export default function QuestionForm({ documentosSeleccionados }: QuestionFormProps) {
  const [mensajes, setMensajes] = useState<Mensaje[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [conversacionActualId, setConversacionActualId] = useState<string | null>(null);
  const [mostrarHistorial, setMostrarHistorial] = useState(false);
  const [guardandoConversacion, setGuardandoConversacion] = useState(false);
  const [cargandoConversacionActiva, setCargandoConversacionActiva] = useState(true);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  const {
    register,
    handleSubmit: handleSubmitForm,
    formState: { errors, isValid },
    reset,
    setValue, // <-- Añadido para sincronizar documentos
    watch
  } = useForm<z.infer<typeof preguntaFormSchema>>({
    resolver: zodResolver(preguntaFormSchema),
    defaultValues: {
      pregunta: '',
      documentos: documentosSeleccionados,
    },
  });

  // Observar cambios en el formulario para debugging
  const watchedValues = watch();

  // Log de debugging para el estado del formulario
  useEffect(() => {
    console.log('📝 Estado del formulario:', {
      values: watchedValues,
      errors,
      isValid,
      documentosSeleccionados: documentosSeleccionados.length
    });

    // Log detallado de errores si los hay
    if (Object.keys(errors).length > 0) {
      console.error('❌ Errores de validación detallados:', errors);
      if (errors.documentos) {
        console.error('📄 Errores específicos en documentos:', errors.documentos);
      }
    }
  }, [watchedValues, errors, isValid, documentosSeleccionados]);

  // Sincronizar documentos seleccionados con el formulario, asegurando tipos correctos
  useEffect(() => {
    console.log('🔄 Sincronizando documentos con el formulario:', documentosSeleccionados);

    const documentosValidados = documentosSeleccionados.map(doc => {
      const docValidado = {
        ...doc,
        categoria: doc.categoria || null,
        numero_tema: doc.numero_tema !== undefined && doc.numero_tema !== null
          ? (typeof doc.numero_tema === 'string' ? parseInt(doc.numero_tema, 10) : doc.numero_tema)
          : undefined,
        // Asegurar que todos los campos opcionales estén presentes
        id: doc.id || undefined,
        creado_en: doc.creado_en || undefined,
        actualizado_en: doc.actualizado_en || undefined,
        user_id: doc.user_id || undefined,
        tipo_original: doc.tipo_original || undefined,
      };

      console.log('📄 Documento validado:', docValidado);
      return docValidado;
    });

    setValue('documentos', documentosValidados);
    console.log('✅ Documentos sincronizados con el formulario');
  }, [documentosSeleccionados, setValue]);

  // Efecto para cargar la conversación activa al iniciar
  useEffect(() => {
    const cargarConversacionActiva = async () => {
      setCargandoConversacionActiva(true);
      try {
        const conversacionActiva = await obtenerConversacionActiva();

        if (conversacionActiva) {
          console.log('Conversación activa encontrada:', conversacionActiva.id);
          setConversacionActualId(conversacionActiva.id);
          await cargarConversacion(conversacionActiva.id);
        } else {
          console.log('No hay conversación activa - esto es normal para usuarios nuevos');
          setMensajes([]);
          setConversacionActualId(null);
        }
      } catch (error) {
        console.warn('No se pudo cargar la conversación activa (esto es normal para usuarios nuevos):', error);
        // No mostrar error al usuario, simplemente inicializar sin conversación
        setMensajes([]);
        setConversacionActualId(null);
      } finally {
        setCargandoConversacionActiva(false);
      }
    };

    cargarConversacionActiva();
  }, []);

  // Efecto para hacer scroll al último mensaje cuando se añade uno nuevo
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [mensajes]);

  // Función para cargar una conversación desde Supabase
  const cargarConversacion = async (conversacionId: string) => {
    try {
      setIsLoading(true);

      // Activar la conversación seleccionada
      await activarConversacion(conversacionId);

      // Obtener los mensajes de la conversación
      const mensajesDB = await obtenerMensajesPorConversacionId(conversacionId);

      // Convertir los mensajes de la base de datos al formato local
      const mensajesFormateados: Mensaje[] = mensajesDB.map(msg => ({
        id: msg.id,
        tipo: msg.tipo,
        contenido: msg.contenido,
        timestamp: new Date(msg.timestamp)
      }));

      // Actualizar el estado
      setMensajes(mensajesFormateados);
      setConversacionActualId(conversacionId);
      setError('');

      console.log(`Conversación ${conversacionId} cargada y activada`);
    } catch (error) {
      console.error('Error al cargar la conversación:', error);
      setError('No se pudo cargar la conversación');
    } finally {
      setIsLoading(false);
    }
  };

  // Función para guardar un mensaje en Supabase
  const guardarMensajeEnDB = async (mensaje: Mensaje) => {
    try {
      setGuardandoConversacion(true);

      // Si no hay una conversación actual, crear una nueva
      if (!conversacionActualId) {
        // Solo crear una nueva conversación si es el primer mensaje del usuario
        if (mensaje.tipo === 'usuario') {
          // Crear un título basado en la primera pregunta
          const titulo = `Conversación: ${mensaje.contenido.substring(0, 50)}${mensaje.contenido.length > 50 ? '...' : ''}`;

          // Crear una nueva conversación y marcarla como activa
          const nuevoId = await crearConversacion(titulo, true);
          if (!nuevoId) {
            throw new Error('No se pudo crear la conversación');
          }

          console.log(`Nueva conversación creada y activada: ${nuevoId}`);

          // Guardar el ID de la conversación para futuros mensajes
          setConversacionActualId(nuevoId);

          // Guardar el mensaje en la nueva conversación
          await guardarMensaje({
            conversacion_id: nuevoId,
            tipo: mensaje.tipo,
            contenido: mensaje.contenido
          });
        } else {
          // Si es un mensaje de la IA pero no hay conversación actual,
          // algo salió mal. Intentar recuperar creando una nueva conversación.
          console.warn('No hay conversación actual para guardar el mensaje de la IA. Creando una nueva.');

          const titulo = 'Nueva conversación';
          const nuevoId = await crearConversacion(titulo, true);

          if (!nuevoId) {
            throw new Error('No se pudo crear la conversación');
          }

          console.log(`Nueva conversación de recuperación creada: ${nuevoId}`);
          setConversacionActualId(nuevoId);

          // Guardar el mensaje en la nueva conversación
          await guardarMensaje({
            conversacion_id: nuevoId,
            tipo: mensaje.tipo,
            contenido: mensaje.contenido
          });
        }
      } else {
        // Verificar que la conversación actual sigue siendo la activa
        const conversacionActiva = await obtenerConversacionActiva();

        if (!conversacionActiva || conversacionActiva.id !== conversacionActualId) {
          // Si la conversación actual no es la activa, activarla
          console.log(`Reactivando conversación: ${conversacionActualId}`);
          await activarConversacion(conversacionActualId);
        }

        // Guardar el mensaje en la conversación existente
        await guardarMensaje({
          conversacion_id: conversacionActualId,
          tipo: mensaje.tipo,
          contenido: mensaje.contenido
        });
      }
    } catch (error) {
      console.error('Error al guardar el mensaje:', error);
      // No mostramos error al usuario para no interrumpir la experiencia
    } finally {
      setGuardandoConversacion(false);
    }
  };

  // Función para iniciar una nueva conversación
  const iniciarNuevaConversacion = async () => {
    try {
      setIsLoading(true);

      // Desactivar todas las conversaciones en la base de datos
      await desactivarTodasLasConversaciones();

      // Limpiar los mensajes actuales
      setMensajes([]);
      // Establecer el ID de conversación a null para que se cree una nueva en el próximo mensaje
      setConversacionActualId(null);
      setError('');

      console.log('Nueva conversación iniciada. El próximo mensaje creará una nueva conversación en la base de datos.');
    } catch (error) {
      console.error('Error al iniciar nueva conversación:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Cambia handleSubmit para usar React Hook Form
  const onSubmit = async (data: z.infer<typeof preguntaFormSchema>) => {
    console.log('🎉 ¡FUNCIÓN onSubmit EJECUTADA!');
    console.log('🚀 Formulario enviado con datos:', data);
    console.log('📄 Documentos seleccionados:', documentosSeleccionados);
    console.log('✅ Validación pasada correctamente');

    setIsLoading(true);
    setError('');

    // Añadir la pregunta del usuario al historial
    const preguntaUsuario: Mensaje = {
      tipo: 'usuario',
      contenido: data.pregunta,
      timestamp: new Date()
    };

    setMensajes(prevMensajes => [...prevMensajes, preguntaUsuario]);
    setIsLoading(true);
    setError('');

    // Limpiar el campo de pregunta después de enviarla
    reset({ pregunta: '', documentos: documentosSeleccionados });

    try {
      // Guardar la pregunta del usuario en Supabase
      await guardarMensajeEnDB(preguntaUsuario);

      // Pasar los documentos completos a la función obtenerRespuestaIA
      // No solo el contenido, sino también el título, categoría y número de tema

      // Obtener respuesta de la IA
      const response = await fetch('/api/gemini', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          pregunta: preguntaUsuario.contenido,
          documentos: data.documentos
        })
      });
      const respuestaIA = await response.json();
      let respuestaTexto = '';
      if (respuestaIA.result) {
        respuestaTexto = typeof respuestaIA.result === 'string' ? respuestaIA.result : JSON.stringify(respuestaIA.result);
      } else if (respuestaIA.error) {
        respuestaTexto = typeof respuestaIA.error === 'string' ? respuestaIA.error : JSON.stringify(respuestaIA.error);
      } else {
        respuestaTexto = 'Error desconocido al obtener respuesta de la IA.';
      }

      // Añadir la respuesta de la IA al historial
      const mensajeIA: Mensaje = {
        tipo: 'ia',
        contenido: respuestaTexto,
        timestamp: new Date()
      };

      setMensajes(prevMensajes => [...prevMensajes, mensajeIA]);

      // Guardar la respuesta de la IA en Supabase
      await guardarMensajeEnDB(mensajeIA);

      // Si es la primera pregunta, actualizar el título de la conversación con un título más descriptivo
      if (mensajes.length === 0 && conversacionActualId) {
        const tituloMejorado = `Conversación: ${data.pregunta.substring(0, 50)}${data.pregunta.length > 50 ? '...' : ''}`;
        await actualizarConversacion(conversacionActualId, tituloMejorado);
      }
    } catch (error) {
      console.error('Error al obtener respuesta:', error);

      // Determinar el tipo de error y mostrar un mensaje más específico
      let mensajeError = 'Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, inténtalo de nuevo.';

      if (error instanceof Error) {
        if (error.message.includes('API key')) {
          mensajeError = 'Error de configuración: La clave de API de Gemini no está configurada correctamente.';
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          mensajeError = 'Error de conexión: No se pudo conectar con el servicio de IA. Verifica tu conexión a internet.';
        } else if (error.message.includes('quota') || error.message.includes('limit')) {
          mensajeError = 'Se ha alcanzado el límite de uso del servicio de IA. Inténtalo más tarde.';
        } else {
          mensajeError = `Error: ${error.message}`;
        }
      }

      setError(mensajeError);

      // Añadir mensaje de error como respuesta de la IA
      const mensajeErrorIA: Mensaje = {
        tipo: 'ia',
        contenido: mensajeError,
        timestamp: new Date()
      };

      setMensajes(prevMensajes => [...prevMensajes, mensajeErrorIA]);

      // Intentar guardar el mensaje de error en Supabase (sin fallar si no se puede)
      try {
        await guardarMensajeEnDB(mensajeErrorIA);
      } catch (dbError) {
        console.error('Error al guardar mensaje de error en DB:', dbError);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Formatear la fecha para mostrarla en el chat
  const formatearFecha = (fecha: Date): string => {
    return fecha.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="mt-6 flex flex-col h-[600px]">
      {/* Botones de control para historial y nueva conversación */}
      <div className="flex justify-between mb-4">
        <button
          type="button"
          onClick={() => setMostrarHistorial(!mostrarHistorial)}
          className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded inline-flex items-center"
        >
          {mostrarHistorial ? 'Ocultar historial' : 'Ver historial de conversaciones'}
        </button>

        <button
          type="button"
          onClick={iniciarNuevaConversacion}
          className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded inline-flex items-center"
          disabled={isLoading}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
          </svg>
          Nueva conversación
        </button>
      </div>

      {/* Mostrar el historial de conversaciones si está activado */}
      {mostrarHistorial && (
        <ConversationHistory
          onSelectConversation={cargarConversacion}
          conversacionActualId={conversacionActualId}
        />
      )}

      {/* Contenedor del chat con historial de mensajes */}
      <div
        ref={chatContainerRef}
        className="flex-grow overflow-y-auto mb-4 p-4 border rounded-lg bg-gray-50"
        style={{ height: 'calc(100% - 180px)' }}
      >
        {mensajes.length === 0 ? (
          <div className="flex items-center justify-center h-full text-gray-500">
            <p>Selecciona documentos y haz una pregunta para comenzar la conversación.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {mensajes.map((mensaje, index) => (
              <div
                key={mensaje.id || index}
                className={`flex ${mensaje.tipo === 'usuario' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] p-3 rounded-lg ${
                    mensaje.tipo === 'usuario'
                      ? 'bg-blue-500 text-white rounded-br-none'
                      : 'bg-white border border-gray-300 rounded-bl-none'
                  }`}
                >
                  <div className="whitespace-pre-wrap">{mensaje.contenido}</div>
                  <div
                    className={`text-xs mt-1 text-right ${
                      mensaje.tipo === 'usuario' ? 'text-blue-100' : 'text-gray-500'
                    }`}
                  >
                    {formatearFecha(mensaje.timestamp)}
                  </div>
                </div>
              </div>
            ))}

            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-white p-3 rounded-lg border border-gray-300 rounded-bl-none">
                  <div className="flex items-center space-x-2">
                    <div className="animate-bounce h-2 w-2 bg-gray-500 rounded-full"></div>
                    <div className="animate-bounce h-2 w-2 bg-gray-500 rounded-full" style={{ animationDelay: '0.2s' }}></div>
                    <div className="animate-bounce h-2 w-2 bg-gray-500 rounded-full" style={{ animationDelay: '0.4s' }}></div>
                  </div>
                </div>
              </div>
            )}

            {guardandoConversacion && (
              <div className="text-xs text-gray-500 text-center py-1">
                Guardando conversación...
              </div>
            )}
          </div>
        )}
      </div>

      {/* Formulario para enviar preguntas */}
      <form
        onSubmit={(e) => {
          console.log('📋 Evento onSubmit del formulario detectado');
          console.log('🎯 Llamando a handleSubmitForm...');
          handleSubmitForm(onSubmit)(e);
        }}
        className="mt-auto"
      >
        {error && (
          <div className="text-red-500 text-sm mb-2">{error}</div>
        )}

        {/* Indicador de documentos seleccionados */}
        <div className="text-xs text-gray-600 mb-2">
          {documentosSeleccionados.length > 0 ? (
            <span className="text-green-600">
              ✓ {documentosSeleccionados.length} documento{documentosSeleccionados.length !== 1 ? 's' : ''} seleccionado{documentosSeleccionados.length !== 1 ? 's' : ''}
            </span>
          ) : (
            <span className="text-red-600">
              ⚠ No hay documentos seleccionados. Selecciona al menos uno para hacer preguntas.
            </span>
          )}
        </div>

        <div className="flex items-end space-x-2">
          <div className="flex-grow">
            <textarea
              id="pregunta"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              rows={2}
              {...register('pregunta')}
              placeholder="Escribe tu pregunta sobre los documentos seleccionados..."
              disabled={isLoading}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSubmitForm(onSubmit)(); // Ejecutar la función devuelta
                }
              }}
            />
            {errors.pregunta && (
              <p className="text-red-500 text-xs mt-1">{errors.pregunta.message}</p>
            )}
            <p className="text-xs text-gray-500 mt-1">Presiona Enter para enviar, Shift+Enter para nueva línea</p>
          </div>

          <button
            type="submit"
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-full h-10 w-10 flex items-center justify-center focus:outline-none focus:shadow-outline disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isLoading || documentosSeleccionados.length === 0}
            title={documentosSeleccionados.length === 0 ? 'Selecciona al menos un documento para hacer una pregunta' : 'Enviar pregunta'}
            onClick={() => {
              console.log('🖱️ Click en botón de envío detectado');
              console.log('🔒 Botón deshabilitado?', isLoading || documentosSeleccionados.length === 0);
              console.log('⏳ isLoading:', isLoading);
              console.log('📄 documentos seleccionados:', documentosSeleccionados.length);
            }}
          >
            {isLoading ? (
              <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 008-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <svg className="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"></path>
              </svg>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
