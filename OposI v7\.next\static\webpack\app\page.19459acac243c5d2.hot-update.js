"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/QuestionForm.tsx":
/*!*****************************************!*\
  !*** ./src/components/QuestionForm.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuestionForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _ConversationHistory__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ConversationHistory */ \"(app-pages-browser)/./src/components/ConversationHistory.tsx\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _lib_formSchemas__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/formSchemas */ \"(app-pages-browser)/./src/lib/formSchemas.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction QuestionForm(param) {\n    let { documentosSeleccionados } = param;\n    _s();\n    const [mensajes, setMensajes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [conversacionActualId, setConversacionActualId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mostrarHistorial, setMostrarHistorial] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [guardandoConversacion, setGuardandoConversacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cargandoConversacionActiva, setCargandoConversacionActiva] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { register, handleSubmit: handleSubmitForm, formState: { errors }, reset, setValue// <-- Añadido para sincronizar documentos\n     } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(_lib_formSchemas__WEBPACK_IMPORTED_MODULE_5__.preguntaFormSchema),\n        defaultValues: {\n            pregunta: '',\n            documentos: documentosSeleccionados\n        }\n    });\n    // Sincronizar documentos seleccionados con el formulario, asegurando tipos correctos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionForm.useEffect\": ()=>{\n            const documentosValidados = documentosSeleccionados.map({\n                \"QuestionForm.useEffect.documentosValidados\": (doc)=>({\n                        ...doc,\n                        numero_tema: doc.numero_tema !== undefined && doc.numero_tema !== null ? Number(doc.numero_tema) : undefined\n                    })\n            }[\"QuestionForm.useEffect.documentosValidados\"]);\n            setValue('documentos', documentosValidados);\n        }\n    }[\"QuestionForm.useEffect\"], [\n        documentosSeleccionados,\n        setValue\n    ]);\n    // Efecto para cargar la conversación activa al iniciar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionForm.useEffect\": ()=>{\n            const cargarConversacionActiva = {\n                \"QuestionForm.useEffect.cargarConversacionActiva\": async ()=>{\n                    setCargandoConversacionActiva(true);\n                    try {\n                        const conversacionActiva = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerConversacionActiva)();\n                        if (conversacionActiva) {\n                            console.log('Conversación activa encontrada:', conversacionActiva.id);\n                            setConversacionActualId(conversacionActiva.id);\n                            await cargarConversacion(conversacionActiva.id);\n                        } else {\n                            console.log('No hay conversación activa - esto es normal para usuarios nuevos');\n                            setMensajes([]);\n                            setConversacionActualId(null);\n                        }\n                    } catch (error) {\n                        console.warn('No se pudo cargar la conversación activa (esto es normal para usuarios nuevos):', error);\n                        // No mostrar error al usuario, simplemente inicializar sin conversación\n                        setMensajes([]);\n                        setConversacionActualId(null);\n                    } finally{\n                        setCargandoConversacionActiva(false);\n                    }\n                }\n            }[\"QuestionForm.useEffect.cargarConversacionActiva\"];\n            cargarConversacionActiva();\n        }\n    }[\"QuestionForm.useEffect\"], []);\n    // Efecto para hacer scroll al último mensaje cuando se añade uno nuevo\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionForm.useEffect\": ()=>{\n            if (chatContainerRef.current) {\n                chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n            }\n        }\n    }[\"QuestionForm.useEffect\"], [\n        mensajes\n    ]);\n    // Función para cargar una conversación desde Supabase\n    const cargarConversacion = async (conversacionId)=>{\n        try {\n            setIsLoading(true);\n            // Activar la conversación seleccionada\n            await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.activarConversacion)(conversacionId);\n            // Obtener los mensajes de la conversación\n            const mensajesDB = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerMensajesPorConversacionId)(conversacionId);\n            // Convertir los mensajes de la base de datos al formato local\n            const mensajesFormateados = mensajesDB.map((msg)=>({\n                    id: msg.id,\n                    tipo: msg.tipo,\n                    contenido: msg.contenido,\n                    timestamp: new Date(msg.timestamp)\n                }));\n            // Actualizar el estado\n            setMensajes(mensajesFormateados);\n            setConversacionActualId(conversacionId);\n            setError('');\n            console.log(\"Conversaci\\xf3n \".concat(conversacionId, \" cargada y activada\"));\n        } catch (error) {\n            console.error('Error al cargar la conversación:', error);\n            setError('No se pudo cargar la conversación');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Función para guardar un mensaje en Supabase\n    const guardarMensajeEnDB = async (mensaje)=>{\n        try {\n            setGuardandoConversacion(true);\n            // Si no hay una conversación actual, crear una nueva\n            if (!conversacionActualId) {\n                // Solo crear una nueva conversación si es el primer mensaje del usuario\n                if (mensaje.tipo === 'usuario') {\n                    // Crear un título basado en la primera pregunta\n                    const titulo = \"Conversaci\\xf3n: \".concat(mensaje.contenido.substring(0, 50)).concat(mensaje.contenido.length > 50 ? '...' : '');\n                    // Crear una nueva conversación y marcarla como activa\n                    const nuevoId = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.crearConversacion)(titulo, true);\n                    if (!nuevoId) {\n                        throw new Error('No se pudo crear la conversación');\n                    }\n                    console.log(\"Nueva conversaci\\xf3n creada y activada: \".concat(nuevoId));\n                    // Guardar el ID de la conversación para futuros mensajes\n                    setConversacionActualId(nuevoId);\n                    // Guardar el mensaje en la nueva conversación\n                    await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.guardarMensaje)({\n                        conversacion_id: nuevoId,\n                        tipo: mensaje.tipo,\n                        contenido: mensaje.contenido\n                    });\n                } else {\n                    // Si es un mensaje de la IA pero no hay conversación actual,\n                    // algo salió mal. Intentar recuperar creando una nueva conversación.\n                    console.warn('No hay conversación actual para guardar el mensaje de la IA. Creando una nueva.');\n                    const titulo = 'Nueva conversación';\n                    const nuevoId = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.crearConversacion)(titulo, true);\n                    if (!nuevoId) {\n                        throw new Error('No se pudo crear la conversación');\n                    }\n                    console.log(\"Nueva conversaci\\xf3n de recuperaci\\xf3n creada: \".concat(nuevoId));\n                    setConversacionActualId(nuevoId);\n                    // Guardar el mensaje en la nueva conversación\n                    await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.guardarMensaje)({\n                        conversacion_id: nuevoId,\n                        tipo: mensaje.tipo,\n                        contenido: mensaje.contenido\n                    });\n                }\n            } else {\n                // Verificar que la conversación actual sigue siendo la activa\n                const conversacionActiva = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerConversacionActiva)();\n                if (!conversacionActiva || conversacionActiva.id !== conversacionActualId) {\n                    // Si la conversación actual no es la activa, activarla\n                    console.log(\"Reactivando conversaci\\xf3n: \".concat(conversacionActualId));\n                    await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.activarConversacion)(conversacionActualId);\n                }\n                // Guardar el mensaje en la conversación existente\n                await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.guardarMensaje)({\n                    conversacion_id: conversacionActualId,\n                    tipo: mensaje.tipo,\n                    contenido: mensaje.contenido\n                });\n            }\n        } catch (error) {\n            console.error('Error al guardar el mensaje:', error);\n        // No mostramos error al usuario para no interrumpir la experiencia\n        } finally{\n            setGuardandoConversacion(false);\n        }\n    };\n    // Función para iniciar una nueva conversación\n    const iniciarNuevaConversacion = async ()=>{\n        try {\n            setIsLoading(true);\n            // Desactivar todas las conversaciones en la base de datos\n            await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.desactivarTodasLasConversaciones)();\n            // Limpiar los mensajes actuales\n            setMensajes([]);\n            // Establecer el ID de conversación a null para que se cree una nueva en el próximo mensaje\n            setConversacionActualId(null);\n            setError('');\n            console.log('Nueva conversación iniciada. El próximo mensaje creará una nueva conversación en la base de datos.');\n        } catch (error) {\n            console.error('Error al iniciar nueva conversación:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Cambia handleSubmit para usar React Hook Form\n    const onSubmit = async (data)=>{\n        console.log('🚀 Formulario enviado con datos:', data);\n        console.log('📄 Documentos seleccionados:', documentosSeleccionados);\n        setIsLoading(true);\n        setError('');\n        // Añadir la pregunta del usuario al historial\n        const preguntaUsuario = {\n            tipo: 'usuario',\n            contenido: data.pregunta,\n            timestamp: new Date()\n        };\n        setMensajes((prevMensajes)=>[\n                ...prevMensajes,\n                preguntaUsuario\n            ]);\n        setIsLoading(true);\n        setError('');\n        // Limpiar el campo de pregunta después de enviarla\n        reset({\n            pregunta: '',\n            documentos: documentosSeleccionados\n        });\n        try {\n            // Guardar la pregunta del usuario en Supabase\n            await guardarMensajeEnDB(preguntaUsuario);\n            // Pasar los documentos completos a la función obtenerRespuestaIA\n            // No solo el contenido, sino también el título, categoría y número de tema\n            // Obtener respuesta de la IA\n            const response = await fetch('/api/gemini', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    pregunta: preguntaUsuario.contenido,\n                    documentos: data.documentos\n                })\n            });\n            const respuestaIA = await response.json();\n            let respuestaTexto = '';\n            if (respuestaIA.result) {\n                respuestaTexto = typeof respuestaIA.result === 'string' ? respuestaIA.result : JSON.stringify(respuestaIA.result);\n            } else if (respuestaIA.error) {\n                respuestaTexto = typeof respuestaIA.error === 'string' ? respuestaIA.error : JSON.stringify(respuestaIA.error);\n            } else {\n                respuestaTexto = 'Error desconocido al obtener respuesta de la IA.';\n            }\n            // Añadir la respuesta de la IA al historial\n            const mensajeIA = {\n                tipo: 'ia',\n                contenido: respuestaTexto,\n                timestamp: new Date()\n            };\n            setMensajes((prevMensajes)=>[\n                    ...prevMensajes,\n                    mensajeIA\n                ]);\n            // Guardar la respuesta de la IA en Supabase\n            await guardarMensajeEnDB(mensajeIA);\n            // Si es la primera pregunta, actualizar el título de la conversación con un título más descriptivo\n            if (mensajes.length === 0 && conversacionActualId) {\n                const tituloMejorado = \"Conversaci\\xf3n: \".concat(data.pregunta.substring(0, 50)).concat(data.pregunta.length > 50 ? '...' : '');\n                await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.actualizarConversacion)(conversacionActualId, tituloMejorado);\n            }\n        } catch (error) {\n            console.error('Error al obtener respuesta:', error);\n            // Determinar el tipo de error y mostrar un mensaje más específico\n            let mensajeError = 'Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, inténtalo de nuevo.';\n            if (error instanceof Error) {\n                if (error.message.includes('API key')) {\n                    mensajeError = 'Error de configuración: La clave de API de Gemini no está configurada correctamente.';\n                } else if (error.message.includes('network') || error.message.includes('fetch')) {\n                    mensajeError = 'Error de conexión: No se pudo conectar con el servicio de IA. Verifica tu conexión a internet.';\n                } else if (error.message.includes('quota') || error.message.includes('limit')) {\n                    mensajeError = 'Se ha alcanzado el límite de uso del servicio de IA. Inténtalo más tarde.';\n                } else {\n                    mensajeError = \"Error: \".concat(error.message);\n                }\n            }\n            setError(mensajeError);\n            // Añadir mensaje de error como respuesta de la IA\n            const mensajeErrorIA = {\n                tipo: 'ia',\n                contenido: mensajeError,\n                timestamp: new Date()\n            };\n            setMensajes((prevMensajes)=>[\n                    ...prevMensajes,\n                    mensajeErrorIA\n                ]);\n            // Intentar guardar el mensaje de error en Supabase (sin fallar si no se puede)\n            try {\n                await guardarMensajeEnDB(mensajeErrorIA);\n            } catch (dbError) {\n                console.error('Error al guardar mensaje de error en DB:', dbError);\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Formatear la fecha para mostrarla en el chat\n    const formatearFecha = (fecha)=>{\n        return fecha.toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-6 flex flex-col h-[600px]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>setMostrarHistorial(!mostrarHistorial),\n                        className: \"bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded inline-flex items-center\",\n                        children: mostrarHistorial ? 'Ocultar historial' : 'Ver historial de conversaciones'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: iniciarNuevaConversacion,\n                        className: \"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded inline-flex items-center\",\n                        disabled: isLoading,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"h-5 w-5 mr-2\",\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 11\n                            }, this),\n                            \"Nueva conversaci\\xf3n\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                lineNumber: 348,\n                columnNumber: 7\n            }, this),\n            mostrarHistorial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConversationHistory__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onSelectConversation: cargarConversacion,\n                conversacionActualId: conversacionActualId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                lineNumber: 372,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatContainerRef,\n                className: \"flex-grow overflow-y-auto mb-4 p-4 border rounded-lg bg-gray-50\",\n                style: {\n                    height: 'calc(100% - 180px)'\n                },\n                children: mensajes.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full text-gray-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Selecciona documentos y haz una pregunta para comenzar la conversaci\\xf3n.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                    lineNumber: 385,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        mensajes.map((mensaje, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex \".concat(mensaje.tipo === 'usuario' ? 'justify-end' : 'justify-start'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-[80%] p-3 rounded-lg \".concat(mensaje.tipo === 'usuario' ? 'bg-blue-500 text-white rounded-br-none' : 'bg-white border border-gray-300 rounded-bl-none'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"whitespace-pre-wrap\",\n                                            children: mensaje.contenido\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs mt-1 text-right \".concat(mensaje.tipo === 'usuario' ? 'text-blue-100' : 'text-gray-500'),\n                                            children: formatearFecha(mensaje.timestamp)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 17\n                                }, this)\n                            }, mensaje.id || index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 15\n                            }, this)),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-3 rounded-lg border border-gray-300 rounded-bl-none\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-bounce h-2 w-2 bg-gray-500 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-bounce h-2 w-2 bg-gray-500 rounded-full\",\n                                            style: {\n                                                animationDelay: '0.2s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-bounce h-2 w-2 bg-gray-500 rounded-full\",\n                                            style: {\n                                                animationDelay: '0.4s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 15\n                        }, this),\n                        guardandoConversacion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 text-center py-1\",\n                            children: \"Guardando conversaci\\xf3n...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                            lineNumber: 427,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                    lineNumber: 389,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                lineNumber: 379,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmitForm(onSubmit),\n                className: \"mt-auto\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-500 text-sm mb-2\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 438,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-600 mb-2\",\n                        children: documentosSeleccionados.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-600\",\n                            children: [\n                                \"✓ \",\n                                documentosSeleccionados.length,\n                                \" documento\",\n                                documentosSeleccionados.length !== 1 ? 's' : '',\n                                \" seleccionado\",\n                                documentosSeleccionados.length !== 1 ? 's' : ''\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-600\",\n                            children: \"⚠ No hay documentos seleccionados. Selecciona al menos uno para hacer preguntas.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-grow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"pregunta\",\n                                        className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                        rows: 2,\n                                        ...register('pregunta'),\n                                        placeholder: \"Escribe tu pregunta sobre los documentos seleccionados...\",\n                                        disabled: isLoading,\n                                        onKeyDown: (e)=>{\n                                            if (e.key === 'Enter' && !e.shiftKey) {\n                                                e.preventDefault();\n                                                handleSubmitForm(onSubmit)(); // Ejecutar la función devuelta\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.pregunta && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-500 text-xs mt-1\",\n                                        children: errors.pregunta.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: \"Presiona Enter para enviar, Shift+Enter para nueva l\\xednea\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-full h-10 w-10 flex items-center justify-center focus:outline-none focus:shadow-outline disabled:opacity-50 disabled:cursor-not-allowed\",\n                                disabled: isLoading || documentosSeleccionados.length === 0,\n                                title: documentosSeleccionados.length === 0 ? 'Selecciona al menos un documento para hacer una pregunta' : 'Enviar pregunta',\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin h-5 w-5 text-white\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 008-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-5 w-5 text-white\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 454,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                lineNumber: 436,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n        lineNumber: 346,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionForm, \"tfR+tk0F7qhXsixIRdOvGhuwjCA=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm\n    ];\n});\n_c = QuestionForm;\nvar _c;\n$RefreshReg$(_c, \"QuestionForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/QuestionForm.tsx\n"));

/***/ })

});