"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_supabase_documentosService_server_ts";
exports.ids = ["_rsc_src_lib_supabase_documentosService_server_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/supabase/documentosService.server.ts":
/*!******************************************************!*\
  !*** ./src/lib/supabase/documentosService.server.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   guardarDocumentoServer: () => (/* binding */ guardarDocumentoServer)\n/* harmony export */ });\n/* harmony import */ var _server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n/**\n * Guarda un nuevo documento en la base de datos asociado al usuario actual (versión servidor)\n */ async function guardarDocumentoServer(documento) {\n    try {\n        // Crear cliente de Supabase para el servidor\n        const supabase = await (0,_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n        // Obtener el usuario actual\n        const { data: { user }, error: userError } = await supabase.auth.getUser();\n        if (userError || !user) {\n            console.error('No hay usuario autenticado:', userError?.message);\n            return null;\n        }\n        // Añadir el user_id y tipo_original al documento\n        const documentoConUsuario = {\n            ...documento,\n            user_id: user.id,\n            tipo_original: documento.tipo_original // Asegurarse que tipo_original se pasa aquí\n        };\n        const { data, error } = await supabase.from('documentos').insert([\n            documentoConUsuario\n        ]).select();\n        if (error) {\n            console.error('Error al guardar documento:', error);\n            return null;\n        }\n        return data?.[0]?.id || null;\n    } catch (error) {\n        console.error('Error al guardar documento:', error);\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL2RvY3VtZW50b3NTZXJ2aWNlLnNlcnZlci50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFzRDtBQUd0RDs7Q0FFQyxHQUNNLGVBQWVDLHVCQUF1QkMsU0FBNkU7SUFDeEgsSUFBSTtRQUNGLDZDQUE2QztRQUM3QyxNQUFNQyxXQUFXLE1BQU1ILG1FQUEwQkE7UUFFakQsNEJBQTRCO1FBQzVCLE1BQU0sRUFBRUksTUFBTSxFQUFFQyxJQUFJLEVBQUUsRUFBRUMsT0FBT0MsU0FBUyxFQUFFLEdBQUcsTUFBTUosU0FBU0ssSUFBSSxDQUFDQyxPQUFPO1FBRXhFLElBQUlGLGFBQWEsQ0FBQ0YsTUFBTTtZQUN0QkssUUFBUUosS0FBSyxDQUFDLCtCQUErQkMsV0FBV0k7WUFDeEQsT0FBTztRQUNUO1FBRUEsaURBQWlEO1FBQ2pELE1BQU1DLHNCQUFzQjtZQUMxQixHQUFHVixTQUFTO1lBQ1pXLFNBQVNSLEtBQUtTLEVBQUU7WUFDaEJDLGVBQWViLFVBQVVhLGFBQWEsQ0FBQyw0Q0FBNEM7UUFDckY7UUFFQSxNQUFNLEVBQUVYLElBQUksRUFBRUUsS0FBSyxFQUFFLEdBQUcsTUFBTUgsU0FDM0JhLElBQUksQ0FBQyxjQUNMQyxNQUFNLENBQUM7WUFBQ0w7U0FBb0IsRUFDNUJNLE1BQU07UUFFVCxJQUFJWixPQUFPO1lBQ1RJLFFBQVFKLEtBQUssQ0FBQywrQkFBK0JBO1lBQzdDLE9BQU87UUFDVDtRQUVBLE9BQU9GLE1BQU0sQ0FBQyxFQUFFLEVBQUVVLE1BQU07SUFDMUIsRUFBRSxPQUFPUixPQUFPO1FBQ2RJLFFBQVFKLEtBQUssQ0FBQywrQkFBK0JBO1FBQzdDLE9BQU87SUFDVDtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcc3JjXFxsaWJcXHN1cGFiYXNlXFxkb2N1bWVudG9zU2VydmljZS5zZXJ2ZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlU2VydmVyU3VwYWJhc2VDbGllbnQgfSBmcm9tICcuL3NlcnZlcic7XG5pbXBvcnQgeyBEb2N1bWVudG8gfSBmcm9tICcuL3N1cGFiYXNlQ2xpZW50JztcblxuLyoqXG4gKiBHdWFyZGEgdW4gbnVldm8gZG9jdW1lbnRvIGVuIGxhIGJhc2UgZGUgZGF0b3MgYXNvY2lhZG8gYWwgdXN1YXJpbyBhY3R1YWwgKHZlcnNpw7NuIHNlcnZpZG9yKVxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ3VhcmRhckRvY3VtZW50b1NlcnZlcihkb2N1bWVudG86IE9taXQ8RG9jdW1lbnRvLCAnaWQnIHwgJ2NyZWFkb19lbicgfCAnYWN0dWFsaXphZG9fZW4nIHwgJ3VzZXJfaWQnPik6IFByb21pc2U8c3RyaW5nIHwgbnVsbD4ge1xuICB0cnkge1xuICAgIC8vIENyZWFyIGNsaWVudGUgZGUgU3VwYWJhc2UgcGFyYSBlbCBzZXJ2aWRvclxuICAgIGNvbnN0IHN1cGFiYXNlID0gYXdhaXQgY3JlYXRlU2VydmVyU3VwYWJhc2VDbGllbnQoKTtcbiAgICBcbiAgICAvLyBPYnRlbmVyIGVsIHVzdWFyaW8gYWN0dWFsXG4gICAgY29uc3QgeyBkYXRhOiB7IHVzZXIgfSwgZXJyb3I6IHVzZXJFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKCk7XG5cbiAgICBpZiAodXNlckVycm9yIHx8ICF1c2VyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdObyBoYXkgdXN1YXJpbyBhdXRlbnRpY2FkbzonLCB1c2VyRXJyb3I/Lm1lc3NhZ2UpO1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuXG4gICAgLy8gQcOxYWRpciBlbCB1c2VyX2lkIHkgdGlwb19vcmlnaW5hbCBhbCBkb2N1bWVudG9cbiAgICBjb25zdCBkb2N1bWVudG9Db25Vc3VhcmlvID0ge1xuICAgICAgLi4uZG9jdW1lbnRvLFxuICAgICAgdXNlcl9pZDogdXNlci5pZCxcbiAgICAgIHRpcG9fb3JpZ2luYWw6IGRvY3VtZW50by50aXBvX29yaWdpbmFsIC8vIEFzZWd1cmFyc2UgcXVlIHRpcG9fb3JpZ2luYWwgc2UgcGFzYSBhcXXDrVxuICAgIH07XG5cbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2RvY3VtZW50b3MnKVxuICAgICAgLmluc2VydChbZG9jdW1lbnRvQ29uVXN1YXJpb10pXG4gICAgICAuc2VsZWN0KCk7XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIGd1YXJkYXIgZG9jdW1lbnRvOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cblxuICAgIHJldHVybiBkYXRhPy5bMF0/LmlkIHx8IG51bGw7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgYWwgZ3VhcmRhciBkb2N1bWVudG86JywgZXJyb3IpO1xuICAgIHJldHVybiBudWxsO1xuICB9XG59XG4iXSwibmFtZXMiOlsiY3JlYXRlU2VydmVyU3VwYWJhc2VDbGllbnQiLCJndWFyZGFyRG9jdW1lbnRvU2VydmVyIiwiZG9jdW1lbnRvIiwic3VwYWJhc2UiLCJkYXRhIiwidXNlciIsImVycm9yIiwidXNlckVycm9yIiwiYXV0aCIsImdldFVzZXIiLCJjb25zb2xlIiwibWVzc2FnZSIsImRvY3VtZW50b0NvblVzdWFyaW8iLCJ1c2VyX2lkIiwiaWQiLCJ0aXBvX29yaWdpbmFsIiwiZnJvbSIsImluc2VydCIsInNlbGVjdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/documentosService.server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerSupabaseClient: () => (/* binding */ createServerSupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// Cliente para el servidor (componentes del servidor, API routes)\nasync function createServerSupabaseClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\", {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL3NlcnZlci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBbUQ7QUFDWjtBQUV2QyxrRUFBa0U7QUFDM0QsZUFBZUU7SUFDcEIsTUFBTUMsY0FBYyxNQUFNRixxREFBT0E7SUFFakMsT0FBT0QsaUVBQWtCQSxDQUN2QkksMENBQW9DLEVBQ3BDQSxrTkFBeUMsRUFDekM7UUFDRUgsU0FBUztZQUNQTztnQkFDRSxPQUFPTCxZQUFZSyxNQUFNO1lBQzNCO1lBQ0FDLFFBQU9DLFlBQVk7Z0JBQ2pCLElBQUk7b0JBQ0ZBLGFBQWFDLE9BQU8sQ0FBQyxDQUFDLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFQyxPQUFPLEVBQUUsR0FDNUNYLFlBQVlZLEdBQUcsQ0FBQ0gsTUFBTUMsT0FBT0M7Z0JBRWpDLEVBQUUsT0FBTTtnQkFDTiwwREFBMEQ7Z0JBQzFELHdEQUF3RDtnQkFDeEQsaUJBQWlCO2dCQUNuQjtZQUNGO1FBQ0Y7SUFDRjtBQUVKIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcc3JjXFxsaWJcXHN1cGFiYXNlXFxzZXJ2ZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlU2VydmVyQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3Nzcic7XG5pbXBvcnQgeyBjb29raWVzIH0gZnJvbSAnbmV4dC9oZWFkZXJzJztcblxuLy8gQ2xpZW50ZSBwYXJhIGVsIHNlcnZpZG9yIChjb21wb25lbnRlcyBkZWwgc2Vydmlkb3IsIEFQSSByb3V0ZXMpXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY3JlYXRlU2VydmVyU3VwYWJhc2VDbGllbnQoKSB7XG4gIGNvbnN0IGNvb2tpZVN0b3JlID0gYXdhaXQgY29va2llcygpO1xuXG4gIHJldHVybiBjcmVhdGVTZXJ2ZXJDbGllbnQoXG4gICAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMISxcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSEsXG4gICAge1xuICAgICAgY29va2llczoge1xuICAgICAgICBnZXRBbGwoKSB7XG4gICAgICAgICAgcmV0dXJuIGNvb2tpZVN0b3JlLmdldEFsbCgpO1xuICAgICAgICB9LFxuICAgICAgICBzZXRBbGwoY29va2llc1RvU2V0KSB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvb2tpZXNUb1NldC5mb3JFYWNoKCh7IG5hbWUsIHZhbHVlLCBvcHRpb25zIH0pID0+XG4gICAgICAgICAgICAgIGNvb2tpZVN0b3JlLnNldChuYW1lLCB2YWx1ZSwgb3B0aW9ucylcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfSBjYXRjaCB7XG4gICAgICAgICAgICAvLyBUaGUgYHNldEFsbGAgbWV0aG9kIHdhcyBjYWxsZWQgZnJvbSBhIFNlcnZlciBDb21wb25lbnQuXG4gICAgICAgICAgICAvLyBUaGlzIGNhbiBiZSBpZ25vcmVkIGlmIHlvdSBoYXZlIG1pZGRsZXdhcmUgcmVmcmVzaGluZ1xuICAgICAgICAgICAgLy8gdXNlciBzZXNzaW9ucy5cbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICB9LFxuICAgIH1cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVTZXJ2ZXJDbGllbnQiLCJjb29raWVzIiwiY3JlYXRlU2VydmVyU3VwYWJhc2VDbGllbnQiLCJjb29raWVTdG9yZSIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSIsImdldEFsbCIsInNldEFsbCIsImNvb2tpZXNUb1NldCIsImZvckVhY2giLCJuYW1lIiwidmFsdWUiLCJvcHRpb25zIiwic2V0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ })

};
;