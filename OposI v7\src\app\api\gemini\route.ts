import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';
import { generarTest, generarFlashcards, generarMapaMental } from '@/lib/gemini';
import { obtenerRespuestaIA } from '@/lib/gemini/questionService';
import { ApiGeminiInputSchema } from '@/lib/zodSchemas';

// API route for Gemini actions
export async function POST(req: NextRequest) {
  try {
    // Obtener cookies de forma compatible con Next.js 15
    const cookieStore = await cookies();

    // Crear cliente de Supabase directamente con configuración simplificada
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    // Intentar obtener el token de las cookies manualmente
    const authTokenCookie = cookieStore.get('sb-fxnhpxjijinfuxxxplzj-auth-token');
    if (authTokenCookie?.value) {
      try {
        const tokenData = JSON.parse(authTokenCookie.value);
        if (tokenData.access_token) {
          await supabase.auth.setSession({
            access_token: tokenData.access_token,
            refresh_token: tokenData.refresh_token
          });
        }
      } catch (e) {
        console.log('Error parsing auth token:', e);
      }
    }

    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    // Diagnóstico mejorado para debugging
    console.log('Session check:', {
      hasSession: !!session,
      sessionError: sessionError?.message,
      userId: session?.user?.id,
      cookies: req.headers.get('cookie')?.includes('supabase') ? 'present' : 'missing'
    });

    if (!session) {
      return NextResponse.json({
        error: 'Unauthorized',
        debug: {
          sessionError: sessionError?.message,
          hasCookies: !!req.headers.get('cookie')
        }
      }, { status: 401 });
    }

    const body = await req.json();
    // Validación robusta de entrada
    const parseResult = ApiGeminiInputSchema.safeParse(body);
    if (!parseResult.success) {
      return NextResponse.json({ error: 'Datos inválidos', detalles: parseResult.error.errors }, { status: 400 });
    }
    // Compatibilidad: si viene pregunta+documentos, es para obtenerRespuestaIA
    if (body.pregunta && body.documentos) {
      const result = await obtenerRespuestaIA(body.pregunta, body.documentos);
      return NextResponse.json({ result });
    }
    const { action, peticion, contextos } = body;
    let result;
    switch (action) {
      case 'generarTest':
        result = await generarTest(peticion, contextos);
        break;
      case 'generarFlashcards':
        result = await generarFlashcards(peticion, contextos);
        break;
      case 'generarMapaMental':
        result = await generarMapaMental(peticion, contextos);
        break;
      default:
        return NextResponse.json({ error: 'Acción no soportada' }, { status: 400 });
    }
    return NextResponse.json({ result });
  } catch (error) {
    return NextResponse.json({ error: (error as Error).message }, { status: 500 });
  }
}
