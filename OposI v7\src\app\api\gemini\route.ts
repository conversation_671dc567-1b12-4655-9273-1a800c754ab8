import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { generarTest, generarFlashcards, generarMapaMental } from '@/lib/gemini';
import { obtenerRespuestaIA } from '@/lib/gemini/questionService';
import { ApiGeminiInputSchema } from '@/lib/zodSchemas';

// API route for Gemini actions
export async function POST(req: NextRequest) {
  try {
    // Crear cliente de Supabase usando la implementación correcta
    const supabase = await createServerSupabaseClient();

    const { data: { user }, error: userError } = await supabase.auth.getUser();

    // Diagnóstico mejorado para debugging
    console.log('User check:', {
      hasUser: !!user,
      userError: userError?.message,
      userId: user?.id,
      cookies: req.headers.get('cookie')?.includes('supabase') ? 'present' : 'missing'
    });

    if (!user) {
      return NextResponse.json({
        error: 'Unauthorized',
        debug: {
          userError: userError?.message,
          hasCookies: !!req.headers.get('cookie')
        }
      }, { status: 401 });
    }

    const body = await req.json();
    // Validación robusta de entrada
    const parseResult = ApiGeminiInputSchema.safeParse(body);
    if (!parseResult.success) {
      return NextResponse.json({ error: 'Datos inválidos', detalles: parseResult.error.errors }, { status: 400 });
    }
    // Compatibilidad: si viene pregunta+documentos, es para obtenerRespuestaIA
    if (body.pregunta && body.documentos) {
      const result = await obtenerRespuestaIA(body.pregunta, body.documentos);
      return NextResponse.json({ result });
    }
    const { action, peticion, contextos } = body;
    let result;
    switch (action) {
      case 'generarTest':
        result = await generarTest(peticion, contextos);
        break;
      case 'generarFlashcards':
        result = await generarFlashcards(peticion, contextos);
        break;
      case 'generarMapaMental':
        result = await generarMapaMental(peticion, contextos);
        break;
      default:
        return NextResponse.json({ error: 'Acción no soportada' }, { status: 400 });
    }
    return NextResponse.json({ result });
  } catch (error) {
    return NextResponse.json({ error: (error as Error).message }, { status: 500 });
  }
}
