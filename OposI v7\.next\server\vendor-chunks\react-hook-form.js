"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-hook-form";
exports.ids = ["vendor-chunks/react-hook-form"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/react-hook-form/dist/index.esm.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   FormProvider: () => (/* binding */ FormProvider),\n/* harmony export */   appendErrors: () => (/* binding */ appendErrors),\n/* harmony export */   createFormControl: () => (/* binding */ createFormControl),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   useController: () => (/* binding */ useController),\n/* harmony export */   useFieldArray: () => (/* binding */ useFieldArray),\n/* harmony export */   useForm: () => (/* binding */ useForm),\n/* harmony export */   useFormContext: () => (/* binding */ useFormContext),\n/* harmony export */   useFormState: () => (/* binding */ useFormState),\n/* harmony export */   useWatch: () => (/* binding */ useWatch)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nconst _excluded = [\"children\"],\n  _excluded2 = [\"control\", \"onSubmit\", \"children\", \"action\", \"method\", \"headers\", \"encType\", \"onError\", \"render\", \"onSuccess\", \"validateStatus\"],\n  _excluded3 = [\"_f\"],\n  _excluded4 = [\"name\"],\n  _excluded5 = [\"_f\"],\n  _excluded6 = [\"ref\", \"message\", \"type\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\n\nvar isCheckBoxInput = element => element.type === 'checkbox';\nvar isDateObject = value => value instanceof Date;\nvar isNullOrUndefined = value => value == null;\nconst isObjectType = value => typeof value === 'object';\nvar isObject = value => !isNullOrUndefined(value) && !Array.isArray(value) && isObjectType(value) && !isDateObject(value);\nvar getEventValue = event => isObject(event) && event.target ? isCheckBoxInput(event.target) ? event.target.checked : event.target.value : event;\nvar getNodeParentName = name => name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\nvar isNameInFieldArray = (names, name) => names.has(getNodeParentName(name));\nvar isPlainObject = tempObject => {\n  const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n  return isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf');\n};\nvar isWeb =  false && 0;\nfunction cloneObject(data) {\n  let copy;\n  const isArray = Array.isArray(data);\n  const isFileListInstance = typeof FileList !== 'undefined' ? data instanceof FileList : false;\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (data instanceof Set) {\n    copy = new Set(data);\n  } else if (!(isWeb && (data instanceof Blob || isFileListInstance)) && (isArray || isObject(data))) {\n    copy = isArray ? [] : {};\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n  return copy;\n}\nvar compact = value => Array.isArray(value) ? value.filter(Boolean) : [];\nvar isUndefined = val => val === undefined;\nvar get = (object, path, defaultValue) => {\n  if (!path || !isObject(object)) {\n    return defaultValue;\n  }\n  const result = compact(path.split(/[,[\\].]+?/)).reduce((result, key) => isNullOrUndefined(result) ? result : result[key], object);\n  return isUndefined(result) || result === object ? isUndefined(object[path]) ? defaultValue : object[path] : result;\n};\nvar isBoolean = value => typeof value === 'boolean';\nvar isKey = value => /^\\w*$/.test(value);\nvar stringToPath = input => compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\nvar set = (object, path, value) => {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue = isObject(objValue) || Array.isArray(objValue) ? objValue : !isNaN(+tempPath[index + 1]) ? [] : {};\n    }\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return;\n    }\n    object[key] = newValue;\n    object = object[key];\n  }\n};\nconst EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change'\n};\nconst VALIDATION_MODE = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all'\n};\nconst INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate'\n};\nconst HookFormContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst useFormContext = () => react__WEBPACK_IMPORTED_MODULE_0__.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst FormProvider = props => {\n  const {\n      children\n    } = props,\n    data = _objectWithoutProperties(props, _excluded);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(HookFormContext.Provider, {\n    value: data\n  }, children);\n};\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true) => {\n  const result = {\n    defaultValues: control._defaultValues\n  };\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key;\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      }\n    });\n  }\n  return result;\n};\nconst useIsomorphicLayoutEffect =  false ? 0 : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFormState(props) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    disabled,\n    name,\n    exact\n  } = props || {};\n  const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._formState);\n  const _localProxyFormState = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    validatingFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false\n  });\n  useIsomorphicLayoutEffect(() => control._subscribe({\n    name: name,\n    formState: _localProxyFormState.current,\n    exact,\n    callback: formState => {\n      !disabled && updateFormState(_objectSpread(_objectSpread({}, control._formState), formState));\n    }\n  }), [name, disabled, exact]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    _localProxyFormState.current.isValid && control._setValid(true);\n  }, [control]);\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => getProxyFormState(formState, control, _localProxyFormState.current, false), [formState, control]);\n}\nvar isString = value => typeof value === 'string';\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n  if (Array.isArray(names)) {\n    return names.map(fieldName => (isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)));\n  }\n  isGlobal && (_names.watchAll = true);\n  return formValues;\n};\n\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nfunction useWatch(props) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact\n  } = props || {};\n  const _defaultValue = react__WEBPACK_IMPORTED_MODULE_0__.useRef(defaultValue);\n  const [value, updateValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getWatch(name, _defaultValue.current));\n  useIsomorphicLayoutEffect(() => control._subscribe({\n    name: name,\n    formState: {\n      values: true\n    },\n    exact,\n    callback: formState => !disabled && updateValue(generateWatchOutput(name, control._names, formState.values || control._formValues, false, _defaultValue.current))\n  }), [name, control, disabled, exact]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => control._removeUnmounted());\n  return value;\n}\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nfunction useController(props) {\n  const methods = useFormContext();\n  const {\n    name,\n    disabled,\n    control = methods.control,\n    shouldUnregister\n  } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n    exact: true\n  });\n  const formState = useFormState({\n    control,\n    name,\n    exact: true\n  });\n  const _props = react__WEBPACK_IMPORTED_MODULE_0__.useRef(props);\n  const _registerProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control.register(name, _objectSpread(_objectSpread({}, props.rules), {}, {\n    value\n  }, isBoolean(props.disabled) ? {\n    disabled: props.disabled\n  } : {})));\n  const fieldState = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => Object.defineProperties({}, {\n    invalid: {\n      enumerable: true,\n      get: () => !!get(formState.errors, name)\n    },\n    isDirty: {\n      enumerable: true,\n      get: () => !!get(formState.dirtyFields, name)\n    },\n    isTouched: {\n      enumerable: true,\n      get: () => !!get(formState.touchedFields, name)\n    },\n    isValidating: {\n      enumerable: true,\n      get: () => !!get(formState.validatingFields, name)\n    },\n    error: {\n      enumerable: true,\n      get: () => get(formState.errors, name)\n    }\n  }), [formState, name]);\n  const onChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(event => _registerProps.current.onChange({\n    target: {\n      value: getEventValue(event),\n      name: name\n    },\n    type: EVENTS.CHANGE\n  }), [name]);\n  const onBlur = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => _registerProps.current.onBlur({\n    target: {\n      value: get(control._formValues, name),\n      name: name\n    },\n    type: EVENTS.BLUR\n  }), [name, control._formValues]);\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(elm => {\n    const field = get(control._fields, name);\n    if (field && elm) {\n      field._f.ref = {\n        focus: () => elm.focus(),\n        select: () => elm.select(),\n        setCustomValidity: message => elm.setCustomValidity(message),\n        reportValidity: () => elm.reportValidity()\n      };\n    }\n  }, [control._fields, name]);\n  const field = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => _objectSpread(_objectSpread({\n    name,\n    value\n  }, isBoolean(disabled) || formState.disabled ? {\n    disabled: formState.disabled || disabled\n  } : {}), {}, {\n    onChange,\n    onBlur,\n    ref\n  }), [name, disabled, formState.disabled, onChange, onBlur, ref, value]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n    control.register(name, _objectSpread(_objectSpread({}, _props.current.rules), isBoolean(_props.current.disabled) ? {\n      disabled: _props.current.disabled\n    } : {}));\n    const updateMounted = (name, value) => {\n      const field = get(control._fields, name);\n      if (field && field._f) {\n        field._f.mount = value;\n      }\n    };\n    updateMounted(name, true);\n    if (_shouldUnregisterField) {\n      const value = cloneObject(get(control._options.defaultValues, name));\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n    !isArrayField && control.register(name);\n    return () => {\n      (isArrayField ? _shouldUnregisterField && !control._state.action : _shouldUnregisterField) ? control.unregister(name) : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    control._setDisabledField({\n      disabled,\n      name\n    });\n  }, [disabled, name, control]);\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n    field,\n    formState,\n    fieldState\n  }), [field, formState, fieldState]);\n}\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = props => props.render(useController(props));\nconst flatten = obj => {\n  const output = {};\n  for (const key of Object.keys(obj)) {\n    if (isObjectType(obj[key]) && obj[key] !== null) {\n      const nested = flatten(obj[key]);\n      for (const nestedKey of Object.keys(nested)) {\n        output[`${key}.${nestedKey}`] = nested[nestedKey];\n      }\n    } else {\n      output[key] = obj[key];\n    }\n  }\n  return output;\n};\nconst POST_REQUEST = 'post';\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form(props) {\n  const methods = useFormContext();\n  const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n  const {\n      control = methods.control,\n      onSubmit,\n      children,\n      action,\n      method = POST_REQUEST,\n      headers,\n      encType,\n      onError,\n      render,\n      onSuccess,\n      validateStatus\n    } = props,\n    rest = _objectWithoutProperties(props, _excluded2);\n  const submit = async event => {\n    let hasError = false;\n    let type = '';\n    await control.handleSubmit(async data => {\n      const formData = new FormData();\n      let formDataJson = '';\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch (_a) {}\n      const flattenFormValues = flatten(control._formValues);\n      for (const key in flattenFormValues) {\n        formData.append(key, flattenFormValues[key]);\n      }\n      if (onSubmit) {\n        await onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson\n        });\n      }\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [headers && headers['Content-Type'], encType].some(value => value && value.includes('json'));\n          const response = await fetch(String(action), {\n            method,\n            headers: _objectSpread(_objectSpread({}, headers), encType ? {\n              'Content-Type': encType\n            } : {}),\n            body: shouldStringifySubmissionData ? formDataJson : formData\n          });\n          if (response && (validateStatus ? !validateStatus(response.status) : response.status < 200 || response.status >= 300)) {\n            hasError = true;\n            onError && onError({\n              response\n            });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({\n              response\n            });\n          }\n        } catch (error) {\n          hasError = true;\n          onError && onError({\n            error\n          });\n        }\n      }\n    })(event);\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false\n      });\n      props.control.setError('root.server', {\n        type\n      });\n    }\n  };\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    setMounted(true);\n  }, []);\n  return render ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render({\n    submit\n  })) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"form\", _objectSpread({\n    noValidate: mounted,\n    action: action,\n    method: method,\n    encType: encType,\n    onSubmit: submit\n  }, rest), children);\n}\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message) => validateAllFieldCriteria ? _objectSpread(_objectSpread({}, errors[name]), {}, {\n  types: _objectSpread(_objectSpread({}, errors[name] && errors[name].types ? errors[name].types : {}), {}, {\n    [type]: message || true\n  })\n}) : {};\nvar convertToArrayPayload = value => Array.isArray(value) ? value : [value];\nvar createSubject = () => {\n  let _observers = [];\n  const next = value => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n  const subscribe = observer => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter(o => o !== observer);\n      }\n    };\n  };\n  const unsubscribe = () => {\n    _observers = [];\n  };\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe\n  };\n};\nvar isPrimitive = value => isNullOrUndefined(value) || !isObjectType(value);\nfunction deepEqual(object1, object2) {\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n  for (const key of keys1) {\n    const val1 = object1[key];\n    if (!keys2.includes(key)) {\n      return false;\n    }\n    if (key !== 'ref') {\n      const val2 = object2[key];\n      if (isDateObject(val1) && isDateObject(val2) || isObject(val1) && isObject(val2) || Array.isArray(val1) && Array.isArray(val2) ? !deepEqual(val1, val2) : val1 !== val2) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\nvar isEmptyObject = value => isObject(value) && !Object.keys(value).length;\nvar isFileInput = element => element.type === 'file';\nvar isFunction = value => typeof value === 'function';\nvar isHTMLElement = value => {\n  if (!isWeb) {\n    return false;\n  }\n  const owner = value ? value.ownerDocument : 0;\n  return value instanceof (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement);\n};\nvar isMultipleSelect = element => element.type === `select-multiple`;\nvar isRadioInput = element => element.type === 'radio';\nvar isRadioOrCheckbox = ref => isRadioInput(ref) || isCheckBoxInput(ref);\nvar live = ref => isHTMLElement(ref) && ref.isConnected;\nfunction baseGet(object, updatePath) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n  return object;\n}\nfunction isEmptyArray(obj) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction unset(object, path) {\n  const paths = Array.isArray(path) ? path : isKey(path) ? [path] : stringToPath(path);\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n  const index = paths.length - 1;\n  const key = paths[index];\n  if (childObject) {\n    delete childObject[key];\n  }\n  if (index !== 0 && (isObject(childObject) && isEmptyObject(childObject) || Array.isArray(childObject) && isEmptyArray(childObject))) {\n    unset(object, paths.slice(0, -1));\n  }\n  return object;\n}\nvar objectHasFunction = data => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\nfunction markFieldsDirty(data, fields = {}) {\n  const isParentNodeArray = Array.isArray(data);\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n        fields[key] = Array.isArray(data[key]) ? [] : {};\n        markFieldsDirty(data[key], fields[key]);\n      } else if (!isNullOrUndefined(data[key])) {\n        fields[key] = true;\n      }\n    }\n  }\n  return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n  const isParentNodeArray = Array.isArray(data);\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n        if (isUndefined(formValues) || isPrimitive(dirtyFieldsFromValues[key])) {\n          dirtyFieldsFromValues[key] = Array.isArray(data[key]) ? markFieldsDirty(data[key], []) : _objectSpread({}, markFieldsDirty(data[key]));\n        } else {\n          getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n        }\n      } else {\n        dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n      }\n    }\n  }\n  return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues) => getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\nconst defaultResult = {\n  value: false,\n  isValid: false\n};\nconst validResult = {\n  value: true,\n  isValid: true\n};\nvar getCheckboxValue = options => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options.filter(option => option && option.checked && !option.disabled).map(option => option.value);\n      return {\n        value: values,\n        isValid: !!values.length\n      };\n    }\n    return options[0].checked && !options[0].disabled ?\n    // @ts-expect-error expected to work in the browser\n    options[0].attributes && !isUndefined(options[0].attributes.value) ? isUndefined(options[0].value) || options[0].value === '' ? validResult : {\n      value: options[0].value,\n      isValid: true\n    } : validResult : defaultResult;\n  }\n  return defaultResult;\n};\nvar getFieldValueAs = (value, {\n  valueAsNumber,\n  valueAsDate,\n  setValueAs\n}) => isUndefined(value) ? value : valueAsNumber ? value === '' ? NaN : value ? +value : value : valueAsDate && isString(value) ? new Date(value) : setValueAs ? setValueAs(value) : value;\nconst defaultReturn = {\n  isValid: false,\n  value: null\n};\nvar getRadioValue = options => Array.isArray(options) ? options.reduce((previous, option) => option && option.checked && !option.disabled ? {\n  isValid: true,\n  value: option.value\n} : previous, defaultReturn) : defaultReturn;\nfunction getFieldValue(_f) {\n  const ref = _f.ref;\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(({\n      value\n    }) => value);\n  }\n  if (isCheckBoxInput(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation) => {\n  const fields = {};\n  for (const name of fieldsNames) {\n    const field = get(_fields, name);\n    field && set(fields, name, field._f);\n  }\n  return {\n    criteriaMode,\n    names: [...fieldsNames],\n    fields,\n    shouldUseNativeValidation\n  };\n};\nvar isRegex = value => value instanceof RegExp;\nvar getRuleValue = rule => isUndefined(rule) ? rule : isRegex(rule) ? rule.source : isObject(rule) ? isRegex(rule.value) ? rule.value.source : rule.value : rule;\nvar getValidationModes = mode => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched\n});\nconst ASYNC_FUNCTION = 'AsyncFunction';\nvar hasPromiseValidation = fieldReference => !!fieldReference && !!fieldReference.validate && !!(isFunction(fieldReference.validate) && fieldReference.validate.constructor.name === ASYNC_FUNCTION || isObject(fieldReference.validate) && Object.values(fieldReference.validate).find(validateFunction => validateFunction.constructor.name === ASYNC_FUNCTION));\nvar hasValidation = options => options.mount && (options.required || options.min || options.max || options.maxLength || options.minLength || options.pattern || options.validate);\nvar isWatched = (name, _names, isBlurEvent) => !isBlurEvent && (_names.watchAll || _names.watch.has(name) || [..._names.watch].some(watchName => name.startsWith(watchName) && /^\\.\\w+/.test(name.slice(watchName.length))));\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n    if (field) {\n      const {\n          _f\n        } = field,\n        currentField = _objectWithoutProperties(field, _excluded3);\n      if (_f) {\n        if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n          return true;\n        } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n          return true;\n        } else {\n          if (iterateFieldsByAction(currentField, action)) {\n            break;\n          }\n        }\n      } else if (isObject(currentField)) {\n        if (iterateFieldsByAction(currentField, action)) {\n          break;\n        }\n      }\n    }\n  }\n  return;\n};\nfunction schemaErrorLookup(errors, _fields, name) {\n  const error = get(errors, name);\n  if (error || isKey(name)) {\n    return {\n      error,\n      name\n    };\n  }\n  const names = name.split('.');\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return {\n        name\n      };\n    }\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError\n      };\n    }\n    names.pop();\n  }\n  return {\n    name\n  };\n}\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot) => {\n  updateFormState(formStateData);\n  const {\n      name\n    } = formStateData,\n    formState = _objectWithoutProperties(formStateData, _excluded4);\n  return isEmptyObject(formState) || Object.keys(formState).length >= Object.keys(_proxyFormState).length || Object.keys(formState).find(key => _proxyFormState[key] === (!isRoot || VALIDATION_MODE.all));\n};\nvar shouldSubscribeByName = (name, signalName, exact) => !name || !signalName || name === signalName || convertToArrayPayload(name).some(currentName => currentName && (exact ? currentName === signalName : currentName.startsWith(signalName) || signalName.startsWith(currentName)));\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\nvar unsetEmptyArray = (ref, name) => !compact(get(ref, name)).length && unset(ref, name);\nvar updateFieldArrayRootError = (errors, error, name) => {\n  const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\nvar isMessage = value => isString(value);\nfunction getValidateError(result, ref, type = 'validate') {\n  if (isMessage(result) || Array.isArray(result) && result.every(isMessage) || isBoolean(result) && !result) {\n    return {\n      type,\n      message: isMessage(result) ? result : '',\n      ref\n    };\n  }\n}\nvar getValueAndMessage = validationData => isObject(validationData) && !isRegex(validationData) ? validationData : {\n  value: validationData,\n  message: ''\n};\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray) => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount\n  } = field._f;\n  const inputValue = get(formValues, name);\n  if (!mount || disabledFieldNames.has(name)) {\n    return {};\n  }\n  const inputRef = refs ? refs[0] : ref;\n  const setCustomValidity = message => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty = (valueAsNumber || isFileInput(ref)) && isUndefined(ref.value) && isUndefined(inputValue) || isHTMLElement(ref) && ref.value === '' || inputValue === '' || Array.isArray(inputValue) && !inputValue.length;\n  const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n  const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength) => {\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = _objectSpread({\n      type: exceedMax ? maxType : minType,\n      message,\n      ref\n    }, appendErrorsCurry(exceedMax ? maxType : minType, message));\n  };\n  if (isFieldArray ? !Array.isArray(inputValue) || !inputValue.length : required && (!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue)) || isBoolean(inputValue) && !inputValue || isCheckBox && !getCheckboxValue(refs).isValid || isRadio && !getRadioValue(refs).isValid)) {\n    const {\n      value,\n      message\n    } = isMessage(required) ? {\n      value: !!required,\n      message: required\n    } : getValueAndMessage(required);\n    if (value) {\n      error[name] = _objectSpread({\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef\n      }, appendErrorsCurry(INPUT_VALIDATION_RULES.required, message));\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n      const valueNumber = ref.valueAsNumber || (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate = ref.valueAsDate || new Date(inputValue);\n      const convertTimeToDate = time => new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value) : isWeek ? inputValue > maxOutput.value : valueDate > new Date(maxOutput.value);\n      }\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value) : isWeek ? inputValue < minOutput.value : valueDate < new Date(minOutput.value);\n      }\n    }\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name].message);\n        return error;\n      }\n    }\n  }\n  if ((maxLength || minLength) && !isEmpty && (isString(inputValue) || isFieldArray && Array.isArray(inputValue))) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax = !isNullOrUndefined(maxLengthOutput.value) && inputValue.length > +maxLengthOutput.value;\n    const exceedMin = !isNullOrUndefined(minLengthOutput.value) && inputValue.length < +minLengthOutput.value;\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name].message);\n        return error;\n      }\n    }\n  }\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const {\n      value: patternValue,\n      message\n    } = getValueAndMessage(pattern);\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = _objectSpread({\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref\n      }, appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message));\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n      if (validateError) {\n        error[name] = _objectSpread(_objectSpread({}, validateError), appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message));\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {};\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n        const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n        if (validateError) {\n          validationResult = _objectSpread(_objectSpread({}, validateError), appendErrorsCurry(key, validateError.message));\n          setCustomValidity(validateError.message);\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n      if (!isEmptyObject(validationResult)) {\n        error[name] = _objectSpread({\n          ref: inputRef\n        }, validationResult);\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n  setCustomValidity(true);\n  return error;\n};\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true\n};\nfunction createFormControl(props = {}) {\n  let _options = _objectSpread(_objectSpread({}, defaultOptions), props);\n  let _formState = {\n    submitCount: 0,\n    isDirty: false,\n    isReady: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    validatingFields: {},\n    errors: _options.errors || {},\n    disabled: _options.disabled || false\n  };\n  const _fields = {};\n  let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values) ? cloneObject(_options.defaultValues || _options.values) || {} : {};\n  let _formValues = _options.shouldUnregister ? {} : cloneObject(_defaultValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false\n  };\n  let _names = {\n    mount: new Set(),\n    disabled: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set()\n  };\n  let delayErrorCallback;\n  let timer = 0;\n  const _proxyFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    validatingFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false\n  };\n  let _proxySubscribeFormState = _objectSpread({}, _proxyFormState);\n  const _subjects = {\n    array: createSubject(),\n    state: createSubject()\n  };\n  const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n  const debounce = callback => wait => {\n    clearTimeout(timer);\n    timer = setTimeout(callback, wait);\n  };\n  const _setValid = async shouldUpdateValid => {\n    if (!_options.disabled && (_proxyFormState.isValid || _proxySubscribeFormState.isValid || shouldUpdateValid)) {\n      const isValid = _options.resolver ? isEmptyObject((await _runSchema()).errors) : await executeBuiltInValidation(_fields, true);\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid\n        });\n      }\n    }\n  };\n  const _updateIsValidating = (names, isValidating) => {\n    if (!_options.disabled && (_proxyFormState.isValidating || _proxyFormState.validatingFields || _proxySubscribeFormState.isValidating || _proxySubscribeFormState.validatingFields)) {\n      (names || Array.from(_names.mount)).forEach(name => {\n        if (name) {\n          isValidating ? set(_formState.validatingFields, name, isValidating) : unset(_formState.validatingFields, name);\n        }\n      });\n      _subjects.state.next({\n        validatingFields: _formState.validatingFields,\n        isValidating: !isEmptyObject(_formState.validatingFields)\n      });\n    }\n  };\n  const _setFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true) => {\n    if (args && method && !_options.disabled) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_formState.errors, name))) {\n        const errors = method(get(_formState.errors, name), args.argA, args.argB);\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n      if ((_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && shouldUpdateFieldsAndState && Array.isArray(get(_formState.touchedFields, name))) {\n        const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n      if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n  const updateErrors = (name, error) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors\n    });\n  };\n  const _setErrors = errors => {\n    _formState.errors = errors;\n    _subjects.state.next({\n      errors: _formState.errors,\n      isValid: false\n    });\n  };\n  const updateValidAndValue = (name, shouldSkipSetValueAs, value, ref) => {\n    const field = get(_fields, name);\n    if (field) {\n      const defaultValue = get(_formValues, name, isUndefined(value) ? get(_defaultValues, name) : value);\n      isUndefined(defaultValue) || ref && ref.defaultChecked || shouldSkipSetValueAs ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f)) : setFieldValue(name, defaultValue);\n      _state.mount && _setValid();\n    }\n  };\n  const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender) => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output = {\n      name\n    };\n    if (!_options.disabled) {\n      if (!isBlurEvent || shouldDirty) {\n        if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n          isPreviousDirty = _formState.isDirty;\n          _formState.isDirty = output.isDirty = _getDirty();\n          shouldUpdateField = isPreviousDirty !== output.isDirty;\n        }\n        const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);\n        isPreviousDirty = !!get(_formState.dirtyFields, name);\n        isCurrentFieldPristine ? unset(_formState.dirtyFields, name) : set(_formState.dirtyFields, name, true);\n        output.dirtyFields = _formState.dirtyFields;\n        shouldUpdateField = shouldUpdateField || (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) && isPreviousDirty !== !isCurrentFieldPristine;\n      }\n      if (isBlurEvent) {\n        const isPreviousFieldTouched = get(_formState.touchedFields, name);\n        if (!isPreviousFieldTouched) {\n          set(_formState.touchedFields, name, isBlurEvent);\n          output.touchedFields = _formState.touchedFields;\n          shouldUpdateField = shouldUpdateField || (_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && isPreviousFieldTouched !== isBlurEvent;\n        }\n      }\n      shouldUpdateField && shouldRender && _subjects.state.next(output);\n    }\n    return shouldUpdateField ? output : {};\n  };\n  const shouldRenderByError = (name, isValid, error, fieldState) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid = (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isBoolean(isValid) && _formState.isValid !== isValid;\n    if (_options.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(_options.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n    }\n    if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) || !isEmptyObject(fieldState) || shouldUpdateValid) {\n      const updatedFormState = _objectSpread(_objectSpread(_objectSpread({}, fieldState), shouldUpdateValid && isBoolean(isValid) ? {\n        isValid\n      } : {}), {}, {\n        errors: _formState.errors,\n        name\n      });\n      _formState = _objectSpread(_objectSpread({}, _formState), updatedFormState);\n      _subjects.state.next(updatedFormState);\n    }\n  };\n  const _runSchema = async name => {\n    _updateIsValidating(name, true);\n    const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n    _updateIsValidating(name);\n    return result;\n  };\n  const executeSchemaAndUpdateState = async names => {\n    const {\n      errors\n    } = await _runSchema(names);\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n    return errors;\n  };\n  const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n    valid: true\n  }) => {\n    for (const name in fields) {\n      const field = fields[name];\n      if (field) {\n        const {\n            _f\n          } = field,\n          fieldValue = _objectWithoutProperties(field, _excluded5);\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name], true);\n          }\n          const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name]);\n          }\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n          !shouldOnlyCheckValid && (get(fieldError, _f.name) ? isFieldArrayRoot ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name) : set(_formState.errors, _f.name, fieldError[_f.name]) : unset(_formState.errors, _f.name));\n        }\n        !isEmptyObject(fieldValue) && (await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context));\n      }\n    }\n    return context.valid;\n  };\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field = get(_fields, name);\n      field && (field._f.refs ? field._f.refs.every(ref => !live(ref)) : !live(field._f.ref)) && unregister(name);\n    }\n    _names.unMount = new Set();\n  };\n  const _getDirty = (name, data) => !_options.disabled && (name && data && set(_formValues, name, data), !deepEqual(getValues(), _defaultValues));\n  const _getWatch = (names, defaultValue, isGlobal) => generateWatchOutput(names, _names, _objectSpread({}, _state.mount ? _formValues : isUndefined(defaultValue) ? _defaultValues : isString(names) ? {\n    [names]: defaultValue\n  } : defaultValue), isGlobal, defaultValue);\n  const _getFieldArray = name => compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n  const setFieldValue = (name, value, options = {}) => {\n    const field = get(_fields, name);\n    let fieldValue = value;\n    if (field) {\n      const fieldReference = field._f;\n      if (fieldReference) {\n        !fieldReference.disabled && set(_formValues, name, getFieldValueAs(value, fieldReference));\n        fieldValue = isHTMLElement(fieldReference.ref) && isNullOrUndefined(value) ? '' : value;\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(optionRef => optionRef.selected = fieldValue.includes(optionRef.value));\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.forEach(checkboxRef => {\n              if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                if (Array.isArray(fieldValue)) {\n                  checkboxRef.checked = !!fieldValue.find(data => data === checkboxRef.value);\n                } else {\n                  checkboxRef.checked = fieldValue === checkboxRef.value || !!fieldValue;\n                }\n              }\n            });\n          } else {\n            fieldReference.refs.forEach(radioRef => radioRef.checked = radioRef.value === fieldValue);\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n          if (!fieldReference.ref.type) {\n            _subjects.state.next({\n              name,\n              values: cloneObject(_formValues)\n            });\n          }\n        }\n      }\n    }\n    (options.shouldDirty || options.shouldTouch) && updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n    options.shouldValidate && trigger(name);\n  };\n  const setValues = (name, value, options) => {\n    for (const fieldKey in value) {\n      if (!value.hasOwnProperty(fieldKey)) {\n        return;\n      }\n      const fieldValue = value[fieldKey];\n      const fieldName = `${name}.${fieldKey}`;\n      const field = get(_fields, fieldName);\n      (_names.array.has(name) || isObject(fieldValue) || field && !field._f) && !isDateObject(fieldValue) ? setValues(fieldName, fieldValue, options) : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n  const setValue = (name, value, options = {}) => {\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n    set(_formValues, name, cloneValue);\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: cloneObject(_formValues)\n      });\n      if ((_proxyFormState.isDirty || _proxyFormState.dirtyFields || _proxySubscribeFormState.isDirty || _proxySubscribeFormState.dirtyFields) && options.shouldDirty) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue)\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue) ? setValues(name, cloneValue, options) : setFieldValue(name, cloneValue, options);\n    }\n    isWatched(name, _names) && _subjects.state.next(_objectSpread({}, _formState));\n    _subjects.state.next({\n      name: _state.mount ? name : undefined,\n      values: cloneObject(_formValues)\n    });\n  };\n  const onChange = async event => {\n    _state.mount = true;\n    const target = event.target;\n    let name = target.name;\n    let isFieldValueUpdated = true;\n    const field = get(_fields, name);\n    const _updateIsFieldValueUpdated = fieldValue => {\n      isFieldValueUpdated = Number.isNaN(fieldValue) || isDateObject(fieldValue) && isNaN(fieldValue.getTime()) || deepEqual(fieldValue, get(_formValues, name, fieldValue));\n    };\n    const validationModeBeforeSubmit = getValidationModes(_options.mode);\n    const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = target.type ? getFieldValue(field._f) : getEventValue(event);\n      const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation = !hasValidation(field._f) && !_options.resolver && !get(_formState.errors, name) && !field._f.deps || skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n      const watched = isWatched(name, _names, isBlurEvent);\n      set(_formValues, name, fieldValue);\n      if (isBlurEvent) {\n        field._f.onBlur && field._f.onBlur(event);\n        delayErrorCallback && delayErrorCallback(0);\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n      const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n      !isBlurEvent && _subjects.state.next({\n        name,\n        type: event.type,\n        values: cloneObject(_formValues)\n      });\n      if (shouldSkipValidation) {\n        if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n          if (_options.mode === 'onBlur') {\n            if (isBlurEvent) {\n              _setValid();\n            }\n          } else if (!isBlurEvent) {\n            _setValid();\n          }\n        }\n        return shouldRender && _subjects.state.next(_objectSpread({\n          name\n        }, watched ? {} : fieldState));\n      }\n      !isBlurEvent && watched && _subjects.state.next(_objectSpread({}, _formState));\n      if (_options.resolver) {\n        const {\n          errors\n        } = await _runSchema([name]);\n        _updateIsFieldValueUpdated(fieldValue);\n        if (isFieldValueUpdated) {\n          const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n          const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n          error = errorLookupResult.error;\n          name = errorLookupResult.name;\n          isValid = isEmptyObject(errors);\n        }\n      } else {\n        _updateIsValidating([name], true);\n        error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n        _updateIsValidating([name]);\n        _updateIsFieldValueUpdated(fieldValue);\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n      if (isFieldValueUpdated) {\n        field._f.deps && trigger(field._f.deps);\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n  const _focusInput = (ref, key) => {\n    if (get(_formState.errors, key) && ref.focus) {\n      ref.focus();\n      return 1;\n    }\n    return;\n  };\n  const trigger = async (name, options = {}) => {\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name);\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n      isValid = isEmptyObject(errors);\n      validationResult = name ? !fieldNames.some(name => get(errors, name)) : isValid;\n    } else if (name) {\n      validationResult = (await Promise.all(fieldNames.map(async fieldName => {\n        const field = get(_fields, fieldName);\n        return await executeBuiltInValidation(field && field._f ? {\n          [fieldName]: field\n        } : field);\n      }))).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _setValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n    _subjects.state.next(_objectSpread(_objectSpread(_objectSpread({}, !isString(name) || (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isValid !== _formState.isValid ? {} : {\n      name\n    }), _options.resolver || !name ? {\n      isValid\n    } : {}), {}, {\n      errors: _formState.errors\n    }));\n    options.shouldFocus && !validationResult && iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n    return validationResult;\n  };\n  const getValues = fieldNames => {\n    const values = _objectSpread({}, _state.mount ? _formValues : _defaultValues);\n    return isUndefined(fieldNames) ? values : isString(fieldNames) ? get(values, fieldNames) : fieldNames.map(name => get(values, name));\n  };\n  const getFieldState = (name, formState) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    error: get((formState || _formState).errors, name),\n    isValidating: !!get(_formState.validatingFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name)\n  });\n  const clearErrors = name => {\n    name && convertToArrayPayload(name).forEach(inputName => unset(_formState.errors, inputName));\n    _subjects.state.next({\n      errors: name ? _formState.errors : {}\n    });\n  };\n  const setError = (name, error, options) => {\n    const ref = (get(_fields, name, {\n      _f: {}\n    })._f || {}).ref;\n    const currentError = get(_formState.errors, name) || {};\n    // Don't override existing error messages elsewhere in the object tree.\n    const {\n        ref: currentRef,\n        message,\n        type\n      } = currentError,\n      restOfErrorTree = _objectWithoutProperties(currentError, _excluded6);\n    set(_formState.errors, name, _objectSpread(_objectSpread(_objectSpread({}, restOfErrorTree), error), {}, {\n      ref\n    }));\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false\n    });\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n  const watch = (name, defaultValue) => isFunction(name) ? _subjects.state.subscribe({\n    next: payload => name(_getWatch(undefined, defaultValue), payload)\n  }) : _getWatch(name, defaultValue, true);\n  const _subscribe = props => _subjects.state.subscribe({\n    next: formState => {\n      if (shouldSubscribeByName(props.name, formState.name, props.exact) && shouldRenderFormState(formState, props.formState || _proxyFormState, _setFormState, props.reRenderRoot)) {\n        props.callback(_objectSpread(_objectSpread({\n          values: _objectSpread({}, _formValues)\n        }, _formState), formState));\n      }\n    }\n  }).unsubscribe;\n  const subscribe = props => {\n    _state.mount = true;\n    _proxySubscribeFormState = _objectSpread(_objectSpread({}, _proxySubscribeFormState), props.formState);\n    return _subscribe(_objectSpread(_objectSpread({}, props), {}, {\n      formState: _proxySubscribeFormState\n    }));\n  };\n  const unregister = (name, options = {}) => {\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !options.keepIsValidating && unset(_formState.validatingFields, fieldName);\n      !_options.shouldUnregister && !options.keepDefaultValue && unset(_defaultValues, fieldName);\n    }\n    _subjects.state.next({\n      values: cloneObject(_formValues)\n    });\n    _subjects.state.next(_objectSpread(_objectSpread({}, _formState), !options.keepDirty ? {} : {\n      isDirty: _getDirty()\n    }));\n    !options.keepIsValid && _setValid();\n  };\n  const _setDisabledField = ({\n    disabled,\n    name\n  }) => {\n    if (isBoolean(disabled) && _state.mount || !!disabled || _names.disabled.has(name)) {\n      disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n    }\n  };\n  const register = (name, options = {}) => {\n    let field = get(_fields, name);\n    const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n    set(_fields, name, _objectSpread(_objectSpread({}, field || {}), {}, {\n      _f: _objectSpread(_objectSpread({}, field && field._f ? field._f : {\n        ref: {\n          name\n        }\n      }), {}, {\n        name,\n        mount: true\n      }, options)\n    }));\n    _names.mount.add(name);\n    if (field) {\n      _setDisabledField({\n        disabled: isBoolean(options.disabled) ? options.disabled : _options.disabled,\n        name\n      });\n    } else {\n      updateValidAndValue(name, true, options.value);\n    }\n    return _objectSpread(_objectSpread(_objectSpread({}, disabledIsDefined ? {\n      disabled: options.disabled || _options.disabled\n    } : {}), _options.progressive ? {\n      required: !!options.required,\n      min: getRuleValue(options.min),\n      max: getRuleValue(options.max),\n      minLength: getRuleValue(options.minLength),\n      maxLength: getRuleValue(options.maxLength),\n      pattern: getRuleValue(options.pattern)\n    } : {}), {}, {\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: ref => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n          const fieldRef = isUndefined(ref.value) ? ref.querySelectorAll ? ref.querySelectorAll('input,select,textarea')[0] || ref : ref : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n          if (radioOrCheckbox ? refs.find(option => option === fieldRef) : fieldRef === field._f.ref) {\n            return;\n          }\n          set(_fields, name, {\n            _f: _objectSpread(_objectSpread({}, field._f), radioOrCheckbox ? {\n              refs: [...refs.filter(live), fieldRef, ...(Array.isArray(get(_defaultValues, name)) ? [{}] : [])],\n              ref: {\n                type: fieldRef.type,\n                name\n              }\n            } : {\n              ref: fieldRef\n            })\n          });\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n          if (field._f) {\n            field._f.mount = false;\n          }\n          (_options.shouldUnregister || options.shouldUnregister) && !(isNameInFieldArray(_names.array, name) && _state.action) && _names.unMount.add(name);\n        }\n      }\n    });\n  };\n  const _focusError = () => _options.shouldFocusError && iterateFieldsByAction(_fields, _focusInput, _names.mount);\n  const _disableForm = disabled => {\n    if (isBoolean(disabled)) {\n      _subjects.state.next({\n        disabled\n      });\n      iterateFieldsByAction(_fields, (ref, name) => {\n        const currentField = get(_fields, name);\n        if (currentField) {\n          ref.disabled = currentField._f.disabled || disabled;\n          if (Array.isArray(currentField._f.refs)) {\n            currentField._f.refs.forEach(inputRef => {\n              inputRef.disabled = currentField._f.disabled || disabled;\n            });\n          }\n        }\n      }, 0, false);\n    }\n  };\n  const handleSubmit = (onValid, onInvalid) => async e => {\n    let onValidError = undefined;\n    if (e) {\n      e.preventDefault && e.preventDefault();\n      e.persist && e.persist();\n    }\n    let fieldValues = cloneObject(_formValues);\n    _subjects.state.next({\n      isSubmitting: true\n    });\n    if (_options.resolver) {\n      const {\n        errors,\n        values\n      } = await _runSchema();\n      _formState.errors = errors;\n      fieldValues = values;\n    } else {\n      await executeBuiltInValidation(_fields);\n    }\n    if (_names.disabled.size) {\n      for (const name of _names.disabled) {\n        set(fieldValues, name, undefined);\n      }\n    }\n    unset(_formState.errors, 'root');\n    if (isEmptyObject(_formState.errors)) {\n      _subjects.state.next({\n        errors: {}\n      });\n      try {\n        await onValid(fieldValues, e);\n      } catch (error) {\n        onValidError = error;\n      }\n    } else {\n      if (onInvalid) {\n        await onInvalid(_objectSpread({}, _formState.errors), e);\n      }\n      _focusError();\n      setTimeout(_focusError);\n    }\n    _subjects.state.next({\n      isSubmitted: true,\n      isSubmitting: false,\n      isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n      submitCount: _formState.submitCount + 1,\n      errors: _formState.errors\n    });\n    if (onValidError) {\n      throw onValidError;\n    }\n  };\n  const resetField = (name, options = {}) => {\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, cloneObject(get(_defaultValues, name)));\n      } else {\n        setValue(name, options.defaultValue);\n        set(_defaultValues, name, cloneObject(options.defaultValue));\n      }\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue ? _getDirty(name, cloneObject(get(_defaultValues, name))) : _getDirty();\n      }\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _setValid();\n      }\n      _subjects.state.next(_objectSpread({}, _formState));\n    }\n  };\n  const _reset = (formValues, keepStateOptions = {}) => {\n    const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const isEmptyResetValues = isEmptyObject(formValues);\n    const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues) {\n        const fieldsToCheck = new Set([..._names.mount, ...Object.keys(getDirtyFields(_defaultValues, _formValues))]);\n        for (const fieldName of Array.from(fieldsToCheck)) {\n          get(_formState.dirtyFields, fieldName) ? set(values, fieldName, get(_formValues, fieldName)) : setValue(fieldName, get(values, fieldName));\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs) ? field._f.refs[0] : field._f.ref;\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n        for (const fieldName of _names.mount) {\n          setValue(fieldName, get(values, fieldName));\n        }\n      }\n      _formValues = cloneObject(values);\n      _subjects.array.next({\n        values: _objectSpread({}, values)\n      });\n      _subjects.state.next({\n        values: _objectSpread({}, values)\n      });\n    }\n    _names = {\n      mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      disabled: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: ''\n    };\n    _state.mount = !_proxyFormState.isValid || !!keepStateOptions.keepIsValid || !!keepStateOptions.keepDirtyValues;\n    _state.watch = !!_options.shouldUnregister;\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount ? _formState.submitCount : 0,\n      isDirty: isEmptyResetValues ? false : keepStateOptions.keepDirty ? _formState.isDirty : !!(keepStateOptions.keepDefaultValues && !deepEqual(formValues, _defaultValues)),\n      isSubmitted: keepStateOptions.keepIsSubmitted ? _formState.isSubmitted : false,\n      dirtyFields: isEmptyResetValues ? {} : keepStateOptions.keepDirtyValues ? keepStateOptions.keepDefaultValues && _formValues ? getDirtyFields(_defaultValues, _formValues) : _formState.dirtyFields : keepStateOptions.keepDefaultValues && formValues ? getDirtyFields(_defaultValues, formValues) : keepStateOptions.keepDirty ? _formState.dirtyFields : {},\n      touchedFields: keepStateOptions.keepTouched ? _formState.touchedFields : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful ? _formState.isSubmitSuccessful : false,\n      isSubmitting: false\n    });\n  };\n  const reset = (formValues, keepStateOptions) => _reset(isFunction(formValues) ? formValues(_formValues) : formValues, keepStateOptions);\n  const setFocus = (name, options = {}) => {\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs ? fieldReference.refs[0] : fieldReference.ref;\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect && isFunction(fieldRef.select) && fieldRef.select();\n      }\n    }\n  };\n  const _setFormState = updatedFormState => {\n    _formState = _objectSpread(_objectSpread({}, _formState), updatedFormState);\n  };\n  const _resetDefaultValues = () => isFunction(_options.defaultValues) && _options.defaultValues().then(values => {\n    reset(values, _options.resetOptions);\n    _subjects.state.next({\n      isLoading: false\n    });\n  });\n  const methods = {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _subscribe,\n      _runSchema,\n      _getWatch,\n      _getDirty,\n      _setValid,\n      _setFieldArray,\n      _setDisabledField,\n      _setErrors,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _removeUnmounted,\n      _disableForm,\n      _subjects,\n      _proxyFormState,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = _objectSpread(_objectSpread({}, _options), value);\n      }\n    },\n    subscribe,\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState\n  };\n  return _objectSpread(_objectSpread({}, methods), {}, {\n    formControl: methods\n  });\n}\nvar generateId = () => {\n  const d = typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n    return (c == 'x' ? r : r & 0x3 | 0x8).toString(16);\n  });\n};\nvar getFocusFieldName = (name, index, options = {}) => options.shouldFocus || isUndefined(options.shouldFocus) ? options.focusName || `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.` : '';\nvar appendAt = (data, value) => [...data, ...convertToArrayPayload(value)];\nvar fillEmptyArray = value => Array.isArray(value) ? value.map(() => undefined) : undefined;\nfunction insert(data, index, value) {\n  return [...data.slice(0, index), ...convertToArrayPayload(value), ...data.slice(index)];\n}\nvar moveArrayAt = (data, from, to) => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n  return data;\n};\nvar prependAt = (data, value) => [...convertToArrayPayload(value), ...convertToArrayPayload(data)];\nfunction removeAtIndexes(data, indexes) {\n  let i = 0;\n  const temp = [...data];\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n  return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index) => isUndefined(index) ? [] : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b) => a - b));\nvar swapArrayAt = (data, indexA, indexB) => {\n  [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\nvar updateAt = (fieldValues, index, value) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFieldArray(props) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister,\n    rules\n  } = props;\n  const [fields, setFields] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getFieldArray(name));\n  const ids = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control._getFieldArray(name).map(generateId));\n  const _fieldIds = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fields);\n  const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n  const _actioned = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  _name.current = name;\n  _fieldIds.current = fields;\n  control._names.array.add(name);\n  rules && control.register(name, rules);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => control._subjects.array.subscribe({\n    next: ({\n      values,\n      name: fieldArrayName\n    }) => {\n      if (fieldArrayName === _name.current || !fieldArrayName) {\n        const fieldValues = get(values, _name.current);\n        if (Array.isArray(fieldValues)) {\n          setFields(fieldValues);\n          ids.current = fieldValues.map(generateId);\n        }\n      }\n    }\n  }).unsubscribe, [control]);\n  const updateValues = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(updatedFieldArrayValues => {\n    _actioned.current = true;\n    control._setFieldArray(name, updatedFieldArrayValues);\n  }, [control, name]);\n  const append = (value, options) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n    control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n    ids.current = appendAt(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n      argA: fillEmptyArray(value)\n    });\n  };\n  const prepend = (value, options) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prependAt(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n      argA: fillEmptyArray(value)\n    });\n  };\n  const remove = index => {\n    const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    !Array.isArray(get(control._fields, name)) && set(control._fields, name, undefined);\n    control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index\n    });\n  };\n  const insert$1 = (index, value, options) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insert(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, insert, {\n      argA: index,\n      argB: fillEmptyArray(value)\n    });\n  };\n  const swap = (indexA, indexB) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n      argA: indexA,\n      argB: indexB\n    }, false);\n  };\n  const move = (from, to) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n      argA: from,\n      argB: to\n    }, false);\n  };\n  const update = (index, value) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n    ids.current = [...updatedFieldArrayValues].map((item, i) => !item || i === index ? generateId() : ids.current[i]);\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(name, updatedFieldArrayValues, updateAt, {\n      argA: index,\n      argB: updateValue\n    }, true, false);\n  };\n  const replace = value => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(name, [...updatedFieldArrayValues], data => data, {}, true, false);\n  };\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    control._state.action = false;\n    isWatched(name, control._names) && control._subjects.state.next(_objectSpread({}, control._formState));\n    if (_actioned.current && (!getValidationModes(control._options.mode).isOnSubmit || control._formState.isSubmitted) && !getValidationModes(control._options.reValidateMode).isOnSubmit) {\n      if (control._options.resolver) {\n        control._runSchema([name]).then(result => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n          if (existingError ? !error && existingError.type || error && (existingError.type !== error.type || existingError.message !== error.message) : error && error.type) {\n            error ? set(control._formState.errors, name, error) : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors\n            });\n          }\n        });\n      } else {\n        const field = get(control._fields, name);\n        if (field && field._f && !(getValidationModes(control._options.reValidateMode).isOnSubmit && getValidationModes(control._options.mode).isOnSubmit)) {\n          validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then(error => !isEmptyObject(error) && control._subjects.state.next({\n            errors: updateFieldArrayRootError(control._formState.errors, error, name)\n          }));\n        }\n      }\n    }\n    control._subjects.state.next({\n      name,\n      values: cloneObject(control._formValues)\n    });\n    control._names.focus && iterateFieldsByAction(control._fields, (ref, key) => {\n      if (control._names.focus && key.startsWith(control._names.focus) && ref.focus) {\n        ref.focus();\n        return 1;\n      }\n      return;\n    });\n    control._names.focus = '';\n    control._setValid();\n    _actioned.current = false;\n  }, [fields, name, control]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    !get(control._formValues, name) && control._setFieldArray(name);\n    return () => {\n      const updateMounted = (name, value) => {\n        const field = get(control._fields, name);\n        if (field && field._f) {\n          field._f.mount = value;\n        }\n      };\n      control._options.shouldUnregister || shouldUnregister ? control.unregister(name) : updateMounted(name, false);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n  return {\n    swap: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(swap, [updateValues, name, control]),\n    move: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(move, [updateValues, name, control]),\n    prepend: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(prepend, [updateValues, name, control]),\n    append: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(append, [updateValues, name, control]),\n    remove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(remove, [updateValues, name, control]),\n    insert: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(insert$1, [updateValues, name, control]),\n    update: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(update, [updateValues, name, control]),\n    replace: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(replace, [updateValues, name, control]),\n    fields: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => fields.map((field, index) => _objectSpread(_objectSpread({}, field), {}, {\n      [keyName]: ids.current[index] || generateId()\n    })), [fields, keyName])\n  };\n}\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useForm(props = {}) {\n  const _formControl = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n  const _values = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n  const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    validatingFields: {},\n    errors: props.errors || {},\n    disabled: props.disabled || false,\n    isReady: false,\n    defaultValues: isFunction(props.defaultValues) ? undefined : props.defaultValues\n  });\n  if (!_formControl.current) {\n    _formControl.current = _objectSpread(_objectSpread({}, props.formControl ? props.formControl : createFormControl(props)), {}, {\n      formState\n    });\n    if (props.formControl && props.defaultValues && !isFunction(props.defaultValues)) {\n      props.formControl.reset(props.defaultValues, props.resetOptions);\n    }\n  }\n  const control = _formControl.current.control;\n  control._options = props;\n  useIsomorphicLayoutEffect(() => {\n    const sub = control._subscribe({\n      formState: control._proxyFormState,\n      callback: () => updateFormState(_objectSpread({}, control._formState)),\n      reRenderRoot: true\n    });\n    updateFormState(data => _objectSpread(_objectSpread({}, data), {}, {\n      isReady: true\n    }));\n    control._formState.isReady = true;\n    return sub;\n  }, [control]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => control._disableForm(props.disabled), [control, props.disabled]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (props.mode) {\n      control._options.mode = props.mode;\n    }\n    if (props.reValidateMode) {\n      control._options.reValidateMode = props.reValidateMode;\n    }\n    if (props.errors && !isEmptyObject(props.errors)) {\n      control._setErrors(props.errors);\n    }\n  }, [control, props.errors, props.mode, props.reValidateMode]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    props.shouldUnregister && control._subjects.state.next({\n      values: control._getWatch()\n    });\n  }, [control, props.shouldUnregister]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (control._proxyFormState.isDirty) {\n      const isDirty = control._getDirty();\n      if (isDirty !== formState.isDirty) {\n        control._subjects.state.next({\n          isDirty\n        });\n      }\n    }\n  }, [control, formState.isDirty]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (props.values && !deepEqual(props.values, _values.current)) {\n      control._reset(props.values, control._options.resetOptions);\n      _values.current = props.values;\n      updateFormState(state => _objectSpread({}, state));\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [control, props.values]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (!control._state.mount) {\n      control._setValid();\n      control._state.mount = true;\n    }\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next(_objectSpread({}, control._formState));\n    }\n    control._removeUnmounted();\n  });\n  _formControl.current.formState = getProxyFormState(formState, control);\n  return _formControl.current;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\n");

/***/ })

};
;