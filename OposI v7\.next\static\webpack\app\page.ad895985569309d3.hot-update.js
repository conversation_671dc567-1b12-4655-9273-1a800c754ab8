"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/flashcards/FlashcardViewer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/flashcards/FlashcardViewer.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./FlashcardCollectionList */ \"(app-pages-browser)/./src/components/flashcards/FlashcardCollectionList.tsx\");\n/* harmony import */ var _FlashcardStatistics__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FlashcardStatistics */ \"(app-pages-browser)/./src/components/flashcards/FlashcardStatistics.tsx\");\n/* harmony import */ var _FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./FlashcardStudyMode */ \"(app-pages-browser)/./src/components/flashcards/FlashcardStudyMode.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst FlashcardViewer = ()=>{\n    _s();\n    // Estado para las colecciones\n    const [colecciones, setColecciones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coleccionSeleccionada, setColeccionSeleccionada] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para las flashcards\n    const [flashcards, setFlashcards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [mostrarRespuesta, setMostrarRespuesta] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [respondiendo, setRespondiendo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estado para el modo de estudio\n    const [modoEstudio, setModoEstudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estado para estadísticas\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para carga y errores\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Cargar colecciones al montar el componente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashcardViewer.useEffect\": ()=>{\n            const cargarColecciones = {\n                \"FlashcardViewer.useEffect.cargarColecciones\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n                        setColecciones(data);\n                    } catch (error) {\n                        console.error('Error al cargar colecciones:', error);\n                        setError('No se pudieron cargar las colecciones de flashcards');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"FlashcardViewer.useEffect.cargarColecciones\"];\n            cargarColecciones();\n        }\n    }[\"FlashcardViewer.useEffect\"], []);\n    // Manejar la selección de una colección\n    const handleSeleccionarColeccion = async (coleccion)=>{\n        setIsLoading(true);\n        setError('');\n        setColeccionSeleccionada(coleccion);\n        setActiveIndex(0);\n        setMostrarRespuesta(false);\n        setRespondiendo(false);\n        setModoEstudio(false);\n        try {\n            // Cargar flashcards con su progreso\n            const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccion.id);\n            // Ordenar las flashcards: primero las que deben estudiarse hoy, luego el resto\n            const ordenadas = [\n                ...data\n            ].sort((a, b)=>{\n                if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                return 0;\n            });\n            setFlashcards(ordenadas);\n            // Cargar estadísticas\n            const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccion.id);\n            setEstadisticas(stats);\n        } catch (error) {\n            console.error('Error al cargar flashcards:', error);\n            setError('No se pudieron cargar las flashcards de esta colección');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Iniciar el modo de estudio\n    const iniciarModoEstudio = async ()=>{\n        setIsLoading(true);\n        try {\n            if (coleccionSeleccionada) {\n                // Recargar las flashcards para asegurarnos de tener los datos más recientes\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                // Filtrar solo las flashcards que deben estudiarse hoy\n                const flashcardsParaEstudiar = data.filter((flashcard)=>flashcard.debeEstudiar);\n                // Actualizar estadísticas\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n                // Verificar si el número de flashcards para estudiar coincide con las estadísticas\n                if (flashcardsParaEstudiar.length !== stats.paraHoy) {\n                    console.warn(\"Discrepancia en el conteo: \".concat(flashcardsParaEstudiar.length, \" flashcards filtradas vs \").concat(stats.paraHoy, \" en estad\\xedsticas\"));\n                }\n                // Si no hay flashcards para hoy, mostrar un mensaje\n                if (flashcardsParaEstudiar.length === 0) {\n                    if (data.length === 0) {\n                        alert('No hay flashcards en esta colección.');\n                    } else {\n                        alert('No hay flashcards programadas para estudiar hoy. Vuelve mañana o ajusta el progreso de las tarjetas.');\n                    }\n                    return; // Salir sin iniciar el modo estudio\n                }\n                // Usar solo las flashcards programadas para hoy\n                setFlashcards(flashcardsParaEstudiar);\n                // Iniciar el modo de estudio\n                setModoEstudio(true);\n                setActiveIndex(0);\n                setMostrarRespuesta(false);\n                setRespondiendo(false);\n            }\n        } catch (error) {\n            console.error('Error al iniciar modo de estudio:', error);\n            setError('No se pudo iniciar el modo de estudio');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Manejar la navegación entre flashcards\n    const handleNavigate = (direction)=>{\n        if (direction === 'prev' && activeIndex > 0) {\n            setActiveIndex(activeIndex - 1);\n            setMostrarRespuesta(false);\n        } else if (direction === 'next' && activeIndex < flashcards.length - 1) {\n            setActiveIndex(activeIndex + 1);\n            setMostrarRespuesta(false);\n        }\n    };\n    // Manejar la respuesta a una flashcard\n    const handleRespuesta = async (dificultad)=>{\n        if (!coleccionSeleccionada || flashcards.length === 0) return;\n        const flashcardId = flashcards[activeIndex].id;\n        setRespondiendo(true);\n        try {\n            // Registrar la respuesta\n            await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.registrarRespuestaFlashcard)(flashcardId, dificultad);\n            // Recargar las flashcards y estadísticas si estamos en la última tarjeta\n            if (activeIndex >= flashcards.length - 1 && coleccionSeleccionada) {\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                const flashcardsParaEstudiar = data.filter((flashcard)=>flashcard.debeEstudiar);\n                // Actualizar estadísticas\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n                if (flashcardsParaEstudiar.length > 0) {\n                    setFlashcards(flashcardsParaEstudiar);\n                    setActiveIndex(0);\n                } else {\n                    // Si no hay más flashcards para hoy, mostrar mensaje y salir del modo de estudio\n                    alert('¡Has completado todas las flashcards para hoy! Vuelve mañana para continuar estudiando.');\n                    setModoEstudio(false);\n                    // Ordenar las flashcards: primero las que deben estudiarse (aunque ya no haya ninguna), luego el resto\n                    const ordenadas = [\n                        ...data\n                    ].sort((a, b)=>{\n                        if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                        if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                        return 0;\n                    });\n                    setFlashcards(ordenadas);\n                }\n            } else {\n                // Avanzar a la siguiente flashcard\n                handleNavigate('next');\n            }\n        } catch (error) {\n            console.error('Error al registrar respuesta:', error);\n            setError('No se pudo registrar la respuesta');\n        } finally{\n            setRespondiendo(false);\n            setMostrarRespuesta(false);\n        }\n    };\n    // Salir del modo de estudio\n    const handleSalirModoEstudio = ()=>{\n        setModoEstudio(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 209,\n                columnNumber: 9\n            }, undefined),\n            modoEstudio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                flashcards: flashcards,\n                activeIndex: activeIndex,\n                respondiendo: respondiendo,\n                onRespuesta: handleRespuesta,\n                onNavigate: handleNavigate,\n                onVolver: handleSalirModoEstudio\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 215,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"Mis Flashcards\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        colecciones: colecciones,\n                        coleccionSeleccionada: coleccionSeleccionada,\n                        onSeleccionarColeccion: handleSeleccionarColeccion,\n                        isLoading: isLoading\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, undefined),\n                    coleccionSeleccionada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: coleccionSeleccionada.titulo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardStatistics__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                estadisticas: estadisticas\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: iniciarModoEstudio,\n                                            className: \"bg-orange-500 hover:bg-orange-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                                            children: \"Estudiar (\".concat(estadisticas ? estadisticas.paraHoy : 0, \" para hoy)\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{},\n                                            className: \"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                                            children: \"Ver estad\\xedsticas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 15\n                            }, undefined),\n                            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center items-center h-40\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 17\n                            }, undefined) : flashcards.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"No hay flashcards en esta colecci\\xf3n.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: flashcards.map((flashcard, index)=>{\n                                    var _flashcard_progreso, _flashcard_progreso1;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4 \".concat(flashcard.debeEstudiar ? 'border-orange-300 bg-orange-50' : 'border-gray-200'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            \"Tarjeta \",\n                                                            index + 1\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    ((_flashcard_progreso = flashcard.progreso) === null || _flashcard_progreso === void 0 ? void 0 : _flashcard_progreso.estado) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs \".concat(flashcard.progreso.estado === 'nuevo' ? 'bg-blue-100 text-blue-800' : flashcard.progreso.estado === 'aprendiendo' ? 'bg-yellow-100 text-yellow-800' : flashcard.progreso.estado === 'repasando' ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'),\n                                                        children: flashcard.progreso.estado\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    !((_flashcard_progreso1 = flashcard.progreso) === null || _flashcard_progreso1 === void 0 ? void 0 : _flashcard_progreso1.estado) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs\",\n                                                        children: \"nuevo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-2\",\n                                                children: flashcard.pregunta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-700 line-clamp-2\",\n                                                children: flashcard.respuesta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, flashcard.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 21\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 224,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlashcardViewer, \"IuVhSLDfzx2htdQglnEz0ZBAS/E=\");\n_c = FlashcardViewer;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlashcardViewer);\nvar _c;\n$RefreshReg$(_c, \"FlashcardViewer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/flashcards/FlashcardViewer.tsx\n"));

/***/ })

});