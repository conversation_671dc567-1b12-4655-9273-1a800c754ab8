"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/flashcards/FlashcardViewer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/flashcards/FlashcardViewer.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _barrel_optimize_names_FiAlertTriangle_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlertTriangle,FiEdit2,FiRefreshCw,FiTrash2!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./FlashcardCollectionList */ \"(app-pages-browser)/./src/components/flashcards/FlashcardCollectionList.tsx\");\n/* harmony import */ var _FlashcardStatistics__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FlashcardStatistics */ \"(app-pages-browser)/./src/components/flashcards/FlashcardStatistics.tsx\");\n/* harmony import */ var _FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./FlashcardStudyMode */ \"(app-pages-browser)/./src/components/flashcards/FlashcardStudyMode.tsx\");\n/* harmony import */ var _FlashcardEditModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./FlashcardEditModal */ \"(app-pages-browser)/./src/components/flashcards/FlashcardEditModal.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst FlashcardViewer = ()=>{\n    _s();\n    // Estado para las colecciones\n    const [colecciones, setColecciones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coleccionSeleccionada, setColeccionSeleccionada] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para las flashcards\n    const [flashcards, setFlashcards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [mostrarRespuesta, setMostrarRespuesta] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [respondiendo, setRespondiendo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estado para el modo de estudio\n    const [modoEstudio, setModoEstudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estado para estadísticas\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para carga y errores\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Estado para edición y eliminación de flashcards\n    const [flashcardEditando, setFlashcardEditando] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDeleteConfirm, setShowDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para eliminación de colecciones\n    const [showDeleteCollectionConfirm, setShowDeleteCollectionConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletingCollectionId, setDeletingCollectionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Cargar colecciones al montar el componente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashcardViewer.useEffect\": ()=>{\n            const cargarColecciones = {\n                \"FlashcardViewer.useEffect.cargarColecciones\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n                        setColecciones(data);\n                    } catch (error) {\n                        console.error('Error al cargar colecciones:', error);\n                        setError('No se pudieron cargar las colecciones de flashcards');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"FlashcardViewer.useEffect.cargarColecciones\"];\n            cargarColecciones();\n        }\n    }[\"FlashcardViewer.useEffect\"], []);\n    // Manejar la selección de una colección\n    const handleSeleccionarColeccion = async (coleccion)=>{\n        setIsLoading(true);\n        setError('');\n        setColeccionSeleccionada(coleccion);\n        setActiveIndex(0);\n        setMostrarRespuesta(false);\n        setRespondiendo(false);\n        setModoEstudio(false);\n        try {\n            // Cargar flashcards con su progreso\n            const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccion.id);\n            // Ordenar las flashcards: primero las que deben estudiarse hoy, luego el resto\n            const ordenadas = [\n                ...data\n            ].sort((a, b)=>{\n                if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                return 0;\n            });\n            setFlashcards(ordenadas);\n            // Cargar estadísticas\n            const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccion.id);\n            setEstadisticas(stats);\n        } catch (error) {\n            console.error('Error al cargar flashcards:', error);\n            setError('No se pudieron cargar las flashcards de esta colección');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Iniciar el modo de estudio\n    const iniciarModoEstudio = async ()=>{\n        setIsLoading(true);\n        try {\n            if (coleccionSeleccionada) {\n                // Recargar las flashcards para asegurarnos de tener los datos más recientes\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                // Filtrar solo las flashcards que deben estudiarse hoy\n                const flashcardsParaEstudiar = data.filter((flashcard)=>flashcard.debeEstudiar);\n                // Actualizar estadísticas\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n                // Verificar si el número de flashcards para estudiar coincide con las estadísticas\n                if (flashcardsParaEstudiar.length !== stats.paraHoy) {\n                    console.warn(\"Discrepancia en el conteo: \".concat(flashcardsParaEstudiar.length, \" flashcards filtradas vs \").concat(stats.paraHoy, \" en estad\\xedsticas\"));\n                }\n                // Si no hay flashcards para hoy, mostrar un mensaje\n                if (flashcardsParaEstudiar.length === 0) {\n                    if (data.length === 0) {\n                        alert('No hay flashcards en esta colección.');\n                    } else {\n                        alert('No hay flashcards programadas para estudiar hoy. Vuelve mañana o ajusta el progreso de las tarjetas.');\n                    }\n                    return; // Salir sin iniciar el modo estudio\n                }\n                // Usar solo las flashcards programadas para hoy\n                setFlashcards(flashcardsParaEstudiar);\n                // Iniciar el modo de estudio\n                setModoEstudio(true);\n                setActiveIndex(0);\n                setMostrarRespuesta(false);\n                setRespondiendo(false);\n            }\n        } catch (error) {\n            console.error('Error al iniciar modo de estudio:', error);\n            setError('No se pudo iniciar el modo de estudio');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Manejar la navegación entre flashcards\n    const handleNavigate = (direction)=>{\n        if (direction === 'prev' && activeIndex > 0) {\n            setActiveIndex(activeIndex - 1);\n            setMostrarRespuesta(false);\n        } else if (direction === 'next' && activeIndex < flashcards.length - 1) {\n            setActiveIndex(activeIndex + 1);\n            setMostrarRespuesta(false);\n        }\n    };\n    // Manejar la respuesta a una flashcard\n    const handleRespuesta = async (dificultad)=>{\n        if (!coleccionSeleccionada || flashcards.length === 0) return;\n        const flashcardId = flashcards[activeIndex].id;\n        setRespondiendo(true);\n        try {\n            // Registrar la respuesta\n            await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.registrarRespuestaFlashcard)(flashcardId, dificultad);\n            // Recargar las flashcards y estadísticas si estamos en la última tarjeta\n            if (activeIndex >= flashcards.length - 1 && coleccionSeleccionada) {\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                const flashcardsParaEstudiar = data.filter((flashcard)=>flashcard.debeEstudiar);\n                // Actualizar estadísticas\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n                if (flashcardsParaEstudiar.length > 0) {\n                    setFlashcards(flashcardsParaEstudiar);\n                    setActiveIndex(0);\n                } else {\n                    // Si no hay más flashcards para hoy, mostrar mensaje y salir del modo de estudio\n                    alert('¡Has completado todas las flashcards para hoy! Vuelve mañana para continuar estudiando.');\n                    setModoEstudio(false);\n                    // Ordenar las flashcards: primero las que deben estudiarse (aunque ya no haya ninguna), luego el resto\n                    const ordenadas = [\n                        ...data\n                    ].sort((a, b)=>{\n                        if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                        if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                        return 0;\n                    });\n                    setFlashcards(ordenadas);\n                }\n            } else {\n                // Avanzar a la siguiente flashcard\n                handleNavigate('next');\n            }\n        } catch (error) {\n            console.error('Error al registrar respuesta:', error);\n            setError('No se pudo registrar la respuesta');\n        } finally{\n            setRespondiendo(false);\n            setMostrarRespuesta(false);\n        }\n    };\n    // Salir del modo de estudio\n    const handleSalirModoEstudio = ()=>{\n        setModoEstudio(false);\n    };\n    // Manejar la edición de una flashcard\n    const handleEditarFlashcard = (flashcard)=>{\n        setFlashcardEditando(flashcard);\n        setShowEditModal(true);\n    };\n    // Manejar el guardado de una flashcard editada\n    const handleGuardarFlashcard = (flashcardActualizada)=>{\n        // Actualizar la flashcard en la lista local\n        setFlashcards((prev)=>prev.map((fc)=>fc.id === flashcardActualizada.id ? flashcardActualizada : fc));\n    };\n    // Manejar la eliminación de una flashcard\n    const handleEliminarFlashcard = async (flashcardId)=>{\n        setDeletingId(flashcardId);\n        let loadingToastId;\n        try {\n            loadingToastId = react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].loading('Eliminando flashcard...');\n            const success = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.eliminarFlashcard)(flashcardId);\n            if (success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success('Flashcard eliminada exitosamente', {\n                    id: loadingToastId\n                });\n                // Actualizar la lista local\n                setFlashcards((prev)=>prev.filter((fc)=>fc.id !== flashcardId));\n                // Recargar estadísticas\n                if (coleccionSeleccionada) {\n                    const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                    setEstadisticas(stats);\n                }\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error('Error al eliminar la flashcard', {\n                    id: loadingToastId\n                });\n            }\n        } catch (error) {\n            console.error('Error al eliminar flashcard:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error('Error al eliminar la flashcard', {\n                id: loadingToastId\n            });\n        } finally{\n            setDeletingId(null);\n            setShowDeleteConfirm(null);\n        }\n    };\n    // Manejar la eliminación de una colección completa\n    const handleEliminarColeccion = (coleccionId)=>{\n        setShowDeleteCollectionConfirm(coleccionId);\n    };\n    const confirmarEliminarColeccion = async (coleccionId)=>{\n        setDeletingCollectionId(coleccionId);\n        let loadingToastId;\n        try {\n            loadingToastId = react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].loading('Eliminando colección...');\n            const success = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.eliminarColeccionFlashcards)(coleccionId);\n            if (success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success('Colección eliminada exitosamente', {\n                    id: loadingToastId\n                });\n                // Actualizar la lista local de colecciones\n                setColecciones((prev)=>prev.filter((col)=>col.id !== coleccionId));\n                // Si la colección eliminada era la seleccionada, limpiar la selección\n                if ((coleccionSeleccionada === null || coleccionSeleccionada === void 0 ? void 0 : coleccionSeleccionada.id) === coleccionId) {\n                    setColeccionSeleccionada(null);\n                    setFlashcards([]);\n                    setEstadisticas(null);\n                }\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error('Error al eliminar la colección', {\n                    id: loadingToastId\n                });\n            }\n        } catch (error) {\n            console.error('Error al eliminar colección:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error('Error al eliminar la colección', {\n                id: loadingToastId\n            });\n        } finally{\n            setDeletingCollectionId(null);\n            setShowDeleteCollectionConfirm(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 307,\n                columnNumber: 9\n            }, undefined),\n            modoEstudio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                flashcards: flashcards,\n                activeIndex: activeIndex,\n                respondiendo: respondiendo,\n                onRespuesta: handleRespuesta,\n                onNavigate: handleNavigate,\n                onVolver: handleSalirModoEstudio\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 313,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"Mis Flashcards\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        colecciones: colecciones,\n                        coleccionSeleccionada: coleccionSeleccionada,\n                        onSeleccionarColeccion: handleSeleccionarColeccion,\n                        onEliminarColeccion: handleEliminarColeccion,\n                        isLoading: isLoading,\n                        deletingId: deletingCollectionId\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 11\n                    }, undefined),\n                    coleccionSeleccionada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: coleccionSeleccionada.titulo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardStatistics__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                estadisticas: estadisticas\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: iniciarModoEstudio,\n                                            className: \"bg-orange-500 hover:bg-orange-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                                            children: \"Estudiar (\".concat(estadisticas ? estadisticas.paraHoy : 0, \" para hoy)\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{},\n                                            className: \"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                                            children: \"Ver estad\\xedsticas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, undefined),\n                            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center items-center h-40\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 17\n                            }, undefined) : flashcards.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"No hay flashcards en esta colecci\\xf3n.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: flashcards.map((flashcard, index)=>{\n                                    var _flashcard_progreso, _flashcard_progreso1;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4 \".concat(flashcard.debeEstudiar ? 'border-orange-300 bg-orange-50' : 'border-gray-200'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            \"Tarjeta \",\n                                                            index + 1\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            ((_flashcard_progreso = flashcard.progreso) === null || _flashcard_progreso === void 0 ? void 0 : _flashcard_progreso.estado) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full text-xs \".concat(flashcard.progreso.estado === 'nuevo' ? 'bg-blue-100 text-blue-800' : flashcard.progreso.estado === 'aprendiendo' ? 'bg-yellow-100 text-yellow-800' : flashcard.progreso.estado === 'repasando' ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'),\n                                                                children: flashcard.progreso.estado\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            !((_flashcard_progreso1 = flashcard.progreso) === null || _flashcard_progreso1 === void 0 ? void 0 : _flashcard_progreso1.estado) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs\",\n                                                                children: \"nuevo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleEditarFlashcard(flashcard),\n                                                                        className: \"p-1 text-blue-500 hover:bg-blue-50 rounded transition-colors\",\n                                                                        title: \"Editar flashcard\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiEdit2, {\n                                                                            size: 14\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                            lineNumber: 409,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                        lineNumber: 404,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setShowDeleteConfirm(flashcard.id),\n                                                                        disabled: deletingId === flashcard.id,\n                                                                        className: \"p-1 text-red-500 hover:bg-red-50 rounded transition-colors disabled:opacity-50\",\n                                                                        title: \"Eliminar flashcard\",\n                                                                        children: deletingId === flashcard.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiRefreshCw, {\n                                                                            size: 14,\n                                                                            className: \"animate-spin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                            lineNumber: 418,\n                                                                            columnNumber: 33\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiTrash2, {\n                                                                            size: 14\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                            lineNumber: 420,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                        lineNumber: 411,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-2\",\n                                                children: flashcard.pregunta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-700 line-clamp-2\",\n                                                children: flashcard.respuesta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, flashcard.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 21\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 322,\n                columnNumber: 9\n            }, undefined),\n            flashcardEditando && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardEditModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                flashcard: flashcardEditando,\n                isOpen: showEditModal,\n                onClose: ()=>{\n                    setShowEditModal(false);\n                    setFlashcardEditando(null);\n                },\n                onSave: handleGuardarFlashcard\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 439,\n                columnNumber: 9\n            }, undefined),\n            showDeleteConfirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 max-w-md w-full mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiAlertTriangle, {\n                                    className: \"text-red-500 mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"Confirmar eliminaci\\xf3n\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"\\xbfEst\\xe1s seguro de que quieres eliminar esta flashcard? Esta acci\\xf3n no se puede deshacer y se perder\\xe1 todo el progreso asociado.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                            lineNumber: 459,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowDeleteConfirm(null),\n                                    className: \"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\",\n                                    disabled: deletingId !== null,\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleEliminarFlashcard(showDeleteConfirm),\n                                    className: \"px-4 py-2 bg-red-500 text-white hover:bg-red-600 rounded-lg transition-colors\",\n                                    disabled: deletingId !== null,\n                                    children: \"Eliminar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                    lineNumber: 453,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 452,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n        lineNumber: 305,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlashcardViewer, \"+HTyYSOPk7h+QHp9CprRrCNOQms=\");\n_c = FlashcardViewer;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlashcardViewer);\nvar _c;\n$RefreshReg$(_c, \"FlashcardViewer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/flashcards/FlashcardViewer.tsx\n"));

/***/ })

});