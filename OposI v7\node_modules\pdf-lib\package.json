{"name": "pdf-lib", "version": "1.17.1", "description": "Create and modify PDF files with JavaScript", "author": "<PERSON> <<EMAIL>>", "contributors": ["jerp (https://github.com/jerp)", "<PERSON> (https://github.com/gregbacchus)", "<PERSON><PERSON> (https://github.com/mlecoq)", "<PERSON> (https://github.com/philipjmurphy)", "<PERSON> (https://github.com/PlushBeaver)", "<PERSON> (https://github.com/samezyane)", "<PERSON> (https://github.com/multiplegeorges)", "<PERSON> (https://github.com/Gerard<PERSON>mit)", "jlmessenger (https://github.com/jlmessenger)", "thebenlamm (https://github.com/thebenlamm)", "cshenks (https://github.com/cshenks)", "<PERSON> (https://github.com/jwoodrow)", "<PERSON> (https://github.com/Mogztter)", "<PERSON> (https://github.com/tessi)", "<PERSON> (https://github.com/tim<PERSON>)", "<PERSON> (https://github.com/taxilian)", "<PERSON> (https://github.com/sebastinez)", "soadzoor (https://github.com/soadzoor)", "Slobodan Babic (https://github.com/bockoblur)", "<PERSON> (https://github.com/ztoben)", "<PERSON> (https://github.com/zackdotcomputer)", "DkDavid (https://github.com/DkDavid)", "<PERSON><PERSON> <PERSON><PERSON> (https://github.com/btecu)", "<PERSON> (https://github.com/mcshaz)", "<PERSON> (https://github.com/duffyd)", "<PERSON> (https://github.com/ChingChang9)"], "scripts": {"release:latest": "yarn publish --tag latest && yarn pack && yarn release:tag", "release:next": "yarn publish --tag next", "release:prep": "yarn clean && yarn lint && yarn typecheck && yarn test && yarn build", "release:tag": "TAG=\"v$(yarn --silent get:version)\" && git tag $TAG && git push origin $TAG", "get:version": "node --eval 'console.log(require(`./package.json`).version)'", "clean": "rimraf ts3.4 build cjs dist es scratchpad/build coverage tsBuildInfo.json apps/node-build apps/node/tsBuildInfo.json isolate*.log flamegraph.html out.pdf", "typecheck": "tsc --noEmit --incremental false --tsBuildInfoFile null", "test": "jest --config jest.json --runInBand", "testw": "jest --config jest.json --watch", "testc": "jest --config jest.json --coverage && open coverage/index.html", "lint": "yarn lint:prettier && yarn lint:tslint:src && yarn lint:tslint:tests", "lint:tslint:src": "tslint --project tsconfig.json --fix", "lint:tslint:tests": "tslint --project tests/tsconfig.json --fix", "lint:prettier": "prettier --write \"./{src,tests,apps}/**/*.{ts,js,json,html,css}\" --loglevel error", "build": "yarn build:cjs && yarn build:es && yarn build:esm && yarn build:esm:min && yarn build:umd && yarn build:umd:min && yarn build:downlevel-dts", "build:cjs": "ttsc --module commonjs --outDir cjs", "build:es": "ttsc --module ES2015 --outDir es", "build:esm": "rollup --config rollup.config.js --file dist/pdf-lib.esm.js --environment MODULE_TYPE:es", "build:esm:min": "rollup --config rollup.config.js --file dist/pdf-lib.esm.min.js --environment MINIFY,MODULE_TYPE:es", "build:umd": "rollup --config rollup.config.js --file dist/pdf-lib.js --environment MODULE_TYPE:umd", "build:umd:min": "rollup --config rollup.config.js --file dist/pdf-lib.min.js --environment MINIFY,MODULE_TYPE:umd", "build:downlevel-dts": "rimraf ts3.4 && yarn downlevel-dts . ts3.4 && rimraf ts3.4/scratchpad", "scratchpad:start": "ttsc --build scratchpad/tsconfig.json --watch", "scratchpad:run": "node scratchpad/build/scratchpad/index.js", "scratchpad:flame": "rimraf isolate*.log && node --prof scratchpad/build/scratchpad/index.js && node --prof-process --preprocess -j isolate*.log | flamebearer", "apps:node": "ttsc --build apps/node/tsconfig.json && node apps/node-build/index.js", "apps:deno": "deno run --allow-read --allow-write --allow-run apps/deno/index.ts", "apps:web": "http-server -c-1 .", "apps:web:mac": "bash -c 'sleep 1 && open http://localhost:8080/apps/web/test1.html' & yarn apps:web", "apps:rn:ios": "cd apps/rn && yarn add ./../.. --force && react-native run-ios", "apps:rn:android": "yarn apps:rn:emulator & cd apps/rn && yarn add ./../.. --force && react-native run-android", "apps:rn:emulator": "emulator -avd \"$(emulator -list-avds | head -n 1)\" & bash -c 'sleep 5 && adb reverse tcp:8080 tcp:8080 && adb reverse tcp:8081 tcp:8081'"}, "main": "cjs/index.js", "module": "es/index.js", "unpkg": "dist/pdf-lib.min.js", "types": "cjs/index.d.ts", "typesVersions": {"<=3.5": {"*": ["ts3.4/*"]}}, "files": ["cjs/", "dist/", "es/", "src/", "ts3.4", "LICENSE.md", "package.json", "README.md", "yarn.lock"], "dependencies": {"@pdf-lib/standard-fonts": "^1.0.0", "@pdf-lib/upng": "^1.0.1", "pako": "^1.0.11", "tslib": "^1.11.1"}, "devDependencies": {"@pdf-lib/fontkit": "^1.1.0", "@rollup/plugin-commonjs": "^13.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^8.0.1", "@types/jest": "^26.0.0", "@types/node-fetch": "^2.5.7", "@types/pako": "^1.0.1", "@zerollup/ts-transform-paths": "^1.7.18", "downlevel-dts": "^0.5.0", "flamebearer": "^1.1.3", "http-server": "^0.12.3", "jest": "^26.0.1", "node-fetch": "^2.6.0", "prettier": "^2.0.5", "rimraf": "^3.0.2", "rollup": "^2.17.1", "rollup-plugin-terser": "^6.1.0", "ts-jest": "^26.1.0", "tslint": "^6.1.2", "tslint-config-prettier": "^1.18.0", "ttypescript": "^1.5.10", "typescript": "^3.9.5"}, "license": "MIT", "private": false, "homepage": "https://pdf-lib.js.org", "repository": "git+https://github.com/Hopding/pdf-lib.git", "bugs": {"url": "https://github.com/Hopding/pdf-lib/issues"}, "keywords": ["pdf-lib", "pdf", "document", "create", "modify", "creation", "modification", "edit", "editing", "typescript", "javascript", "library"]}