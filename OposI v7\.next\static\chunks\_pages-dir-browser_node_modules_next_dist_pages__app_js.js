"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_node_modules_next_dist_pages__app_js"],{

/***/ "(pages-dir-browser)/./node_modules/next/dist/pages/_app.js":
/*!**********************************************!*\
  !*** ./node_modules/next/dist/pages/_app.js ***!
  \**********************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n\nvar _regeneratorRuntime = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(pages-dir-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\nvar _defineProperty = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/defineProperty.js */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/defineProperty.js\");\nvar _classCallCheck = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/classCallCheck.js */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/classCallCheck.js\");\nvar _createClass = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/createClass.js */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/createClass.js\");\nvar _inherits = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/inherits.js */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/inherits.js\");\nvar _possibleConstructorReturn = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/possibleConstructorReturn.js */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/possibleConstructorReturn.js\");\nvar _getPrototypeOf = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/getPrototypeOf.js */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/getPrototypeOf.js\");\nvar _asyncToGenerator = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/asyncToGenerator.js */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/asyncToGenerator.js\");\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n  enumerable: true,\n  get: function get() {\n    return App;\n  }\n}));\nvar _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nvar _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nvar _react = /*#__PURE__*/_interop_require_default._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nvar _utils = __webpack_require__(/*! ../shared/lib/utils */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils.js\");\n/**\n * `App` component is used for initialize of pages. It allows for overwriting and full control of the `page` initialization.\n * This allows for keeping state between navigation, custom error handling, injecting additional data.\n */\nfunction appGetInitialProps(_x) {\n  return _appGetInitialProps.apply(this, arguments);\n}\nfunction _appGetInitialProps() {\n  _appGetInitialProps = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee(param) {\n    var Component, ctx, pageProps;\n    return _regeneratorRuntime.wrap(function _callee$(_context) {\n      while (1) switch (_context.prev = _context.next) {\n        case 0:\n          Component = param.Component, ctx = param.ctx;\n          _context.next = 3;\n          return (0, _utils.loadGetInitialProps)(Component, ctx);\n        case 3:\n          pageProps = _context.sent;\n          return _context.abrupt(\"return\", {\n            pageProps: pageProps\n          });\n        case 5:\n        case \"end\":\n          return _context.stop();\n      }\n    }, _callee);\n  }));\n  return _appGetInitialProps.apply(this, arguments);\n}\nvar App = /*#__PURE__*/function (_react$default$Compon) {\n  _inherits(App, _react$default$Compon);\n  var _super = _createSuper(App);\n  function App() {\n    _classCallCheck(this, App);\n    return _super.apply(this, arguments);\n  }\n  _createClass(App, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        Component = _this$props.Component,\n        pageProps = _this$props.pageProps;\n      return /*#__PURE__*/(0, _jsxruntime.jsx)(Component, _objectSpread({}, pageProps));\n    }\n  }]);\n  return App;\n}(_react[\"default\"].Component);\nApp.origGetInitialProps = appGetInitialProps;\nApp.getInitialProps = appGetInitialProps;\nif ((typeof exports[\"default\"] === 'function' || typeof exports[\"default\"] === 'object' && exports[\"default\"] !== null) && typeof exports[\"default\"].__esModule === 'undefined') {\n  Object.defineProperty(exports[\"default\"], '__esModule', {\n    value: true\n  });\n  Object.assign(exports[\"default\"], exports);\n  module.exports = exports[\"default\"];\n}\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/pages/_app.js\n"));

/***/ })

}]);