"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/QuestionForm.tsx":
/*!*****************************************!*\
  !*** ./src/components/QuestionForm.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuestionForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _ConversationHistory__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ConversationHistory */ \"(app-pages-browser)/./src/components/ConversationHistory.tsx\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _lib_formSchemas__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/formSchemas */ \"(app-pages-browser)/./src/lib/formSchemas.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction QuestionForm(param) {\n    let { documentosSeleccionados } = param;\n    _s();\n    const [mensajes, setMensajes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [conversacionActualId, setConversacionActualId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mostrarHistorial, setMostrarHistorial] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [guardandoConversacion, setGuardandoConversacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cargandoConversacionActiva, setCargandoConversacionActiva] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { register, handleSubmit: handleSubmitForm, formState: { errors, isValid }, reset, setValue, watch } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(_lib_formSchemas__WEBPACK_IMPORTED_MODULE_5__.preguntaFormSchema),\n        defaultValues: {\n            pregunta: '',\n            documentos: documentosSeleccionados\n        }\n    });\n    // Observar cambios en el formulario para debugging\n    const watchedValues = watch();\n    // Log de debugging para el estado del formulario\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionForm.useEffect\": ()=>{\n            console.log('📝 Estado del formulario:', {\n                values: watchedValues,\n                errors,\n                isValid,\n                documentosSeleccionados: documentosSeleccionados.length\n            });\n        }\n    }[\"QuestionForm.useEffect\"], [\n        watchedValues,\n        errors,\n        isValid,\n        documentosSeleccionados\n    ]);\n    // Sincronizar documentos seleccionados con el formulario, asegurando tipos correctos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionForm.useEffect\": ()=>{\n            console.log('🔄 Sincronizando documentos con el formulario:', documentosSeleccionados);\n            const documentosValidados = documentosSeleccionados.map({\n                \"QuestionForm.useEffect.documentosValidados\": (doc)=>{\n                    const docValidado = {\n                        ...doc,\n                        categoria: doc.categoria || null,\n                        numero_tema: doc.numero_tema !== undefined && doc.numero_tema !== null ? typeof doc.numero_tema === 'string' ? parseInt(doc.numero_tema, 10) : doc.numero_tema : undefined,\n                        // Asegurar que todos los campos opcionales estén presentes\n                        id: doc.id || undefined,\n                        creado_en: doc.creado_en || undefined,\n                        actualizado_en: doc.actualizado_en || undefined,\n                        user_id: doc.user_id || undefined,\n                        tipo_original: doc.tipo_original || undefined\n                    };\n                    console.log('📄 Documento validado:', docValidado);\n                    return docValidado;\n                }\n            }[\"QuestionForm.useEffect.documentosValidados\"]);\n            setValue('documentos', documentosValidados);\n            console.log('✅ Documentos sincronizados con el formulario');\n        }\n    }[\"QuestionForm.useEffect\"], [\n        documentosSeleccionados,\n        setValue\n    ]);\n    // Efecto para cargar la conversación activa al iniciar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionForm.useEffect\": ()=>{\n            const cargarConversacionActiva = {\n                \"QuestionForm.useEffect.cargarConversacionActiva\": async ()=>{\n                    setCargandoConversacionActiva(true);\n                    try {\n                        const conversacionActiva = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerConversacionActiva)();\n                        if (conversacionActiva) {\n                            console.log('Conversación activa encontrada:', conversacionActiva.id);\n                            setConversacionActualId(conversacionActiva.id);\n                            await cargarConversacion(conversacionActiva.id);\n                        } else {\n                            console.log('No hay conversación activa - esto es normal para usuarios nuevos');\n                            setMensajes([]);\n                            setConversacionActualId(null);\n                        }\n                    } catch (error) {\n                        console.warn('No se pudo cargar la conversación activa (esto es normal para usuarios nuevos):', error);\n                        // No mostrar error al usuario, simplemente inicializar sin conversación\n                        setMensajes([]);\n                        setConversacionActualId(null);\n                    } finally{\n                        setCargandoConversacionActiva(false);\n                    }\n                }\n            }[\"QuestionForm.useEffect.cargarConversacionActiva\"];\n            cargarConversacionActiva();\n        }\n    }[\"QuestionForm.useEffect\"], []);\n    // Efecto para hacer scroll al último mensaje cuando se añade uno nuevo\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionForm.useEffect\": ()=>{\n            if (chatContainerRef.current) {\n                chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n            }\n        }\n    }[\"QuestionForm.useEffect\"], [\n        mensajes\n    ]);\n    // Función para cargar una conversación desde Supabase\n    const cargarConversacion = async (conversacionId)=>{\n        try {\n            setIsLoading(true);\n            // Activar la conversación seleccionada\n            await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.activarConversacion)(conversacionId);\n            // Obtener los mensajes de la conversación\n            const mensajesDB = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerMensajesPorConversacionId)(conversacionId);\n            // Convertir los mensajes de la base de datos al formato local\n            const mensajesFormateados = mensajesDB.map((msg)=>({\n                    id: msg.id,\n                    tipo: msg.tipo,\n                    contenido: msg.contenido,\n                    timestamp: new Date(msg.timestamp)\n                }));\n            // Actualizar el estado\n            setMensajes(mensajesFormateados);\n            setConversacionActualId(conversacionId);\n            setError('');\n            console.log(\"Conversaci\\xf3n \".concat(conversacionId, \" cargada y activada\"));\n        } catch (error) {\n            console.error('Error al cargar la conversación:', error);\n            setError('No se pudo cargar la conversación');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Función para guardar un mensaje en Supabase\n    const guardarMensajeEnDB = async (mensaje)=>{\n        try {\n            setGuardandoConversacion(true);\n            // Si no hay una conversación actual, crear una nueva\n            if (!conversacionActualId) {\n                // Solo crear una nueva conversación si es el primer mensaje del usuario\n                if (mensaje.tipo === 'usuario') {\n                    // Crear un título basado en la primera pregunta\n                    const titulo = \"Conversaci\\xf3n: \".concat(mensaje.contenido.substring(0, 50)).concat(mensaje.contenido.length > 50 ? '...' : '');\n                    // Crear una nueva conversación y marcarla como activa\n                    const nuevoId = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.crearConversacion)(titulo, true);\n                    if (!nuevoId) {\n                        throw new Error('No se pudo crear la conversación');\n                    }\n                    console.log(\"Nueva conversaci\\xf3n creada y activada: \".concat(nuevoId));\n                    // Guardar el ID de la conversación para futuros mensajes\n                    setConversacionActualId(nuevoId);\n                    // Guardar el mensaje en la nueva conversación\n                    await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.guardarMensaje)({\n                        conversacion_id: nuevoId,\n                        tipo: mensaje.tipo,\n                        contenido: mensaje.contenido\n                    });\n                } else {\n                    // Si es un mensaje de la IA pero no hay conversación actual,\n                    // algo salió mal. Intentar recuperar creando una nueva conversación.\n                    console.warn('No hay conversación actual para guardar el mensaje de la IA. Creando una nueva.');\n                    const titulo = 'Nueva conversación';\n                    const nuevoId = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.crearConversacion)(titulo, true);\n                    if (!nuevoId) {\n                        throw new Error('No se pudo crear la conversación');\n                    }\n                    console.log(\"Nueva conversaci\\xf3n de recuperaci\\xf3n creada: \".concat(nuevoId));\n                    setConversacionActualId(nuevoId);\n                    // Guardar el mensaje en la nueva conversación\n                    await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.guardarMensaje)({\n                        conversacion_id: nuevoId,\n                        tipo: mensaje.tipo,\n                        contenido: mensaje.contenido\n                    });\n                }\n            } else {\n                // Verificar que la conversación actual sigue siendo la activa\n                const conversacionActiva = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerConversacionActiva)();\n                if (!conversacionActiva || conversacionActiva.id !== conversacionActualId) {\n                    // Si la conversación actual no es la activa, activarla\n                    console.log(\"Reactivando conversaci\\xf3n: \".concat(conversacionActualId));\n                    await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.activarConversacion)(conversacionActualId);\n                }\n                // Guardar el mensaje en la conversación existente\n                await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.guardarMensaje)({\n                    conversacion_id: conversacionActualId,\n                    tipo: mensaje.tipo,\n                    contenido: mensaje.contenido\n                });\n            }\n        } catch (error) {\n            console.error('Error al guardar el mensaje:', error);\n        // No mostramos error al usuario para no interrumpir la experiencia\n        } finally{\n            setGuardandoConversacion(false);\n        }\n    };\n    // Función para iniciar una nueva conversación\n    const iniciarNuevaConversacion = async ()=>{\n        try {\n            setIsLoading(true);\n            // Desactivar todas las conversaciones en la base de datos\n            await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.desactivarTodasLasConversaciones)();\n            // Limpiar los mensajes actuales\n            setMensajes([]);\n            // Establecer el ID de conversación a null para que se cree una nueva en el próximo mensaje\n            setConversacionActualId(null);\n            setError('');\n            console.log('Nueva conversación iniciada. El próximo mensaje creará una nueva conversación en la base de datos.');\n        } catch (error) {\n            console.error('Error al iniciar nueva conversación:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Cambia handleSubmit para usar React Hook Form\n    const onSubmit = async (data)=>{\n        console.log('🎉 ¡FUNCIÓN onSubmit EJECUTADA!');\n        console.log('🚀 Formulario enviado con datos:', data);\n        console.log('📄 Documentos seleccionados:', documentosSeleccionados);\n        console.log('✅ Validación pasada correctamente');\n        setIsLoading(true);\n        setError('');\n        // Añadir la pregunta del usuario al historial\n        const preguntaUsuario = {\n            tipo: 'usuario',\n            contenido: data.pregunta,\n            timestamp: new Date()\n        };\n        setMensajes((prevMensajes)=>[\n                ...prevMensajes,\n                preguntaUsuario\n            ]);\n        setIsLoading(true);\n        setError('');\n        // Limpiar el campo de pregunta después de enviarla\n        reset({\n            pregunta: '',\n            documentos: documentosSeleccionados\n        });\n        try {\n            // Guardar la pregunta del usuario en Supabase\n            await guardarMensajeEnDB(preguntaUsuario);\n            // Pasar los documentos completos a la función obtenerRespuestaIA\n            // No solo el contenido, sino también el título, categoría y número de tema\n            // Obtener respuesta de la IA\n            const response = await fetch('/api/gemini', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    pregunta: preguntaUsuario.contenido,\n                    documentos: data.documentos\n                })\n            });\n            const respuestaIA = await response.json();\n            let respuestaTexto = '';\n            if (respuestaIA.result) {\n                respuestaTexto = typeof respuestaIA.result === 'string' ? respuestaIA.result : JSON.stringify(respuestaIA.result);\n            } else if (respuestaIA.error) {\n                respuestaTexto = typeof respuestaIA.error === 'string' ? respuestaIA.error : JSON.stringify(respuestaIA.error);\n            } else {\n                respuestaTexto = 'Error desconocido al obtener respuesta de la IA.';\n            }\n            // Añadir la respuesta de la IA al historial\n            const mensajeIA = {\n                tipo: 'ia',\n                contenido: respuestaTexto,\n                timestamp: new Date()\n            };\n            setMensajes((prevMensajes)=>[\n                    ...prevMensajes,\n                    mensajeIA\n                ]);\n            // Guardar la respuesta de la IA en Supabase\n            await guardarMensajeEnDB(mensajeIA);\n            // Si es la primera pregunta, actualizar el título de la conversación con un título más descriptivo\n            if (mensajes.length === 0 && conversacionActualId) {\n                const tituloMejorado = \"Conversaci\\xf3n: \".concat(data.pregunta.substring(0, 50)).concat(data.pregunta.length > 50 ? '...' : '');\n                await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.actualizarConversacion)(conversacionActualId, tituloMejorado);\n            }\n        } catch (error) {\n            console.error('Error al obtener respuesta:', error);\n            // Determinar el tipo de error y mostrar un mensaje más específico\n            let mensajeError = 'Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, inténtalo de nuevo.';\n            if (error instanceof Error) {\n                if (error.message.includes('API key')) {\n                    mensajeError = 'Error de configuración: La clave de API de Gemini no está configurada correctamente.';\n                } else if (error.message.includes('network') || error.message.includes('fetch')) {\n                    mensajeError = 'Error de conexión: No se pudo conectar con el servicio de IA. Verifica tu conexión a internet.';\n                } else if (error.message.includes('quota') || error.message.includes('limit')) {\n                    mensajeError = 'Se ha alcanzado el límite de uso del servicio de IA. Inténtalo más tarde.';\n                } else {\n                    mensajeError = \"Error: \".concat(error.message);\n                }\n            }\n            setError(mensajeError);\n            // Añadir mensaje de error como respuesta de la IA\n            const mensajeErrorIA = {\n                tipo: 'ia',\n                contenido: mensajeError,\n                timestamp: new Date()\n            };\n            setMensajes((prevMensajes)=>[\n                    ...prevMensajes,\n                    mensajeErrorIA\n                ]);\n            // Intentar guardar el mensaje de error en Supabase (sin fallar si no se puede)\n            try {\n                await guardarMensajeEnDB(mensajeErrorIA);\n            } catch (dbError) {\n                console.error('Error al guardar mensaje de error en DB:', dbError);\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Formatear la fecha para mostrarla en el chat\n    const formatearFecha = (fecha)=>{\n        return fecha.toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-6 flex flex-col h-[600px]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>setMostrarHistorial(!mostrarHistorial),\n                        className: \"bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded inline-flex items-center\",\n                        children: mostrarHistorial ? 'Ocultar historial' : 'Ver historial de conversaciones'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: iniciarNuevaConversacion,\n                        className: \"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded inline-flex items-center\",\n                        disabled: isLoading,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"h-5 w-5 mr-2\",\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 11\n                            }, this),\n                            \"Nueva conversaci\\xf3n\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                lineNumber: 380,\n                columnNumber: 7\n            }, this),\n            mostrarHistorial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConversationHistory__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onSelectConversation: cargarConversacion,\n                conversacionActualId: conversacionActualId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                lineNumber: 404,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatContainerRef,\n                className: \"flex-grow overflow-y-auto mb-4 p-4 border rounded-lg bg-gray-50\",\n                style: {\n                    height: 'calc(100% - 180px)'\n                },\n                children: mensajes.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full text-gray-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Selecciona documentos y haz una pregunta para comenzar la conversaci\\xf3n.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                    lineNumber: 417,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        mensajes.map((mensaje, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex \".concat(mensaje.tipo === 'usuario' ? 'justify-end' : 'justify-start'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-[80%] p-3 rounded-lg \".concat(mensaje.tipo === 'usuario' ? 'bg-blue-500 text-white rounded-br-none' : 'bg-white border border-gray-300 rounded-bl-none'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"whitespace-pre-wrap\",\n                                            children: mensaje.contenido\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs mt-1 text-right \".concat(mensaje.tipo === 'usuario' ? 'text-blue-100' : 'text-gray-500'),\n                                            children: formatearFecha(mensaje.timestamp)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 17\n                                }, this)\n                            }, mensaje.id || index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 15\n                            }, this)),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-3 rounded-lg border border-gray-300 rounded-bl-none\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-bounce h-2 w-2 bg-gray-500 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-bounce h-2 w-2 bg-gray-500 rounded-full\",\n                                            style: {\n                                                animationDelay: '0.2s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-bounce h-2 w-2 bg-gray-500 rounded-full\",\n                                            style: {\n                                                animationDelay: '0.4s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                            lineNumber: 447,\n                            columnNumber: 15\n                        }, this),\n                        guardandoConversacion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 text-center py-1\",\n                            children: \"Guardando conversaci\\xf3n...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                            lineNumber: 459,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                    lineNumber: 421,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                lineNumber: 411,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: (e)=>{\n                    console.log('📋 Evento onSubmit del formulario detectado');\n                    console.log('🎯 Llamando a handleSubmitForm...');\n                    handleSubmitForm(onSubmit)(e);\n                },\n                className: \"mt-auto\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-500 text-sm mb-2\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 477,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-600 mb-2\",\n                        children: documentosSeleccionados.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-600\",\n                            children: [\n                                \"✓ \",\n                                documentosSeleccionados.length,\n                                \" documento\",\n                                documentosSeleccionados.length !== 1 ? 's' : '',\n                                \" seleccionado\",\n                                documentosSeleccionados.length !== 1 ? 's' : ''\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                            lineNumber: 483,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-600\",\n                            children: \"⚠ No hay documentos seleccionados. Selecciona al menos uno para hacer preguntas.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                            lineNumber: 487,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 481,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-grow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"pregunta\",\n                                        className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                        rows: 2,\n                                        ...register('pregunta'),\n                                        placeholder: \"Escribe tu pregunta sobre los documentos seleccionados...\",\n                                        disabled: isLoading,\n                                        onKeyDown: (e)=>{\n                                            if (e.key === 'Enter' && !e.shiftKey) {\n                                                e.preventDefault();\n                                                handleSubmitForm(onSubmit)(); // Ejecutar la función devuelta\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.pregunta && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-500 text-xs mt-1\",\n                                        children: errors.pregunta.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: \"Presiona Enter para enviar, Shift+Enter para nueva l\\xednea\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-full h-10 w-10 flex items-center justify-center focus:outline-none focus:shadow-outline disabled:opacity-50 disabled:cursor-not-allowed\",\n                                disabled: isLoading || documentosSeleccionados.length === 0,\n                                title: documentosSeleccionados.length === 0 ? 'Selecciona al menos un documento para hacer una pregunta' : 'Enviar pregunta',\n                                onClick: ()=>{\n                                    console.log('🖱️ Click en botón de envío detectado');\n                                    console.log('🔒 Botón deshabilitado?', isLoading || documentosSeleccionados.length === 0);\n                                    console.log('⏳ isLoading:', isLoading);\n                                    console.log('📄 documentos seleccionados:', documentosSeleccionados.length);\n                                },\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin h-5 w-5 text-white\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 008-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-5 w-5 text-white\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 515,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                lineNumber: 468,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n        lineNumber: 378,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionForm, \"I42aJdgBQNzGUtf/645NjTDxehY=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm\n    ];\n});\n_c = QuestionForm;\nvar _c;\n$RefreshReg$(_c, \"QuestionForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1F1ZXN0aW9uRm9ybS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBMkQ7QUFZbEM7QUFDK0I7QUFDZDtBQUNZO0FBQ0U7QUFjekMsU0FBU2UsYUFBYSxLQUE4QztRQUE5QyxFQUFFQyx1QkFBdUIsRUFBcUIsR0FBOUM7O0lBQ25DLE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHakIsK0NBQVFBLENBQVksRUFBRTtJQUN0RCxNQUFNLENBQUNrQixXQUFXQyxhQUFhLEdBQUduQiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNvQixPQUFPQyxTQUFTLEdBQUdyQiwrQ0FBUUEsQ0FBQztJQUNuQyxNQUFNLENBQUNzQixzQkFBc0JDLHdCQUF3QixHQUFHdkIsK0NBQVFBLENBQWdCO0lBQ2hGLE1BQU0sQ0FBQ3dCLGtCQUFrQkMsb0JBQW9CLEdBQUd6QiwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUMwQix1QkFBdUJDLHlCQUF5QixHQUFHM0IsK0NBQVFBLENBQUM7SUFDbkUsTUFBTSxDQUFDNEIsNEJBQTRCQyw4QkFBOEIsR0FBRzdCLCtDQUFRQSxDQUFDO0lBQzdFLE1BQU04QixtQkFBbUI3Qiw2Q0FBTUEsQ0FBaUI7SUFFaEQsTUFBTSxFQUNKOEIsUUFBUSxFQUNSQyxjQUFjQyxnQkFBZ0IsRUFDOUJDLFdBQVcsRUFBRUMsTUFBTSxFQUFFQyxPQUFPLEVBQUUsRUFDOUJDLEtBQUssRUFDTEMsUUFBUSxFQUNSQyxLQUFLLEVBQ04sR0FBRzVCLHdEQUFPQSxDQUFxQztRQUM5QzZCLFVBQVU1QixvRUFBV0EsQ0FBQ0MsZ0VBQWtCQTtRQUN4QzRCLGVBQWU7WUFDYkMsVUFBVTtZQUNWQyxZQUFZNUI7UUFDZDtJQUNGO0lBRUEsbURBQW1EO0lBQ25ELE1BQU02QixnQkFBZ0JMO0lBRXRCLGlEQUFpRDtJQUNqRHJDLGdEQUFTQTtrQ0FBQztZQUNSMkMsUUFBUUMsR0FBRyxDQUFDLDZCQUE2QjtnQkFDdkNDLFFBQVFIO2dCQUNSVDtnQkFDQUM7Z0JBQ0FyQix5QkFBeUJBLHdCQUF3QmlDLE1BQU07WUFDekQ7UUFDRjtpQ0FBRztRQUFDSjtRQUFlVDtRQUFRQztRQUFTckI7S0FBd0I7SUFFNUQscUZBQXFGO0lBQ3JGYixnREFBU0E7a0NBQUM7WUFDUjJDLFFBQVFDLEdBQUcsQ0FBQyxrREFBa0QvQjtZQUU5RCxNQUFNa0Msc0JBQXNCbEMsd0JBQXdCbUMsR0FBRzs4REFBQ0MsQ0FBQUE7b0JBQ3RELE1BQU1DLGNBQWM7d0JBQ2xCLEdBQUdELEdBQUc7d0JBQ05FLFdBQVdGLElBQUlFLFNBQVMsSUFBSTt3QkFDNUJDLGFBQWFILElBQUlHLFdBQVcsS0FBS0MsYUFBYUosSUFBSUcsV0FBVyxLQUFLLE9BQzdELE9BQU9ILElBQUlHLFdBQVcsS0FBSyxXQUFXRSxTQUFTTCxJQUFJRyxXQUFXLEVBQUUsTUFBTUgsSUFBSUcsV0FBVyxHQUN0RkM7d0JBQ0osMkRBQTJEO3dCQUMzREUsSUFBSU4sSUFBSU0sRUFBRSxJQUFJRjt3QkFDZEcsV0FBV1AsSUFBSU8sU0FBUyxJQUFJSDt3QkFDNUJJLGdCQUFnQlIsSUFBSVEsY0FBYyxJQUFJSjt3QkFDdENLLFNBQVNULElBQUlTLE9BQU8sSUFBSUw7d0JBQ3hCTSxlQUFlVixJQUFJVSxhQUFhLElBQUlOO29CQUN0QztvQkFFQVYsUUFBUUMsR0FBRyxDQUFDLDBCQUEwQk07b0JBQ3RDLE9BQU9BO2dCQUNUOztZQUVBZCxTQUFTLGNBQWNXO1lBQ3ZCSixRQUFRQyxHQUFHLENBQUM7UUFDZDtpQ0FBRztRQUFDL0I7UUFBeUJ1QjtLQUFTO0lBRXRDLHVEQUF1RDtJQUN2RHBDLGdEQUFTQTtrQ0FBQztZQUNSLE1BQU00RDttRUFBMkI7b0JBQy9CakMsOEJBQThCO29CQUM5QixJQUFJO3dCQUNGLE1BQU1rQyxxQkFBcUIsTUFBTXZELHdFQUF5QkE7d0JBRTFELElBQUl1RCxvQkFBb0I7NEJBQ3RCbEIsUUFBUUMsR0FBRyxDQUFDLG1DQUFtQ2lCLG1CQUFtQk4sRUFBRTs0QkFDcEVsQyx3QkFBd0J3QyxtQkFBbUJOLEVBQUU7NEJBQzdDLE1BQU1PLG1CQUFtQkQsbUJBQW1CTixFQUFFO3dCQUNoRCxPQUFPOzRCQUNMWixRQUFRQyxHQUFHLENBQUM7NEJBQ1o3QixZQUFZLEVBQUU7NEJBQ2RNLHdCQUF3Qjt3QkFDMUI7b0JBQ0YsRUFBRSxPQUFPSCxPQUFPO3dCQUNkeUIsUUFBUW9CLElBQUksQ0FBQyxtRkFBbUY3Qzt3QkFDaEcsd0VBQXdFO3dCQUN4RUgsWUFBWSxFQUFFO3dCQUNkTSx3QkFBd0I7b0JBQzFCLFNBQVU7d0JBQ1JNLDhCQUE4QjtvQkFDaEM7Z0JBQ0Y7O1lBRUFpQztRQUNGO2lDQUFHLEVBQUU7SUFFTCx1RUFBdUU7SUFDdkU1RCxnREFBU0E7a0NBQUM7WUFDUixJQUFJNEIsaUJBQWlCb0MsT0FBTyxFQUFFO2dCQUM1QnBDLGlCQUFpQm9DLE9BQU8sQ0FBQ0MsU0FBUyxHQUFHckMsaUJBQWlCb0MsT0FBTyxDQUFDRSxZQUFZO1lBQzVFO1FBQ0Y7aUNBQUc7UUFBQ3BEO0tBQVM7SUFFYixzREFBc0Q7SUFDdEQsTUFBTWdELHFCQUFxQixPQUFPSztRQUNoQyxJQUFJO1lBQ0ZsRCxhQUFhO1lBRWIsdUNBQXVDO1lBQ3ZDLE1BQU1aLGtFQUFtQkEsQ0FBQzhEO1lBRTFCLDBDQUEwQztZQUMxQyxNQUFNQyxhQUFhLE1BQU1qRSwrRUFBZ0NBLENBQUNnRTtZQUUxRCw4REFBOEQ7WUFDOUQsTUFBTUUsc0JBQWlDRCxXQUFXcEIsR0FBRyxDQUFDc0IsQ0FBQUEsTUFBUTtvQkFDNURmLElBQUllLElBQUlmLEVBQUU7b0JBQ1ZnQixNQUFNRCxJQUFJQyxJQUFJO29CQUNkQyxXQUFXRixJQUFJRSxTQUFTO29CQUN4QkMsV0FBVyxJQUFJQyxLQUFLSixJQUFJRyxTQUFTO2dCQUNuQztZQUVBLHVCQUF1QjtZQUN2QjFELFlBQVlzRDtZQUNaaEQsd0JBQXdCOEM7WUFDeEJoRCxTQUFTO1lBRVR3QixRQUFRQyxHQUFHLENBQUMsbUJBQStCLE9BQWZ1QixnQkFBZTtRQUM3QyxFQUFFLE9BQU9qRCxPQUFPO1lBQ2R5QixRQUFRekIsS0FBSyxDQUFDLG9DQUFvQ0E7WUFDbERDLFNBQVM7UUFDWCxTQUFVO1lBQ1JGLGFBQWE7UUFDZjtJQUNGO0lBRUEsOENBQThDO0lBQzlDLE1BQU0wRCxxQkFBcUIsT0FBT0M7UUFDaEMsSUFBSTtZQUNGbkQseUJBQXlCO1lBRXpCLHFEQUFxRDtZQUNyRCxJQUFJLENBQUNMLHNCQUFzQjtnQkFDekIsd0VBQXdFO2dCQUN4RSxJQUFJd0QsUUFBUUwsSUFBSSxLQUFLLFdBQVc7b0JBQzlCLGdEQUFnRDtvQkFDaEQsTUFBTU0sU0FBUyxvQkFBc0RELE9BQXJDQSxRQUFRSixTQUFTLENBQUNNLFNBQVMsQ0FBQyxHQUFHLEtBQWlELE9BQTNDRixRQUFRSixTQUFTLENBQUMxQixNQUFNLEdBQUcsS0FBSyxRQUFRO29CQUU3RyxzREFBc0Q7b0JBQ3RELE1BQU1pQyxVQUFVLE1BQU05RSxnRUFBaUJBLENBQUM0RSxRQUFRO29CQUNoRCxJQUFJLENBQUNFLFNBQVM7d0JBQ1osTUFBTSxJQUFJQyxNQUFNO29CQUNsQjtvQkFFQXJDLFFBQVFDLEdBQUcsQ0FBQyw0Q0FBaUQsT0FBUm1DO29CQUVyRCx5REFBeUQ7b0JBQ3pEMUQsd0JBQXdCMEQ7b0JBRXhCLDhDQUE4QztvQkFDOUMsTUFBTTdFLDZEQUFjQSxDQUFDO3dCQUNuQitFLGlCQUFpQkY7d0JBQ2pCUixNQUFNSyxRQUFRTCxJQUFJO3dCQUNsQkMsV0FBV0ksUUFBUUosU0FBUztvQkFDOUI7Z0JBQ0YsT0FBTztvQkFDTCw2REFBNkQ7b0JBQzdELHFFQUFxRTtvQkFDckU3QixRQUFRb0IsSUFBSSxDQUFDO29CQUViLE1BQU1jLFNBQVM7b0JBQ2YsTUFBTUUsVUFBVSxNQUFNOUUsZ0VBQWlCQSxDQUFDNEUsUUFBUTtvQkFFaEQsSUFBSSxDQUFDRSxTQUFTO3dCQUNaLE1BQU0sSUFBSUMsTUFBTTtvQkFDbEI7b0JBRUFyQyxRQUFRQyxHQUFHLENBQUMsb0RBQXNELE9BQVJtQztvQkFDMUQxRCx3QkFBd0IwRDtvQkFFeEIsOENBQThDO29CQUM5QyxNQUFNN0UsNkRBQWNBLENBQUM7d0JBQ25CK0UsaUJBQWlCRjt3QkFDakJSLE1BQU1LLFFBQVFMLElBQUk7d0JBQ2xCQyxXQUFXSSxRQUFRSixTQUFTO29CQUM5QjtnQkFDRjtZQUNGLE9BQU87Z0JBQ0wsOERBQThEO2dCQUM5RCxNQUFNWCxxQkFBcUIsTUFBTXZELHdFQUF5QkE7Z0JBRTFELElBQUksQ0FBQ3VELHNCQUFzQkEsbUJBQW1CTixFQUFFLEtBQUtuQyxzQkFBc0I7b0JBQ3pFLHVEQUF1RDtvQkFDdkR1QixRQUFRQyxHQUFHLENBQUMsZ0NBQWtELE9BQXJCeEI7b0JBQ3pDLE1BQU1mLGtFQUFtQkEsQ0FBQ2U7Z0JBQzVCO2dCQUVBLGtEQUFrRDtnQkFDbEQsTUFBTWxCLDZEQUFjQSxDQUFDO29CQUNuQitFLGlCQUFpQjdEO29CQUNqQm1ELE1BQU1LLFFBQVFMLElBQUk7b0JBQ2xCQyxXQUFXSSxRQUFRSixTQUFTO2dCQUM5QjtZQUNGO1FBQ0YsRUFBRSxPQUFPdEQsT0FBTztZQUNkeUIsUUFBUXpCLEtBQUssQ0FBQyxnQ0FBZ0NBO1FBQzlDLG1FQUFtRTtRQUNyRSxTQUFVO1lBQ1JPLHlCQUF5QjtRQUMzQjtJQUNGO0lBRUEsOENBQThDO0lBQzlDLE1BQU15RCwyQkFBMkI7UUFDL0IsSUFBSTtZQUNGakUsYUFBYTtZQUViLDBEQUEwRDtZQUMxRCxNQUFNViwrRUFBZ0NBO1lBRXRDLGdDQUFnQztZQUNoQ1EsWUFBWSxFQUFFO1lBQ2QsMkZBQTJGO1lBQzNGTSx3QkFBd0I7WUFDeEJGLFNBQVM7WUFFVHdCLFFBQVFDLEdBQUcsQ0FBQztRQUNkLEVBQUUsT0FBTzFCLE9BQU87WUFDZHlCLFFBQVF6QixLQUFLLENBQUMsd0NBQXdDQTtRQUN4RCxTQUFVO1lBQ1JELGFBQWE7UUFDZjtJQUNGO0lBRUEsZ0RBQWdEO0lBQ2hELE1BQU1rRSxXQUFXLE9BQU9DO1FBQ3RCekMsUUFBUUMsR0FBRyxDQUFDO1FBQ1pELFFBQVFDLEdBQUcsQ0FBQyxvQ0FBb0N3QztRQUNoRHpDLFFBQVFDLEdBQUcsQ0FBQyxnQ0FBZ0MvQjtRQUM1QzhCLFFBQVFDLEdBQUcsQ0FBQztRQUVaM0IsYUFBYTtRQUNiRSxTQUFTO1FBRVQsOENBQThDO1FBQzlDLE1BQU1rRSxrQkFBMkI7WUFDL0JkLE1BQU07WUFDTkMsV0FBV1ksS0FBSzVDLFFBQVE7WUFDeEJpQyxXQUFXLElBQUlDO1FBQ2pCO1FBRUEzRCxZQUFZdUUsQ0FBQUEsZUFBZ0I7bUJBQUlBO2dCQUFjRDthQUFnQjtRQUM5RHBFLGFBQWE7UUFDYkUsU0FBUztRQUVULG1EQUFtRDtRQUNuRGdCLE1BQU07WUFBRUssVUFBVTtZQUFJQyxZQUFZNUI7UUFBd0I7UUFFMUQsSUFBSTtZQUNGLDhDQUE4QztZQUM5QyxNQUFNOEQsbUJBQW1CVTtZQUV6QixpRUFBaUU7WUFDakUsMkVBQTJFO1lBRTNFLDZCQUE2QjtZQUM3QixNQUFNRSxXQUFXLE1BQU1DLE1BQU0sZUFBZTtnQkFDMUNDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUNuQnJELFVBQVU2QyxnQkFBZ0JiLFNBQVM7b0JBQ25DL0IsWUFBWTJDLEtBQUszQyxVQUFVO2dCQUM3QjtZQUNGO1lBQ0EsTUFBTXFELGNBQWMsTUFBTVAsU0FBU1EsSUFBSTtZQUN2QyxJQUFJQyxpQkFBaUI7WUFDckIsSUFBSUYsWUFBWUcsTUFBTSxFQUFFO2dCQUN0QkQsaUJBQWlCLE9BQU9GLFlBQVlHLE1BQU0sS0FBSyxXQUFXSCxZQUFZRyxNQUFNLEdBQUdMLEtBQUtDLFNBQVMsQ0FBQ0MsWUFBWUcsTUFBTTtZQUNsSCxPQUFPLElBQUlILFlBQVk1RSxLQUFLLEVBQUU7Z0JBQzVCOEUsaUJBQWlCLE9BQU9GLFlBQVk1RSxLQUFLLEtBQUssV0FBVzRFLFlBQVk1RSxLQUFLLEdBQUcwRSxLQUFLQyxTQUFTLENBQUNDLFlBQVk1RSxLQUFLO1lBQy9HLE9BQU87Z0JBQ0w4RSxpQkFBaUI7WUFDbkI7WUFFQSw0Q0FBNEM7WUFDNUMsTUFBTUUsWUFBcUI7Z0JBQ3pCM0IsTUFBTTtnQkFDTkMsV0FBV3dCO2dCQUNYdkIsV0FBVyxJQUFJQztZQUNqQjtZQUVBM0QsWUFBWXVFLENBQUFBLGVBQWdCO3VCQUFJQTtvQkFBY1k7aUJBQVU7WUFFeEQsNENBQTRDO1lBQzVDLE1BQU12QixtQkFBbUJ1QjtZQUV6QixtR0FBbUc7WUFDbkcsSUFBSXBGLFNBQVNnQyxNQUFNLEtBQUssS0FBSzFCLHNCQUFzQjtnQkFDakQsTUFBTStFLGlCQUFpQixvQkFBa0RmLE9BQWpDQSxLQUFLNUMsUUFBUSxDQUFDc0MsU0FBUyxDQUFDLEdBQUcsS0FBNkMsT0FBdkNNLEtBQUs1QyxRQUFRLENBQUNNLE1BQU0sR0FBRyxLQUFLLFFBQVE7Z0JBQzdHLE1BQU0xQyxxRUFBc0JBLENBQUNnQixzQkFBc0IrRTtZQUNyRDtRQUNGLEVBQUUsT0FBT2pGLE9BQU87WUFDZHlCLFFBQVF6QixLQUFLLENBQUMsK0JBQStCQTtZQUU3QyxrRUFBa0U7WUFDbEUsSUFBSWtGLGVBQWU7WUFFbkIsSUFBSWxGLGlCQUFpQjhELE9BQU87Z0JBQzFCLElBQUk5RCxNQUFNbUYsT0FBTyxDQUFDQyxRQUFRLENBQUMsWUFBWTtvQkFDckNGLGVBQWU7Z0JBQ2pCLE9BQU8sSUFBSWxGLE1BQU1tRixPQUFPLENBQUNDLFFBQVEsQ0FBQyxjQUFjcEYsTUFBTW1GLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDLFVBQVU7b0JBQy9FRixlQUFlO2dCQUNqQixPQUFPLElBQUlsRixNQUFNbUYsT0FBTyxDQUFDQyxRQUFRLENBQUMsWUFBWXBGLE1BQU1tRixPQUFPLENBQUNDLFFBQVEsQ0FBQyxVQUFVO29CQUM3RUYsZUFBZTtnQkFDakIsT0FBTztvQkFDTEEsZUFBZSxVQUF3QixPQUFkbEYsTUFBTW1GLE9BQU87Z0JBQ3hDO1lBQ0Y7WUFFQWxGLFNBQVNpRjtZQUVULGtEQUFrRDtZQUNsRCxNQUFNRyxpQkFBMEI7Z0JBQzlCaEMsTUFBTTtnQkFDTkMsV0FBVzRCO2dCQUNYM0IsV0FBVyxJQUFJQztZQUNqQjtZQUVBM0QsWUFBWXVFLENBQUFBLGVBQWdCO3VCQUFJQTtvQkFBY2lCO2lCQUFlO1lBRTdELCtFQUErRTtZQUMvRSxJQUFJO2dCQUNGLE1BQU01QixtQkFBbUI0QjtZQUMzQixFQUFFLE9BQU9DLFNBQVM7Z0JBQ2hCN0QsUUFBUXpCLEtBQUssQ0FBQyw0Q0FBNENzRjtZQUM1RDtRQUNGLFNBQVU7WUFDUnZGLGFBQWE7UUFDZjtJQUNGO0lBRUEsK0NBQStDO0lBQy9DLE1BQU13RixpQkFBaUIsQ0FBQ0M7UUFDdEIsT0FBT0EsTUFBTUMsa0JBQWtCLENBQUMsRUFBRSxFQUFFO1lBQUVDLE1BQU07WUFBV0MsUUFBUTtRQUFVO0lBQzNFO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUNDQyxNQUFLO3dCQUNMQyxTQUFTLElBQU0zRixvQkFBb0IsQ0FBQ0Q7d0JBQ3BDeUYsV0FBVTtrQ0FFVHpGLG1CQUFtQixzQkFBc0I7Ozs7OztrQ0FHNUMsOERBQUMwRjt3QkFDQ0MsTUFBSzt3QkFDTEMsU0FBU2hDO3dCQUNUNkIsV0FBVTt3QkFDVkksVUFBVW5HOzswQ0FFViw4REFBQ29HO2dDQUFJQyxPQUFNO2dDQUE2Qk4sV0FBVTtnQ0FBZU8sU0FBUTtnQ0FBWUMsTUFBSzswQ0FDeEYsNEVBQUNDO29DQUFLQyxVQUFTO29DQUFVQyxHQUFFO29DQUF3RkMsVUFBUzs7Ozs7Ozs7Ozs7NEJBQ3hIOzs7Ozs7Ozs7Ozs7O1lBTVRyRyxrQ0FDQyw4REFBQ2QsNERBQW1CQTtnQkFDbEJvSCxzQkFBc0I5RDtnQkFDdEIxQyxzQkFBc0JBOzs7Ozs7MEJBSzFCLDhEQUFDMEY7Z0JBQ0NlLEtBQUtqRztnQkFDTG1GLFdBQVU7Z0JBQ1ZlLE9BQU87b0JBQUVDLFFBQVE7Z0JBQXFCOzBCQUVyQ2pILFNBQVNnQyxNQUFNLEtBQUssa0JBQ25CLDhEQUFDZ0U7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNpQjtrQ0FBRTs7Ozs7Ozs7Ozt5Q0FHTCw4REFBQ2xCO29CQUFJQyxXQUFVOzt3QkFDWmpHLFNBQVNrQyxHQUFHLENBQUMsQ0FBQzRCLFNBQVNxRCxzQkFDdEIsOERBQUNuQjtnQ0FFQ0MsV0FBVyxRQUFxRSxPQUE3RG5DLFFBQVFMLElBQUksS0FBSyxZQUFZLGdCQUFnQjswQ0FFaEUsNEVBQUN1QztvQ0FDQ0MsV0FBVyw4QkFJVixPQUhDbkMsUUFBUUwsSUFBSSxLQUFLLFlBQ2IsMkNBQ0E7O3NEQUdOLDhEQUFDdUM7NENBQUlDLFdBQVU7c0RBQXVCbkMsUUFBUUosU0FBUzs7Ozs7O3NEQUN2RCw4REFBQ3NDOzRDQUNDQyxXQUFXLDJCQUVWLE9BRENuQyxRQUFRTCxJQUFJLEtBQUssWUFBWSxrQkFBa0I7c0RBR2hEa0MsZUFBZTdCLFFBQVFILFNBQVM7Ozs7Ozs7Ozs7OzsrQkFoQmhDRyxRQUFRckIsRUFBRSxJQUFJMEU7Ozs7O3dCQXNCdEJqSCwyQkFDQyw4REFBQzhGOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7Ozs7O3NEQUNmLDhEQUFDRDs0Q0FBSUMsV0FBVTs0Q0FBa0RlLE9BQU87Z0RBQUVJLGdCQUFnQjs0Q0FBTzs7Ozs7O3NEQUNqRyw4REFBQ3BCOzRDQUFJQyxXQUFVOzRDQUFrRGUsT0FBTztnREFBRUksZ0JBQWdCOzRDQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dCQU14RzFHLHVDQUNDLDhEQUFDc0Y7NEJBQUlDLFdBQVU7c0NBQXlDOzs7Ozs7Ozs7Ozs7Ozs7OzswQkFTaEUsOERBQUNvQjtnQkFDQ2hELFVBQVUsQ0FBQ2lEO29CQUNUekYsUUFBUUMsR0FBRyxDQUFDO29CQUNaRCxRQUFRQyxHQUFHLENBQUM7b0JBQ1piLGlCQUFpQm9ELFVBQVVpRDtnQkFDN0I7Z0JBQ0FyQixXQUFVOztvQkFFVDdGLHVCQUNDLDhEQUFDNEY7d0JBQUlDLFdBQVU7a0NBQTZCN0Y7Ozs7OztrQ0FJOUMsOERBQUM0Rjt3QkFBSUMsV0FBVTtrQ0FDWmxHLHdCQUF3QmlDLE1BQU0sR0FBRyxrQkFDaEMsOERBQUN1Rjs0QkFBS3RCLFdBQVU7O2dDQUFpQjtnQ0FDNUJsRyx3QkFBd0JpQyxNQUFNO2dDQUFDO2dDQUFXakMsd0JBQXdCaUMsTUFBTSxLQUFLLElBQUksTUFBTTtnQ0FBRztnQ0FBY2pDLHdCQUF3QmlDLE1BQU0sS0FBSyxJQUFJLE1BQU07Ozs7OztpREFHMUosOERBQUN1Rjs0QkFBS3RCLFdBQVU7c0NBQWU7Ozs7Ozs7Ozs7O2tDQU1uQyw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUN1Qjt3Q0FDQy9FLElBQUc7d0NBQ0h3RCxXQUFVO3dDQUNWd0IsTUFBTTt3Q0FDTCxHQUFHMUcsU0FBUyxXQUFXO3dDQUN4QjJHLGFBQVk7d0NBQ1pyQixVQUFVbkc7d0NBQ1Z5SCxXQUFXLENBQUNMOzRDQUNWLElBQUlBLEVBQUVNLEdBQUcsS0FBSyxXQUFXLENBQUNOLEVBQUVPLFFBQVEsRUFBRTtnREFDcENQLEVBQUVRLGNBQWM7Z0RBQ2hCN0csaUJBQWlCb0QsYUFBYSwrQkFBK0I7NENBQy9EO3dDQUNGOzs7Ozs7b0NBRURsRCxPQUFPTyxRQUFRLGtCQUNkLDhEQUFDd0Y7d0NBQUVqQixXQUFVO2tEQUE2QjlFLE9BQU9PLFFBQVEsQ0FBQzZELE9BQU87Ozs7OztrREFFbkUsOERBQUMyQjt3Q0FBRWpCLFdBQVU7a0RBQTZCOzs7Ozs7Ozs7Ozs7MENBRzVDLDhEQUFDQztnQ0FDQ0MsTUFBSztnQ0FDTEYsV0FBVTtnQ0FDVkksVUFBVW5HLGFBQWFILHdCQUF3QmlDLE1BQU0sS0FBSztnQ0FDMUQrRixPQUFPaEksd0JBQXdCaUMsTUFBTSxLQUFLLElBQUksNkRBQTZEO2dDQUMzR29FLFNBQVM7b0NBQ1B2RSxRQUFRQyxHQUFHLENBQUM7b0NBQ1pELFFBQVFDLEdBQUcsQ0FBQywyQkFBMkI1QixhQUFhSCx3QkFBd0JpQyxNQUFNLEtBQUs7b0NBQ3ZGSCxRQUFRQyxHQUFHLENBQUMsZ0JBQWdCNUI7b0NBQzVCMkIsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQy9CLHdCQUF3QmlDLE1BQU07Z0NBQzVFOzBDQUVDOUIsMEJBQ0MsOERBQUNvRztvQ0FBSUwsV0FBVTtvQ0FBa0NNLE9BQU07b0NBQTZCRSxNQUFLO29DQUFPRCxTQUFROztzREFDdEcsOERBQUN3Qjs0Q0FBTy9CLFdBQVU7NENBQWFnQyxJQUFHOzRDQUFLQyxJQUFHOzRDQUFLQyxHQUFFOzRDQUFLQyxRQUFPOzRDQUFlQyxhQUFZOzs7Ozs7c0RBQ3hGLDhEQUFDM0I7NENBQUtULFdBQVU7NENBQWFRLE1BQUs7NENBQWVHLEdBQUU7Ozs7Ozs7Ozs7O3lEQUdyRCw4REFBQ047b0NBQUlMLFdBQVU7b0NBQXFCUSxNQUFLO29DQUFlRCxTQUFROzhDQUM5RCw0RUFBQ0U7d0NBQUtFLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFReEI7R0EvZndCOUc7O1FBaUJsQkgsb0RBQU9BOzs7S0FqQldHIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcc3JjXFxjb21wb25lbnRzXFxRdWVzdGlvbkZvcm0udHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlUmVmLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQge1xuICBEb2N1bWVudG8sXG4gIE1lbnNhamUgYXMgTWVuc2FqZURCLFxuICBjcmVhckNvbnZlcnNhY2lvbixcbiAgZ3VhcmRhck1lbnNhamUsXG4gIG9idGVuZXJNZW5zYWplc1BvckNvbnZlcnNhY2lvbklkLFxuICBvYnRlbmVyQ29udmVyc2FjaW9uUG9ySWQsXG4gIGFjdHVhbGl6YXJDb252ZXJzYWNpb24sXG4gIGFjdGl2YXJDb252ZXJzYWNpb24sXG4gIG9idGVuZXJDb252ZXJzYWNpb25BY3RpdmEsXG4gIGRlc2FjdGl2YXJUb2Rhc0xhc0NvbnZlcnNhY2lvbmVzXG59IGZyb20gJy4uL2xpYi9zdXBhYmFzZSc7XG5pbXBvcnQgQ29udmVyc2F0aW9uSGlzdG9yeSBmcm9tICcuL0NvbnZlcnNhdGlvbkhpc3RvcnknO1xuaW1wb3J0IHsgdXNlRm9ybSB9IGZyb20gJ3JlYWN0LWhvb2stZm9ybSc7XG5pbXBvcnQgeyB6b2RSZXNvbHZlciB9IGZyb20gJ0Bob29rZm9ybS9yZXNvbHZlcnMvem9kJztcbmltcG9ydCB7IHByZWd1bnRhRm9ybVNjaGVtYSB9IGZyb20gJy4uL2xpYi9mb3JtU2NoZW1hcyc7XG5pbXBvcnQgeyB6IH0gZnJvbSAnem9kJztcblxuaW50ZXJmYWNlIFF1ZXN0aW9uRm9ybVByb3BzIHtcbiAgZG9jdW1lbnRvc1NlbGVjY2lvbmFkb3M6IERvY3VtZW50b1tdO1xufVxuXG5pbnRlcmZhY2UgTWVuc2FqZSB7XG4gIHRpcG86ICd1c3VhcmlvJyB8ICdpYSc7XG4gIGNvbnRlbmlkbzogc3RyaW5nO1xuICB0aW1lc3RhbXA6IERhdGU7XG4gIGlkPzogc3RyaW5nO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBRdWVzdGlvbkZvcm0oeyBkb2N1bWVudG9zU2VsZWNjaW9uYWRvcyB9OiBRdWVzdGlvbkZvcm1Qcm9wcykge1xuICBjb25zdCBbbWVuc2FqZXMsIHNldE1lbnNhamVzXSA9IHVzZVN0YXRlPE1lbnNhamVbXT4oW10pO1xuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2NvbnZlcnNhY2lvbkFjdHVhbElkLCBzZXRDb252ZXJzYWNpb25BY3R1YWxJZF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW21vc3RyYXJIaXN0b3JpYWwsIHNldE1vc3RyYXJIaXN0b3JpYWxdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZ3VhcmRhbmRvQ29udmVyc2FjaW9uLCBzZXRHdWFyZGFuZG9Db252ZXJzYWNpb25dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbY2FyZ2FuZG9Db252ZXJzYWNpb25BY3RpdmEsIHNldENhcmdhbmRvQ29udmVyc2FjaW9uQWN0aXZhXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBjaGF0Q29udGFpbmVyUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcblxuICBjb25zdCB7XG4gICAgcmVnaXN0ZXIsXG4gICAgaGFuZGxlU3VibWl0OiBoYW5kbGVTdWJtaXRGb3JtLFxuICAgIGZvcm1TdGF0ZTogeyBlcnJvcnMsIGlzVmFsaWQgfSxcbiAgICByZXNldCxcbiAgICBzZXRWYWx1ZSwgLy8gPC0tIEHDsWFkaWRvIHBhcmEgc2luY3Jvbml6YXIgZG9jdW1lbnRvc1xuICAgIHdhdGNoXG4gIH0gPSB1c2VGb3JtPHouaW5mZXI8dHlwZW9mIHByZWd1bnRhRm9ybVNjaGVtYT4+KHtcbiAgICByZXNvbHZlcjogem9kUmVzb2x2ZXIocHJlZ3VudGFGb3JtU2NoZW1hKSxcbiAgICBkZWZhdWx0VmFsdWVzOiB7XG4gICAgICBwcmVndW50YTogJycsXG4gICAgICBkb2N1bWVudG9zOiBkb2N1bWVudG9zU2VsZWNjaW9uYWRvcyxcbiAgICB9LFxuICB9KTtcblxuICAvLyBPYnNlcnZhciBjYW1iaW9zIGVuIGVsIGZvcm11bGFyaW8gcGFyYSBkZWJ1Z2dpbmdcbiAgY29uc3Qgd2F0Y2hlZFZhbHVlcyA9IHdhdGNoKCk7XG5cbiAgLy8gTG9nIGRlIGRlYnVnZ2luZyBwYXJhIGVsIGVzdGFkbyBkZWwgZm9ybXVsYXJpb1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnNvbGUubG9nKCfwn5OdIEVzdGFkbyBkZWwgZm9ybXVsYXJpbzonLCB7XG4gICAgICB2YWx1ZXM6IHdhdGNoZWRWYWx1ZXMsXG4gICAgICBlcnJvcnMsXG4gICAgICBpc1ZhbGlkLFxuICAgICAgZG9jdW1lbnRvc1NlbGVjY2lvbmFkb3M6IGRvY3VtZW50b3NTZWxlY2Npb25hZG9zLmxlbmd0aFxuICAgIH0pO1xuICB9LCBbd2F0Y2hlZFZhbHVlcywgZXJyb3JzLCBpc1ZhbGlkLCBkb2N1bWVudG9zU2VsZWNjaW9uYWRvc10pO1xuXG4gIC8vIFNpbmNyb25pemFyIGRvY3VtZW50b3Mgc2VsZWNjaW9uYWRvcyBjb24gZWwgZm9ybXVsYXJpbywgYXNlZ3VyYW5kbyB0aXBvcyBjb3JyZWN0b3NcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zb2xlLmxvZygn8J+UhCBTaW5jcm9uaXphbmRvIGRvY3VtZW50b3MgY29uIGVsIGZvcm11bGFyaW86JywgZG9jdW1lbnRvc1NlbGVjY2lvbmFkb3MpO1xuXG4gICAgY29uc3QgZG9jdW1lbnRvc1ZhbGlkYWRvcyA9IGRvY3VtZW50b3NTZWxlY2Npb25hZG9zLm1hcChkb2MgPT4ge1xuICAgICAgY29uc3QgZG9jVmFsaWRhZG8gPSB7XG4gICAgICAgIC4uLmRvYyxcbiAgICAgICAgY2F0ZWdvcmlhOiBkb2MuY2F0ZWdvcmlhIHx8IG51bGwsXG4gICAgICAgIG51bWVyb190ZW1hOiBkb2MubnVtZXJvX3RlbWEgIT09IHVuZGVmaW5lZCAmJiBkb2MubnVtZXJvX3RlbWEgIT09IG51bGxcbiAgICAgICAgICA/ICh0eXBlb2YgZG9jLm51bWVyb190ZW1hID09PSAnc3RyaW5nJyA/IHBhcnNlSW50KGRvYy5udW1lcm9fdGVtYSwgMTApIDogZG9jLm51bWVyb190ZW1hKVxuICAgICAgICAgIDogdW5kZWZpbmVkLFxuICAgICAgICAvLyBBc2VndXJhciBxdWUgdG9kb3MgbG9zIGNhbXBvcyBvcGNpb25hbGVzIGVzdMOpbiBwcmVzZW50ZXNcbiAgICAgICAgaWQ6IGRvYy5pZCB8fCB1bmRlZmluZWQsXG4gICAgICAgIGNyZWFkb19lbjogZG9jLmNyZWFkb19lbiB8fCB1bmRlZmluZWQsXG4gICAgICAgIGFjdHVhbGl6YWRvX2VuOiBkb2MuYWN0dWFsaXphZG9fZW4gfHwgdW5kZWZpbmVkLFxuICAgICAgICB1c2VyX2lkOiBkb2MudXNlcl9pZCB8fCB1bmRlZmluZWQsXG4gICAgICAgIHRpcG9fb3JpZ2luYWw6IGRvYy50aXBvX29yaWdpbmFsIHx8IHVuZGVmaW5lZCxcbiAgICAgIH07XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5OEIERvY3VtZW50byB2YWxpZGFkbzonLCBkb2NWYWxpZGFkbyk7XG4gICAgICByZXR1cm4gZG9jVmFsaWRhZG87XG4gICAgfSk7XG5cbiAgICBzZXRWYWx1ZSgnZG9jdW1lbnRvcycsIGRvY3VtZW50b3NWYWxpZGFkb3MpO1xuICAgIGNvbnNvbGUubG9nKCfinIUgRG9jdW1lbnRvcyBzaW5jcm9uaXphZG9zIGNvbiBlbCBmb3JtdWxhcmlvJyk7XG4gIH0sIFtkb2N1bWVudG9zU2VsZWNjaW9uYWRvcywgc2V0VmFsdWVdKTtcblxuICAvLyBFZmVjdG8gcGFyYSBjYXJnYXIgbGEgY29udmVyc2FjacOzbiBhY3RpdmEgYWwgaW5pY2lhclxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGNhcmdhckNvbnZlcnNhY2lvbkFjdGl2YSA9IGFzeW5jICgpID0+IHtcbiAgICAgIHNldENhcmdhbmRvQ29udmVyc2FjaW9uQWN0aXZhKHRydWUpO1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgY29udmVyc2FjaW9uQWN0aXZhID0gYXdhaXQgb2J0ZW5lckNvbnZlcnNhY2lvbkFjdGl2YSgpO1xuXG4gICAgICAgIGlmIChjb252ZXJzYWNpb25BY3RpdmEpIHtcbiAgICAgICAgICBjb25zb2xlLmxvZygnQ29udmVyc2FjacOzbiBhY3RpdmEgZW5jb250cmFkYTonLCBjb252ZXJzYWNpb25BY3RpdmEuaWQpO1xuICAgICAgICAgIHNldENvbnZlcnNhY2lvbkFjdHVhbElkKGNvbnZlcnNhY2lvbkFjdGl2YS5pZCk7XG4gICAgICAgICAgYXdhaXQgY2FyZ2FyQ29udmVyc2FjaW9uKGNvbnZlcnNhY2lvbkFjdGl2YS5pZCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ05vIGhheSBjb252ZXJzYWNpw7NuIGFjdGl2YSAtIGVzdG8gZXMgbm9ybWFsIHBhcmEgdXN1YXJpb3MgbnVldm9zJyk7XG4gICAgICAgICAgc2V0TWVuc2FqZXMoW10pO1xuICAgICAgICAgIHNldENvbnZlcnNhY2lvbkFjdHVhbElkKG51bGwpO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLndhcm4oJ05vIHNlIHB1ZG8gY2FyZ2FyIGxhIGNvbnZlcnNhY2nDs24gYWN0aXZhIChlc3RvIGVzIG5vcm1hbCBwYXJhIHVzdWFyaW9zIG51ZXZvcyk6JywgZXJyb3IpO1xuICAgICAgICAvLyBObyBtb3N0cmFyIGVycm9yIGFsIHVzdWFyaW8sIHNpbXBsZW1lbnRlIGluaWNpYWxpemFyIHNpbiBjb252ZXJzYWNpw7NuXG4gICAgICAgIHNldE1lbnNhamVzKFtdKTtcbiAgICAgICAgc2V0Q29udmVyc2FjaW9uQWN0dWFsSWQobnVsbCk7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRDYXJnYW5kb0NvbnZlcnNhY2lvbkFjdGl2YShmYWxzZSk7XG4gICAgICB9XG4gICAgfTtcblxuICAgIGNhcmdhckNvbnZlcnNhY2lvbkFjdGl2YSgpO1xuICB9LCBbXSk7XG5cbiAgLy8gRWZlY3RvIHBhcmEgaGFjZXIgc2Nyb2xsIGFsIMO6bHRpbW8gbWVuc2FqZSBjdWFuZG8gc2UgYcOxYWRlIHVubyBudWV2b1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChjaGF0Q29udGFpbmVyUmVmLmN1cnJlbnQpIHtcbiAgICAgIGNoYXRDb250YWluZXJSZWYuY3VycmVudC5zY3JvbGxUb3AgPSBjaGF0Q29udGFpbmVyUmVmLmN1cnJlbnQuc2Nyb2xsSGVpZ2h0O1xuICAgIH1cbiAgfSwgW21lbnNhamVzXSk7XG5cbiAgLy8gRnVuY2nDs24gcGFyYSBjYXJnYXIgdW5hIGNvbnZlcnNhY2nDs24gZGVzZGUgU3VwYWJhc2VcbiAgY29uc3QgY2FyZ2FyQ29udmVyc2FjaW9uID0gYXN5bmMgKGNvbnZlcnNhY2lvbklkOiBzdHJpbmcpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuXG4gICAgICAvLyBBY3RpdmFyIGxhIGNvbnZlcnNhY2nDs24gc2VsZWNjaW9uYWRhXG4gICAgICBhd2FpdCBhY3RpdmFyQ29udmVyc2FjaW9uKGNvbnZlcnNhY2lvbklkKTtcblxuICAgICAgLy8gT2J0ZW5lciBsb3MgbWVuc2FqZXMgZGUgbGEgY29udmVyc2FjacOzblxuICAgICAgY29uc3QgbWVuc2FqZXNEQiA9IGF3YWl0IG9idGVuZXJNZW5zYWplc1BvckNvbnZlcnNhY2lvbklkKGNvbnZlcnNhY2lvbklkKTtcblxuICAgICAgLy8gQ29udmVydGlyIGxvcyBtZW5zYWplcyBkZSBsYSBiYXNlIGRlIGRhdG9zIGFsIGZvcm1hdG8gbG9jYWxcbiAgICAgIGNvbnN0IG1lbnNhamVzRm9ybWF0ZWFkb3M6IE1lbnNhamVbXSA9IG1lbnNhamVzREIubWFwKG1zZyA9PiAoe1xuICAgICAgICBpZDogbXNnLmlkLFxuICAgICAgICB0aXBvOiBtc2cudGlwbyxcbiAgICAgICAgY29udGVuaWRvOiBtc2cuY29udGVuaWRvLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKG1zZy50aW1lc3RhbXApXG4gICAgICB9KSk7XG5cbiAgICAgIC8vIEFjdHVhbGl6YXIgZWwgZXN0YWRvXG4gICAgICBzZXRNZW5zYWplcyhtZW5zYWplc0Zvcm1hdGVhZG9zKTtcbiAgICAgIHNldENvbnZlcnNhY2lvbkFjdHVhbElkKGNvbnZlcnNhY2lvbklkKTtcbiAgICAgIHNldEVycm9yKCcnKTtcblxuICAgICAgY29uc29sZS5sb2coYENvbnZlcnNhY2nDs24gJHtjb252ZXJzYWNpb25JZH0gY2FyZ2FkYSB5IGFjdGl2YWRhYCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIGNhcmdhciBsYSBjb252ZXJzYWNpw7NuOicsIGVycm9yKTtcbiAgICAgIHNldEVycm9yKCdObyBzZSBwdWRvIGNhcmdhciBsYSBjb252ZXJzYWNpw7NuJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIEZ1bmNpw7NuIHBhcmEgZ3VhcmRhciB1biBtZW5zYWplIGVuIFN1cGFiYXNlXG4gIGNvbnN0IGd1YXJkYXJNZW5zYWplRW5EQiA9IGFzeW5jIChtZW5zYWplOiBNZW5zYWplKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldEd1YXJkYW5kb0NvbnZlcnNhY2lvbih0cnVlKTtcblxuICAgICAgLy8gU2kgbm8gaGF5IHVuYSBjb252ZXJzYWNpw7NuIGFjdHVhbCwgY3JlYXIgdW5hIG51ZXZhXG4gICAgICBpZiAoIWNvbnZlcnNhY2lvbkFjdHVhbElkKSB7XG4gICAgICAgIC8vIFNvbG8gY3JlYXIgdW5hIG51ZXZhIGNvbnZlcnNhY2nDs24gc2kgZXMgZWwgcHJpbWVyIG1lbnNhamUgZGVsIHVzdWFyaW9cbiAgICAgICAgaWYgKG1lbnNhamUudGlwbyA9PT0gJ3VzdWFyaW8nKSB7XG4gICAgICAgICAgLy8gQ3JlYXIgdW4gdMOtdHVsbyBiYXNhZG8gZW4gbGEgcHJpbWVyYSBwcmVndW50YVxuICAgICAgICAgIGNvbnN0IHRpdHVsbyA9IGBDb252ZXJzYWNpw7NuOiAke21lbnNhamUuY29udGVuaWRvLnN1YnN0cmluZygwLCA1MCl9JHttZW5zYWplLmNvbnRlbmlkby5sZW5ndGggPiA1MCA/ICcuLi4nIDogJyd9YDtcblxuICAgICAgICAgIC8vIENyZWFyIHVuYSBudWV2YSBjb252ZXJzYWNpw7NuIHkgbWFyY2FybGEgY29tbyBhY3RpdmFcbiAgICAgICAgICBjb25zdCBudWV2b0lkID0gYXdhaXQgY3JlYXJDb252ZXJzYWNpb24odGl0dWxvLCB0cnVlKTtcbiAgICAgICAgICBpZiAoIW51ZXZvSWQpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignTm8gc2UgcHVkbyBjcmVhciBsYSBjb252ZXJzYWNpw7NuJyk7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgY29uc29sZS5sb2coYE51ZXZhIGNvbnZlcnNhY2nDs24gY3JlYWRhIHkgYWN0aXZhZGE6ICR7bnVldm9JZH1gKTtcblxuICAgICAgICAgIC8vIEd1YXJkYXIgZWwgSUQgZGUgbGEgY29udmVyc2FjacOzbiBwYXJhIGZ1dHVyb3MgbWVuc2FqZXNcbiAgICAgICAgICBzZXRDb252ZXJzYWNpb25BY3R1YWxJZChudWV2b0lkKTtcblxuICAgICAgICAgIC8vIEd1YXJkYXIgZWwgbWVuc2FqZSBlbiBsYSBudWV2YSBjb252ZXJzYWNpw7NuXG4gICAgICAgICAgYXdhaXQgZ3VhcmRhck1lbnNhamUoe1xuICAgICAgICAgICAgY29udmVyc2FjaW9uX2lkOiBudWV2b0lkLFxuICAgICAgICAgICAgdGlwbzogbWVuc2FqZS50aXBvLFxuICAgICAgICAgICAgY29udGVuaWRvOiBtZW5zYWplLmNvbnRlbmlkb1xuICAgICAgICAgIH0pO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIC8vIFNpIGVzIHVuIG1lbnNhamUgZGUgbGEgSUEgcGVybyBubyBoYXkgY29udmVyc2FjacOzbiBhY3R1YWwsXG4gICAgICAgICAgLy8gYWxnbyBzYWxpw7MgbWFsLiBJbnRlbnRhciByZWN1cGVyYXIgY3JlYW5kbyB1bmEgbnVldmEgY29udmVyc2FjacOzbi5cbiAgICAgICAgICBjb25zb2xlLndhcm4oJ05vIGhheSBjb252ZXJzYWNpw7NuIGFjdHVhbCBwYXJhIGd1YXJkYXIgZWwgbWVuc2FqZSBkZSBsYSBJQS4gQ3JlYW5kbyB1bmEgbnVldmEuJyk7XG5cbiAgICAgICAgICBjb25zdCB0aXR1bG8gPSAnTnVldmEgY29udmVyc2FjacOzbic7XG4gICAgICAgICAgY29uc3QgbnVldm9JZCA9IGF3YWl0IGNyZWFyQ29udmVyc2FjaW9uKHRpdHVsbywgdHJ1ZSk7XG5cbiAgICAgICAgICBpZiAoIW51ZXZvSWQpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignTm8gc2UgcHVkbyBjcmVhciBsYSBjb252ZXJzYWNpw7NuJyk7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgY29uc29sZS5sb2coYE51ZXZhIGNvbnZlcnNhY2nDs24gZGUgcmVjdXBlcmFjacOzbiBjcmVhZGE6ICR7bnVldm9JZH1gKTtcbiAgICAgICAgICBzZXRDb252ZXJzYWNpb25BY3R1YWxJZChudWV2b0lkKTtcblxuICAgICAgICAgIC8vIEd1YXJkYXIgZWwgbWVuc2FqZSBlbiBsYSBudWV2YSBjb252ZXJzYWNpw7NuXG4gICAgICAgICAgYXdhaXQgZ3VhcmRhck1lbnNhamUoe1xuICAgICAgICAgICAgY29udmVyc2FjaW9uX2lkOiBudWV2b0lkLFxuICAgICAgICAgICAgdGlwbzogbWVuc2FqZS50aXBvLFxuICAgICAgICAgICAgY29udGVuaWRvOiBtZW5zYWplLmNvbnRlbmlkb1xuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBWZXJpZmljYXIgcXVlIGxhIGNvbnZlcnNhY2nDs24gYWN0dWFsIHNpZ3VlIHNpZW5kbyBsYSBhY3RpdmFcbiAgICAgICAgY29uc3QgY29udmVyc2FjaW9uQWN0aXZhID0gYXdhaXQgb2J0ZW5lckNvbnZlcnNhY2lvbkFjdGl2YSgpO1xuXG4gICAgICAgIGlmICghY29udmVyc2FjaW9uQWN0aXZhIHx8IGNvbnZlcnNhY2lvbkFjdGl2YS5pZCAhPT0gY29udmVyc2FjaW9uQWN0dWFsSWQpIHtcbiAgICAgICAgICAvLyBTaSBsYSBjb252ZXJzYWNpw7NuIGFjdHVhbCBubyBlcyBsYSBhY3RpdmEsIGFjdGl2YXJsYVxuICAgICAgICAgIGNvbnNvbGUubG9nKGBSZWFjdGl2YW5kbyBjb252ZXJzYWNpw7NuOiAke2NvbnZlcnNhY2lvbkFjdHVhbElkfWApO1xuICAgICAgICAgIGF3YWl0IGFjdGl2YXJDb252ZXJzYWNpb24oY29udmVyc2FjaW9uQWN0dWFsSWQpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gR3VhcmRhciBlbCBtZW5zYWplIGVuIGxhIGNvbnZlcnNhY2nDs24gZXhpc3RlbnRlXG4gICAgICAgIGF3YWl0IGd1YXJkYXJNZW5zYWplKHtcbiAgICAgICAgICBjb252ZXJzYWNpb25faWQ6IGNvbnZlcnNhY2lvbkFjdHVhbElkLFxuICAgICAgICAgIHRpcG86IG1lbnNhamUudGlwbyxcbiAgICAgICAgICBjb250ZW5pZG86IG1lbnNhamUuY29udGVuaWRvXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhbCBndWFyZGFyIGVsIG1lbnNhamU6JywgZXJyb3IpO1xuICAgICAgLy8gTm8gbW9zdHJhbW9zIGVycm9yIGFsIHVzdWFyaW8gcGFyYSBubyBpbnRlcnJ1bXBpciBsYSBleHBlcmllbmNpYVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRHdWFyZGFuZG9Db252ZXJzYWNpb24oZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyBGdW5jacOzbiBwYXJhIGluaWNpYXIgdW5hIG51ZXZhIGNvbnZlcnNhY2nDs25cbiAgY29uc3QgaW5pY2lhck51ZXZhQ29udmVyc2FjaW9uID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XG5cbiAgICAgIC8vIERlc2FjdGl2YXIgdG9kYXMgbGFzIGNvbnZlcnNhY2lvbmVzIGVuIGxhIGJhc2UgZGUgZGF0b3NcbiAgICAgIGF3YWl0IGRlc2FjdGl2YXJUb2Rhc0xhc0NvbnZlcnNhY2lvbmVzKCk7XG5cbiAgICAgIC8vIExpbXBpYXIgbG9zIG1lbnNhamVzIGFjdHVhbGVzXG4gICAgICBzZXRNZW5zYWplcyhbXSk7XG4gICAgICAvLyBFc3RhYmxlY2VyIGVsIElEIGRlIGNvbnZlcnNhY2nDs24gYSBudWxsIHBhcmEgcXVlIHNlIGNyZWUgdW5hIG51ZXZhIGVuIGVsIHByw7N4aW1vIG1lbnNhamVcbiAgICAgIHNldENvbnZlcnNhY2lvbkFjdHVhbElkKG51bGwpO1xuICAgICAgc2V0RXJyb3IoJycpO1xuXG4gICAgICBjb25zb2xlLmxvZygnTnVldmEgY29udmVyc2FjacOzbiBpbmljaWFkYS4gRWwgcHLDs3hpbW8gbWVuc2FqZSBjcmVhcsOhIHVuYSBudWV2YSBjb252ZXJzYWNpw7NuIGVuIGxhIGJhc2UgZGUgZGF0b3MuJyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIGluaWNpYXIgbnVldmEgY29udmVyc2FjacOzbjonLCBlcnJvcik7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIENhbWJpYSBoYW5kbGVTdWJtaXQgcGFyYSB1c2FyIFJlYWN0IEhvb2sgRm9ybVxuICBjb25zdCBvblN1Ym1pdCA9IGFzeW5jIChkYXRhOiB6LmluZmVyPHR5cGVvZiBwcmVndW50YUZvcm1TY2hlbWE+KSA9PiB7XG4gICAgY29uc29sZS5sb2coJ/CfjokgwqFGVU5DScOTTiBvblN1Ym1pdCBFSkVDVVRBREEhJyk7XG4gICAgY29uc29sZS5sb2coJ/CfmoAgRm9ybXVsYXJpbyBlbnZpYWRvIGNvbiBkYXRvczonLCBkYXRhKTtcbiAgICBjb25zb2xlLmxvZygn8J+ThCBEb2N1bWVudG9zIHNlbGVjY2lvbmFkb3M6JywgZG9jdW1lbnRvc1NlbGVjY2lvbmFkb3MpO1xuICAgIGNvbnNvbGUubG9nKCfinIUgVmFsaWRhY2nDs24gcGFzYWRhIGNvcnJlY3RhbWVudGUnKTtcblxuICAgIHNldElzTG9hZGluZyh0cnVlKTtcbiAgICBzZXRFcnJvcignJyk7XG5cbiAgICAvLyBBw7FhZGlyIGxhIHByZWd1bnRhIGRlbCB1c3VhcmlvIGFsIGhpc3RvcmlhbFxuICAgIGNvbnN0IHByZWd1bnRhVXN1YXJpbzogTWVuc2FqZSA9IHtcbiAgICAgIHRpcG86ICd1c3VhcmlvJyxcbiAgICAgIGNvbnRlbmlkbzogZGF0YS5wcmVndW50YSxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKVxuICAgIH07XG5cbiAgICBzZXRNZW5zYWplcyhwcmV2TWVuc2FqZXMgPT4gWy4uLnByZXZNZW5zYWplcywgcHJlZ3VudGFVc3VhcmlvXSk7XG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgIHNldEVycm9yKCcnKTtcblxuICAgIC8vIExpbXBpYXIgZWwgY2FtcG8gZGUgcHJlZ3VudGEgZGVzcHXDqXMgZGUgZW52aWFybGFcbiAgICByZXNldCh7IHByZWd1bnRhOiAnJywgZG9jdW1lbnRvczogZG9jdW1lbnRvc1NlbGVjY2lvbmFkb3MgfSk7XG5cbiAgICB0cnkge1xuICAgICAgLy8gR3VhcmRhciBsYSBwcmVndW50YSBkZWwgdXN1YXJpbyBlbiBTdXBhYmFzZVxuICAgICAgYXdhaXQgZ3VhcmRhck1lbnNhamVFbkRCKHByZWd1bnRhVXN1YXJpbyk7XG5cbiAgICAgIC8vIFBhc2FyIGxvcyBkb2N1bWVudG9zIGNvbXBsZXRvcyBhIGxhIGZ1bmNpw7NuIG9idGVuZXJSZXNwdWVzdGFJQVxuICAgICAgLy8gTm8gc29sbyBlbCBjb250ZW5pZG8sIHNpbm8gdGFtYmnDqW4gZWwgdMOtdHVsbywgY2F0ZWdvcsOtYSB5IG7Dum1lcm8gZGUgdGVtYVxuXG4gICAgICAvLyBPYnRlbmVyIHJlc3B1ZXN0YSBkZSBsYSBJQVxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9nZW1pbmknLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJ1xuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgcHJlZ3VudGE6IHByZWd1bnRhVXN1YXJpby5jb250ZW5pZG8sXG4gICAgICAgICAgZG9jdW1lbnRvczogZGF0YS5kb2N1bWVudG9zXG4gICAgICAgIH0pXG4gICAgICB9KTtcbiAgICAgIGNvbnN0IHJlc3B1ZXN0YUlBID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgbGV0IHJlc3B1ZXN0YVRleHRvID0gJyc7XG4gICAgICBpZiAocmVzcHVlc3RhSUEucmVzdWx0KSB7XG4gICAgICAgIHJlc3B1ZXN0YVRleHRvID0gdHlwZW9mIHJlc3B1ZXN0YUlBLnJlc3VsdCA9PT0gJ3N0cmluZycgPyByZXNwdWVzdGFJQS5yZXN1bHQgOiBKU09OLnN0cmluZ2lmeShyZXNwdWVzdGFJQS5yZXN1bHQpO1xuICAgICAgfSBlbHNlIGlmIChyZXNwdWVzdGFJQS5lcnJvcikge1xuICAgICAgICByZXNwdWVzdGFUZXh0byA9IHR5cGVvZiByZXNwdWVzdGFJQS5lcnJvciA9PT0gJ3N0cmluZycgPyByZXNwdWVzdGFJQS5lcnJvciA6IEpTT04uc3RyaW5naWZ5KHJlc3B1ZXN0YUlBLmVycm9yKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHJlc3B1ZXN0YVRleHRvID0gJ0Vycm9yIGRlc2Nvbm9jaWRvIGFsIG9idGVuZXIgcmVzcHVlc3RhIGRlIGxhIElBLic7XG4gICAgICB9XG5cbiAgICAgIC8vIEHDsWFkaXIgbGEgcmVzcHVlc3RhIGRlIGxhIElBIGFsIGhpc3RvcmlhbFxuICAgICAgY29uc3QgbWVuc2FqZUlBOiBNZW5zYWplID0ge1xuICAgICAgICB0aXBvOiAnaWEnLFxuICAgICAgICBjb250ZW5pZG86IHJlc3B1ZXN0YVRleHRvLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKClcbiAgICAgIH07XG5cbiAgICAgIHNldE1lbnNhamVzKHByZXZNZW5zYWplcyA9PiBbLi4ucHJldk1lbnNhamVzLCBtZW5zYWplSUFdKTtcblxuICAgICAgLy8gR3VhcmRhciBsYSByZXNwdWVzdGEgZGUgbGEgSUEgZW4gU3VwYWJhc2VcbiAgICAgIGF3YWl0IGd1YXJkYXJNZW5zYWplRW5EQihtZW5zYWplSUEpO1xuXG4gICAgICAvLyBTaSBlcyBsYSBwcmltZXJhIHByZWd1bnRhLCBhY3R1YWxpemFyIGVsIHTDrXR1bG8gZGUgbGEgY29udmVyc2FjacOzbiBjb24gdW4gdMOtdHVsbyBtw6FzIGRlc2NyaXB0aXZvXG4gICAgICBpZiAobWVuc2FqZXMubGVuZ3RoID09PSAwICYmIGNvbnZlcnNhY2lvbkFjdHVhbElkKSB7XG4gICAgICAgIGNvbnN0IHRpdHVsb01lam9yYWRvID0gYENvbnZlcnNhY2nDs246ICR7ZGF0YS5wcmVndW50YS5zdWJzdHJpbmcoMCwgNTApfSR7ZGF0YS5wcmVndW50YS5sZW5ndGggPiA1MCA/ICcuLi4nIDogJyd9YDtcbiAgICAgICAgYXdhaXQgYWN0dWFsaXphckNvbnZlcnNhY2lvbihjb252ZXJzYWNpb25BY3R1YWxJZCwgdGl0dWxvTWVqb3JhZG8pO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhbCBvYnRlbmVyIHJlc3B1ZXN0YTonLCBlcnJvcik7XG5cbiAgICAgIC8vIERldGVybWluYXIgZWwgdGlwbyBkZSBlcnJvciB5IG1vc3RyYXIgdW4gbWVuc2FqZSBtw6FzIGVzcGVjw61maWNvXG4gICAgICBsZXQgbWVuc2FqZUVycm9yID0gJ0xvIHNpZW50bywgaGEgb2N1cnJpZG8gdW4gZXJyb3IgYWwgcHJvY2VzYXIgdHUgcHJlZ3VudGEuIFBvciBmYXZvciwgaW50w6ludGFsbyBkZSBudWV2by4nO1xuXG4gICAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBFcnJvcikge1xuICAgICAgICBpZiAoZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnQVBJIGtleScpKSB7XG4gICAgICAgICAgbWVuc2FqZUVycm9yID0gJ0Vycm9yIGRlIGNvbmZpZ3VyYWNpw7NuOiBMYSBjbGF2ZSBkZSBBUEkgZGUgR2VtaW5pIG5vIGVzdMOhIGNvbmZpZ3VyYWRhIGNvcnJlY3RhbWVudGUuJztcbiAgICAgICAgfSBlbHNlIGlmIChlcnJvci5tZXNzYWdlLmluY2x1ZGVzKCduZXR3b3JrJykgfHwgZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnZmV0Y2gnKSkge1xuICAgICAgICAgIG1lbnNhamVFcnJvciA9ICdFcnJvciBkZSBjb25leGnDs246IE5vIHNlIHB1ZG8gY29uZWN0YXIgY29uIGVsIHNlcnZpY2lvIGRlIElBLiBWZXJpZmljYSB0dSBjb25leGnDs24gYSBpbnRlcm5ldC4nO1xuICAgICAgICB9IGVsc2UgaWYgKGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJ3F1b3RhJykgfHwgZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnbGltaXQnKSkge1xuICAgICAgICAgIG1lbnNhamVFcnJvciA9ICdTZSBoYSBhbGNhbnphZG8gZWwgbMOtbWl0ZSBkZSB1c28gZGVsIHNlcnZpY2lvIGRlIElBLiBJbnTDqW50YWxvIG3DoXMgdGFyZGUuJztcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBtZW5zYWplRXJyb3IgPSBgRXJyb3I6ICR7ZXJyb3IubWVzc2FnZX1gO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIHNldEVycm9yKG1lbnNhamVFcnJvcik7XG5cbiAgICAgIC8vIEHDsWFkaXIgbWVuc2FqZSBkZSBlcnJvciBjb21vIHJlc3B1ZXN0YSBkZSBsYSBJQVxuICAgICAgY29uc3QgbWVuc2FqZUVycm9ySUE6IE1lbnNhamUgPSB7XG4gICAgICAgIHRpcG86ICdpYScsXG4gICAgICAgIGNvbnRlbmlkbzogbWVuc2FqZUVycm9yLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKClcbiAgICAgIH07XG5cbiAgICAgIHNldE1lbnNhamVzKHByZXZNZW5zYWplcyA9PiBbLi4ucHJldk1lbnNhamVzLCBtZW5zYWplRXJyb3JJQV0pO1xuXG4gICAgICAvLyBJbnRlbnRhciBndWFyZGFyIGVsIG1lbnNhamUgZGUgZXJyb3IgZW4gU3VwYWJhc2UgKHNpbiBmYWxsYXIgc2kgbm8gc2UgcHVlZGUpXG4gICAgICB0cnkge1xuICAgICAgICBhd2FpdCBndWFyZGFyTWVuc2FqZUVuREIobWVuc2FqZUVycm9ySUEpO1xuICAgICAgfSBjYXRjaCAoZGJFcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhbCBndWFyZGFyIG1lbnNhamUgZGUgZXJyb3IgZW4gREI6JywgZGJFcnJvcik7XG4gICAgICB9XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIEZvcm1hdGVhciBsYSBmZWNoYSBwYXJhIG1vc3RyYXJsYSBlbiBlbCBjaGF0XG4gIGNvbnN0IGZvcm1hdGVhckZlY2hhID0gKGZlY2hhOiBEYXRlKTogc3RyaW5nID0+IHtcbiAgICByZXR1cm4gZmVjaGEudG9Mb2NhbGVUaW1lU3RyaW5nKFtdLCB7IGhvdXI6ICcyLWRpZ2l0JywgbWludXRlOiAnMi1kaWdpdCcgfSk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTYgZmxleCBmbGV4LWNvbCBoLVs2MDBweF1cIj5cbiAgICAgIHsvKiBCb3RvbmVzIGRlIGNvbnRyb2wgcGFyYSBoaXN0b3JpYWwgeSBudWV2YSBjb252ZXJzYWNpw7NuICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgIDxidXR0b25cbiAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRNb3N0cmFySGlzdG9yaWFsKCFtb3N0cmFySGlzdG9yaWFsKX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmF5LTIwMCBob3ZlcjpiZy1ncmF5LTMwMCB0ZXh0LWdyYXktODAwIGZvbnQtc2VtaWJvbGQgcHktMiBweC00IHJvdW5kZWQgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyXCJcbiAgICAgICAgPlxuICAgICAgICAgIHttb3N0cmFySGlzdG9yaWFsID8gJ09jdWx0YXIgaGlzdG9yaWFsJyA6ICdWZXIgaGlzdG9yaWFsIGRlIGNvbnZlcnNhY2lvbmVzJ31cbiAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgPGJ1dHRvblxuICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgIG9uQ2xpY2s9e2luaWNpYXJOdWV2YUNvbnZlcnNhY2lvbn1cbiAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmVlbi01MDAgaG92ZXI6YmctZ3JlZW4tNjAwIHRleHQtd2hpdGUgZm9udC1zZW1pYm9sZCBweS0yIHB4LTQgcm91bmRlZCBpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXJcIlxuICAgICAgICAgIGRpc2FibGVkPXtpc0xvYWRpbmd9XG4gICAgICAgID5cbiAgICAgICAgICA8c3ZnIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIiBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTJcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCIgZmlsbD1cImN1cnJlbnRDb2xvclwiPlxuICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk0xMCAzYTEgMSAwIDAxMSAxdjVoNWExIDEgMCAxMTAgMmgtNXY1YTEgMSAwIDExLTIgMHYtNUg0YTEgMSAwIDExMC0yaDVWNGExIDEgMCAwMTEtMXpcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIiAvPlxuICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgIE51ZXZhIGNvbnZlcnNhY2nDs25cbiAgICAgICAgPC9idXR0b24+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIE1vc3RyYXIgZWwgaGlzdG9yaWFsIGRlIGNvbnZlcnNhY2lvbmVzIHNpIGVzdMOhIGFjdGl2YWRvICovfVxuICAgICAge21vc3RyYXJIaXN0b3JpYWwgJiYgKFxuICAgICAgICA8Q29udmVyc2F0aW9uSGlzdG9yeVxuICAgICAgICAgIG9uU2VsZWN0Q29udmVyc2F0aW9uPXtjYXJnYXJDb252ZXJzYWNpb259XG4gICAgICAgICAgY29udmVyc2FjaW9uQWN0dWFsSWQ9e2NvbnZlcnNhY2lvbkFjdHVhbElkfVxuICAgICAgICAvPlxuICAgICAgKX1cblxuICAgICAgey8qIENvbnRlbmVkb3IgZGVsIGNoYXQgY29uIGhpc3RvcmlhbCBkZSBtZW5zYWplcyAqL31cbiAgICAgIDxkaXZcbiAgICAgICAgcmVmPXtjaGF0Q29udGFpbmVyUmVmfVxuICAgICAgICBjbGFzc05hbWU9XCJmbGV4LWdyb3cgb3ZlcmZsb3cteS1hdXRvIG1iLTQgcC00IGJvcmRlciByb3VuZGVkLWxnIGJnLWdyYXktNTBcIlxuICAgICAgICBzdHlsZT17eyBoZWlnaHQ6ICdjYWxjKDEwMCUgLSAxODBweCknIH19XG4gICAgICA+XG4gICAgICAgIHttZW5zYWplcy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLWZ1bGwgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgPHA+U2VsZWNjaW9uYSBkb2N1bWVudG9zIHkgaGF6IHVuYSBwcmVndW50YSBwYXJhIGNvbWVuemFyIGxhIGNvbnZlcnNhY2nDs24uPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApIDogKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICB7bWVuc2FqZXMubWFwKChtZW5zYWplLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAga2V5PXttZW5zYWplLmlkIHx8IGluZGV4fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggJHttZW5zYWplLnRpcG8gPT09ICd1c3VhcmlvJyA/ICdqdXN0aWZ5LWVuZCcgOiAnanVzdGlmeS1zdGFydCd9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YG1heC13LVs4MCVdIHAtMyByb3VuZGVkLWxnICR7XG4gICAgICAgICAgICAgICAgICAgIG1lbnNhamUudGlwbyA9PT0gJ3VzdWFyaW8nXG4gICAgICAgICAgICAgICAgICAgICAgPyAnYmctYmx1ZS01MDAgdGV4dC13aGl0ZSByb3VuZGVkLWJyLW5vbmUnXG4gICAgICAgICAgICAgICAgICAgICAgOiAnYmctd2hpdGUgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWJsLW5vbmUnXG4gICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIndoaXRlc3BhY2UtcHJlLXdyYXBcIj57bWVuc2FqZS5jb250ZW5pZG99PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRleHQteHMgbXQtMSB0ZXh0LXJpZ2h0ICR7XG4gICAgICAgICAgICAgICAgICAgICAgbWVuc2FqZS50aXBvID09PSAndXN1YXJpbycgPyAndGV4dC1ibHVlLTEwMCcgOiAndGV4dC1ncmF5LTUwMCdcbiAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXRlYXJGZWNoYShtZW5zYWplLnRpbWVzdGFtcCl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApKX1cblxuICAgICAgICAgICAge2lzTG9hZGluZyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LXN0YXJ0XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBwLTMgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtYmwtbm9uZVwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLWJvdW5jZSBoLTIgdy0yIGJnLWdyYXktNTAwIHJvdW5kZWQtZnVsbFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtYm91bmNlIGgtMiB3LTIgYmctZ3JheS01MDAgcm91bmRlZC1mdWxsXCIgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6ICcwLjJzJyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLWJvdW5jZSBoLTIgdy0yIGJnLWdyYXktNTAwIHJvdW5kZWQtZnVsbFwiIHN0eWxlPXt7IGFuaW1hdGlvbkRlbGF5OiAnMC40cycgfX0+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7Z3VhcmRhbmRvQ29udmVyc2FjaW9uICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgdGV4dC1jZW50ZXIgcHktMVwiPlxuICAgICAgICAgICAgICAgIEd1YXJkYW5kbyBjb252ZXJzYWNpw7NuLi4uXG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRm9ybXVsYXJpbyBwYXJhIGVudmlhciBwcmVndW50YXMgKi99XG4gICAgICA8Zm9ybVxuICAgICAgICBvblN1Ym1pdD17KGUpID0+IHtcbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+TiyBFdmVudG8gb25TdWJtaXQgZGVsIGZvcm11bGFyaW8gZGV0ZWN0YWRvJyk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ/Cfjq8gTGxhbWFuZG8gYSBoYW5kbGVTdWJtaXRGb3JtLi4uJyk7XG4gICAgICAgICAgaGFuZGxlU3VibWl0Rm9ybShvblN1Ym1pdCkoZSk7XG4gICAgICAgIH19XG4gICAgICAgIGNsYXNzTmFtZT1cIm10LWF1dG9cIlxuICAgICAgPlxuICAgICAgICB7ZXJyb3IgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwIHRleHQtc20gbWItMlwiPntlcnJvcn08L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogSW5kaWNhZG9yIGRlIGRvY3VtZW50b3Mgc2VsZWNjaW9uYWRvcyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS02MDAgbWItMlwiPlxuICAgICAgICAgIHtkb2N1bWVudG9zU2VsZWNjaW9uYWRvcy5sZW5ndGggPiAwID8gKFxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi02MDBcIj5cbiAgICAgICAgICAgICAg4pyTIHtkb2N1bWVudG9zU2VsZWNjaW9uYWRvcy5sZW5ndGh9IGRvY3VtZW50b3tkb2N1bWVudG9zU2VsZWNjaW9uYWRvcy5sZW5ndGggIT09IDEgPyAncycgOiAnJ30gc2VsZWNjaW9uYWRve2RvY3VtZW50b3NTZWxlY2Npb25hZG9zLmxlbmd0aCAhPT0gMSA/ICdzJyA6ICcnfVxuICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDBcIj5cbiAgICAgICAgICAgICAg4pqgIE5vIGhheSBkb2N1bWVudG9zIHNlbGVjY2lvbmFkb3MuIFNlbGVjY2lvbmEgYWwgbWVub3MgdW5vIHBhcmEgaGFjZXIgcHJlZ3VudGFzLlxuICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1lbmQgc3BhY2UteC0yXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LWdyb3dcIj5cbiAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICBpZD1cInByZWd1bnRhXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic2hhZG93IGFwcGVhcmFuY2Utbm9uZSBib3JkZXIgcm91bmRlZCB3LWZ1bGwgcHktMiBweC0zIHRleHQtZ3JheS03MDAgbGVhZGluZy10aWdodCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6c2hhZG93LW91dGxpbmVcIlxuICAgICAgICAgICAgICByb3dzPXsyfVxuICAgICAgICAgICAgICB7Li4ucmVnaXN0ZXIoJ3ByZWd1bnRhJyl9XG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRXNjcmliZSB0dSBwcmVndW50YSBzb2JyZSBsb3MgZG9jdW1lbnRvcyBzZWxlY2Npb25hZG9zLi4uXCJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzTG9hZGluZ31cbiAgICAgICAgICAgICAgb25LZXlEb3duPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChlLmtleSA9PT0gJ0VudGVyJyAmJiAhZS5zaGlmdEtleSkge1xuICAgICAgICAgICAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgICAgICAgaGFuZGxlU3VibWl0Rm9ybShvblN1Ym1pdCkoKTsgLy8gRWplY3V0YXIgbGEgZnVuY2nDs24gZGV2dWVsdGFcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAge2Vycm9ycy5wcmVndW50YSAmJiAoXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMCB0ZXh0LXhzIG10LTFcIj57ZXJyb3JzLnByZWd1bnRhLm1lc3NhZ2V9PC9wPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtdC0xXCI+UHJlc2lvbmEgRW50ZXIgcGFyYSBlbnZpYXIsIFNoaWZ0K0VudGVyIHBhcmEgbnVldmEgbMOtbmVhPC9wPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTUwMCBob3ZlcjpiZy1ibHVlLTcwMCB0ZXh0LXdoaXRlIGZvbnQtYm9sZCBweS0yIHB4LTQgcm91bmRlZC1mdWxsIGgtMTAgdy0xMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6c2hhZG93LW91dGxpbmUgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgZGlzYWJsZWQ9e2lzTG9hZGluZyB8fCBkb2N1bWVudG9zU2VsZWNjaW9uYWRvcy5sZW5ndGggPT09IDB9XG4gICAgICAgICAgICB0aXRsZT17ZG9jdW1lbnRvc1NlbGVjY2lvbmFkb3MubGVuZ3RoID09PSAwID8gJ1NlbGVjY2lvbmEgYWwgbWVub3MgdW4gZG9jdW1lbnRvIHBhcmEgaGFjZXIgdW5hIHByZWd1bnRhJyA6ICdFbnZpYXIgcHJlZ3VudGEnfVxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+Wse+4jyBDbGljayBlbiBib3TDs24gZGUgZW52w61vIGRldGVjdGFkbycpO1xuICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UkiBCb3TDs24gZGVzaGFiaWxpdGFkbz8nLCBpc0xvYWRpbmcgfHwgZG9jdW1lbnRvc1NlbGVjY2lvbmFkb3MubGVuZ3RoID09PSAwKTtcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+KPsyBpc0xvYWRpbmc6JywgaXNMb2FkaW5nKTtcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfk4QgZG9jdW1lbnRvcyBzZWxlY2Npb25hZG9zOicsIGRvY3VtZW50b3NTZWxlY2Npb25hZG9zLmxlbmd0aCk7XG4gICAgICAgICAgICB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtpc0xvYWRpbmcgPyAoXG4gICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIGgtNSB3LTUgdGV4dC13aGl0ZVwiIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIiBmaWxsPVwibm9uZVwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICA8Y2lyY2xlIGNsYXNzTmFtZT1cIm9wYWNpdHktMjVcIiBjeD1cIjEyXCIgY3k9XCIxMlwiIHI9XCIxMFwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiNFwiPjwvY2lyY2xlPlxuICAgICAgICAgICAgICAgIDxwYXRoIGNsYXNzTmFtZT1cIm9wYWNpdHktNzVcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgZD1cIk00IDEyYTggOCAwIDAwOC04VjBDNS4zNzMgMCAwIDUuMzczIDAgMTJoNHptMiA1LjI5MUE3Ljk2MiA3Ljk2MiAwIDAxNCAxMkgwYzAgMy4wNDIgMS4xMzUgNS44MjQgMyA3LjkzOGwzLTIuNjQ3elwiPjwvcGF0aD5cbiAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC13aGl0ZVwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XG4gICAgICAgICAgICAgICAgPHBhdGggZD1cIk0xMC44OTQgMi41NTNhMSAxIDAgMDAtMS43ODggMGwtNyAxNGExIDEgMCAwMDEuMTY5IDEuNDA5bDUtMS40MjlBMSAxIDAgMDA5IDE1LjU3MVYxMWExIDEgMCAxMTIgMHY0LjU3MWExIDEgMCAwMC43MjUuOTYybDUgMS40MjhhMSAxIDAgMDAxLjE3LTEuNDA4bC03LTE0elwiPjwvcGF0aD5cbiAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZm9ybT5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlUmVmIiwidXNlRWZmZWN0IiwiY3JlYXJDb252ZXJzYWNpb24iLCJndWFyZGFyTWVuc2FqZSIsIm9idGVuZXJNZW5zYWplc1BvckNvbnZlcnNhY2lvbklkIiwiYWN0dWFsaXphckNvbnZlcnNhY2lvbiIsImFjdGl2YXJDb252ZXJzYWNpb24iLCJvYnRlbmVyQ29udmVyc2FjaW9uQWN0aXZhIiwiZGVzYWN0aXZhclRvZGFzTGFzQ29udmVyc2FjaW9uZXMiLCJDb252ZXJzYXRpb25IaXN0b3J5IiwidXNlRm9ybSIsInpvZFJlc29sdmVyIiwicHJlZ3VudGFGb3JtU2NoZW1hIiwiUXVlc3Rpb25Gb3JtIiwiZG9jdW1lbnRvc1NlbGVjY2lvbmFkb3MiLCJtZW5zYWplcyIsInNldE1lbnNhamVzIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsImNvbnZlcnNhY2lvbkFjdHVhbElkIiwic2V0Q29udmVyc2FjaW9uQWN0dWFsSWQiLCJtb3N0cmFySGlzdG9yaWFsIiwic2V0TW9zdHJhckhpc3RvcmlhbCIsImd1YXJkYW5kb0NvbnZlcnNhY2lvbiIsInNldEd1YXJkYW5kb0NvbnZlcnNhY2lvbiIsImNhcmdhbmRvQ29udmVyc2FjaW9uQWN0aXZhIiwic2V0Q2FyZ2FuZG9Db252ZXJzYWNpb25BY3RpdmEiLCJjaGF0Q29udGFpbmVyUmVmIiwicmVnaXN0ZXIiLCJoYW5kbGVTdWJtaXQiLCJoYW5kbGVTdWJtaXRGb3JtIiwiZm9ybVN0YXRlIiwiZXJyb3JzIiwiaXNWYWxpZCIsInJlc2V0Iiwic2V0VmFsdWUiLCJ3YXRjaCIsInJlc29sdmVyIiwiZGVmYXVsdFZhbHVlcyIsInByZWd1bnRhIiwiZG9jdW1lbnRvcyIsIndhdGNoZWRWYWx1ZXMiLCJjb25zb2xlIiwibG9nIiwidmFsdWVzIiwibGVuZ3RoIiwiZG9jdW1lbnRvc1ZhbGlkYWRvcyIsIm1hcCIsImRvYyIsImRvY1ZhbGlkYWRvIiwiY2F0ZWdvcmlhIiwibnVtZXJvX3RlbWEiLCJ1bmRlZmluZWQiLCJwYXJzZUludCIsImlkIiwiY3JlYWRvX2VuIiwiYWN0dWFsaXphZG9fZW4iLCJ1c2VyX2lkIiwidGlwb19vcmlnaW5hbCIsImNhcmdhckNvbnZlcnNhY2lvbkFjdGl2YSIsImNvbnZlcnNhY2lvbkFjdGl2YSIsImNhcmdhckNvbnZlcnNhY2lvbiIsIndhcm4iLCJjdXJyZW50Iiwic2Nyb2xsVG9wIiwic2Nyb2xsSGVpZ2h0IiwiY29udmVyc2FjaW9uSWQiLCJtZW5zYWplc0RCIiwibWVuc2FqZXNGb3JtYXRlYWRvcyIsIm1zZyIsInRpcG8iLCJjb250ZW5pZG8iLCJ0aW1lc3RhbXAiLCJEYXRlIiwiZ3VhcmRhck1lbnNhamVFbkRCIiwibWVuc2FqZSIsInRpdHVsbyIsInN1YnN0cmluZyIsIm51ZXZvSWQiLCJFcnJvciIsImNvbnZlcnNhY2lvbl9pZCIsImluaWNpYXJOdWV2YUNvbnZlcnNhY2lvbiIsIm9uU3VibWl0IiwiZGF0YSIsInByZWd1bnRhVXN1YXJpbyIsInByZXZNZW5zYWplcyIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJyZXNwdWVzdGFJQSIsImpzb24iLCJyZXNwdWVzdGFUZXh0byIsInJlc3VsdCIsIm1lbnNhamVJQSIsInRpdHVsb01lam9yYWRvIiwibWVuc2FqZUVycm9yIiwibWVzc2FnZSIsImluY2x1ZGVzIiwibWVuc2FqZUVycm9ySUEiLCJkYkVycm9yIiwiZm9ybWF0ZWFyRmVjaGEiLCJmZWNoYSIsInRvTG9jYWxlVGltZVN0cmluZyIsImhvdXIiLCJtaW51dGUiLCJkaXYiLCJjbGFzc05hbWUiLCJidXR0b24iLCJ0eXBlIiwib25DbGljayIsImRpc2FibGVkIiwic3ZnIiwieG1sbnMiLCJ2aWV3Qm94IiwiZmlsbCIsInBhdGgiLCJmaWxsUnVsZSIsImQiLCJjbGlwUnVsZSIsIm9uU2VsZWN0Q29udmVyc2F0aW9uIiwicmVmIiwic3R5bGUiLCJoZWlnaHQiLCJwIiwiaW5kZXgiLCJhbmltYXRpb25EZWxheSIsImZvcm0iLCJlIiwic3BhbiIsInRleHRhcmVhIiwicm93cyIsInBsYWNlaG9sZGVyIiwib25LZXlEb3duIiwia2V5Iiwic2hpZnRLZXkiLCJwcmV2ZW50RGVmYXVsdCIsInRpdGxlIiwiY2lyY2xlIiwiY3giLCJjeSIsInIiLCJzdHJva2UiLCJzdHJva2VXaWR0aCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/QuestionForm.tsx\n"));

/***/ })

});