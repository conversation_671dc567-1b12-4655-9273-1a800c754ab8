"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase/testsService.ts":
/*!******************************************!*\
  !*** ./src/lib/supabase/testsService.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   crearPreguntaTest: () => (/* binding */ crearPreguntaTest),\n/* harmony export */   crearTest: () => (/* binding */ crearTest),\n/* harmony export */   guardarPreguntasTest: () => (/* binding */ guardarPreguntasTest),\n/* harmony export */   obtenerEstadisticasGeneralesTests: () => (/* binding */ obtenerEstadisticasGeneralesTests),\n/* harmony export */   obtenerEstadisticasTest: () => (/* binding */ obtenerEstadisticasTest),\n/* harmony export */   obtenerPreguntasPorTestId: () => (/* binding */ obtenerPreguntasPorTestId),\n/* harmony export */   obtenerPreguntasTestCount: () => (/* binding */ obtenerPreguntasTestCount),\n/* harmony export */   obtenerTestPorId: () => (/* binding */ obtenerTestPorId),\n/* harmony export */   obtenerTests: () => (/* binding */ obtenerTests),\n/* harmony export */   registrarRespuestaTest: () => (/* binding */ registrarRespuestaTest)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./authService */ \"(app-pages-browser)/./src/lib/supabase/authService.ts\");\n\n\n/**\n * Crea un nuevo test\n */ async function crearTest(titulo, descripcion, documentosIds) {\n    try {\n        var _data_, _data_1;\n        console.log('📝 Creando nuevo test:', titulo);\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('❌ No hay usuario autenticado para crear test');\n            return null;\n        }\n        console.log('👤 Usuario autenticado:', user.id);\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('tests').insert([\n            {\n                titulo,\n                descripcion,\n                documentos_ids: documentosIds,\n                user_id: user.id\n            }\n        ]).select();\n        if (error) {\n            console.error('❌ Error al crear test:', error);\n            return null;\n        }\n        console.log('✅ Test creado exitosamente:', data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id);\n        return (data === null || data === void 0 ? void 0 : (_data_1 = data[0]) === null || _data_1 === void 0 ? void 0 : _data_1.id) || null;\n    } catch (error) {\n        console.error('💥 Error inesperado al crear test:', error);\n        return null;\n    }\n}\n/**\n * Obtiene todos los tests del usuario actual\n */ async function obtenerTests() {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return [];\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('tests').select('*').eq('user_id', user.id).order('creado_en', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener tests:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error al obtener tests:', error);\n        return [];\n    }\n}\n/**\n * Obtiene un test por su ID\n */ async function obtenerTestPorId(id) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('tests').select('*').eq('id', id).single();\n    if (error) {\n        console.error('Error al obtener test:', error);\n        return null;\n    }\n    return data;\n}\n/**\n * Crea una nueva pregunta para un test\n */ async function crearPreguntaTest(testId, pregunta, opcionA, opcionB, opcionC, opcionD, respuestaCorrecta) {\n    var _data_;\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('preguntas_test').insert([\n        {\n            test_id: testId,\n            pregunta,\n            opcion_a: opcionA,\n            opcion_b: opcionB,\n            opcion_c: opcionC,\n            opcion_d: opcionD,\n            respuesta_correcta: respuestaCorrecta\n        }\n    ]).select();\n    if (error) {\n        console.error('Error al crear pregunta de test:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n}\n/**\n * Obtiene todas las preguntas de un test\n */ async function obtenerPreguntasPorTestId(testId) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('preguntas_test').select('*').eq('test_id', testId);\n    if (error) {\n        console.error('Error al obtener preguntas de test:', error);\n        return [];\n    }\n    return data || [];\n}\n/**\n * Obtiene el número de preguntas de un test\n */ async function obtenerPreguntasTestCount(testId) {\n    const { count, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('preguntas_test').select('*', {\n        count: 'exact',\n        head: true\n    }).eq('test_id', testId);\n    if (error) {\n        console.error('Error al obtener conteo de preguntas:', error);\n        return 0;\n    }\n    return count || 0;\n}\n/**\n * Guarda múltiples preguntas de test\n */ async function guardarPreguntasTest(preguntas) {\n    const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('preguntas_test').insert(preguntas);\n    if (error) {\n        console.error('Error al guardar preguntas de test:', error);\n        return false;\n    }\n    return true;\n}\n/**\n * Registra la respuesta de un usuario a una pregunta de test\n */ async function registrarRespuestaTest(testId, preguntaId, respuestaUsuario, esCorrecta) {\n    const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('estadisticas_test').insert([\n        {\n            test_id: testId,\n            pregunta_id: preguntaId,\n            respuesta_usuario: respuestaUsuario,\n            es_correcta: esCorrecta,\n            fecha_respuesta: new Date().toISOString()\n        }\n    ]);\n    if (error) {\n        console.error('Error al registrar respuesta de test:', error);\n        return false;\n    }\n    return true;\n}\n/**\n * Obtiene estadísticas generales de todos los tests\n */ async function obtenerEstadisticasGeneralesTests() {\n    // Obtener todas las respuestas\n    const { data: respuestas, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('estadisticas_test').select('*');\n    if (error) {\n        console.error('Error al obtener estadísticas de tests:', error);\n        return {\n            totalTests: 0,\n            totalPreguntas: 0,\n            totalRespuestasCorrectas: 0,\n            totalRespuestasIncorrectas: 0,\n            porcentajeAcierto: 0\n        };\n    }\n    // Obtener tests únicos respondidos\n    const testsUnicos = new Set((respuestas === null || respuestas === void 0 ? void 0 : respuestas.map((r)=>r.test_id)) || []);\n    // Obtener preguntas únicas respondidas\n    const preguntasUnicas = new Set((respuestas === null || respuestas === void 0 ? void 0 : respuestas.map((r)=>r.pregunta_id)) || []);\n    // Contar respuestas correctas e incorrectas\n    const correctas = (respuestas === null || respuestas === void 0 ? void 0 : respuestas.filter((r)=>r.es_correcta).length) || 0;\n    const incorrectas = ((respuestas === null || respuestas === void 0 ? void 0 : respuestas.length) || 0) - correctas;\n    // Calcular porcentaje de acierto\n    const porcentaje = respuestas && respuestas.length > 0 ? Math.round(correctas / respuestas.length * 100) : 0;\n    return {\n        totalTests: testsUnicos.size,\n        totalPreguntas: preguntasUnicas.size,\n        totalRespuestasCorrectas: correctas,\n        totalRespuestasIncorrectas: incorrectas,\n        porcentajeAcierto: porcentaje\n    };\n}\n/**\n * Obtiene estadísticas detalladas de un test específico\n */ async function obtenerEstadisticasTest(testId) {\n    // Obtener todas las respuestas del test\n    const { data: respuestas, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('estadisticas_test').select('*').eq('test_id', testId);\n    if (error) {\n        console.error('Error al obtener estadísticas del test:', error);\n        return {\n            totalPreguntas: 0,\n            totalCorrectas: 0,\n            totalIncorrectas: 0,\n            porcentajeAcierto: 0,\n            fechasRealizacion: [],\n            preguntasMasFalladas: []\n        };\n    }\n    // Obtener preguntas del test\n    const { data: preguntas } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('preguntas_test').select('*').eq('test_id', testId);\n    // Contar respuestas correctas e incorrectas\n    const correctas = (respuestas === null || respuestas === void 0 ? void 0 : respuestas.filter((r)=>r.es_correcta).length) || 0;\n    const incorrectas = ((respuestas === null || respuestas === void 0 ? void 0 : respuestas.length) || 0) - correctas;\n    // Calcular porcentaje de acierto\n    const porcentaje = respuestas && respuestas.length > 0 ? Math.round(correctas / respuestas.length * 100) : 0;\n    // Obtener fechas únicas de realización\n    const fechasSet = new Set();\n    respuestas === null || respuestas === void 0 ? void 0 : respuestas.forEach((r)=>{\n        const fecha = new Date(r.fecha_respuesta);\n        fechasSet.add(\"\".concat(fecha.getDate(), \"/\").concat(fecha.getMonth() + 1, \"/\").concat(fecha.getFullYear()));\n    });\n    const fechasUnicas = Array.from(fechasSet);\n    // Calcular preguntas más falladas\n    const fallosPorPregunta = new Map();\n    respuestas === null || respuestas === void 0 ? void 0 : respuestas.forEach((respuesta)=>{\n        const actual = fallosPorPregunta.get(respuesta.pregunta_id) || {\n            fallos: 0,\n            aciertos: 0\n        };\n        if (respuesta.es_correcta) {\n            actual.aciertos++;\n        } else {\n            actual.fallos++;\n        }\n        fallosPorPregunta.set(respuesta.pregunta_id, actual);\n    });\n    // Convertir a array y ordenar por fallos\n    const preguntasFalladas = Array.from(fallosPorPregunta.entries()).map((param)=>{\n        let [id, stats] = param;\n        var _preguntas_find;\n        return {\n            preguntaId: id,\n            totalFallos: stats.fallos,\n            totalAciertos: stats.aciertos,\n            // Encontrar la pregunta correspondiente\n            pregunta: (preguntas === null || preguntas === void 0 ? void 0 : (_preguntas_find = preguntas.find((p)=>p.id === id)) === null || _preguntas_find === void 0 ? void 0 : _preguntas_find.pregunta) || 'Desconocida'\n        };\n    }).sort((a, b)=>b.totalFallos - a.totalFallos).slice(0, 5); // Tomar las 5 más falladas\n    return {\n        totalPreguntas: (preguntas === null || preguntas === void 0 ? void 0 : preguntas.length) || 0,\n        totalCorrectas: correctas,\n        totalIncorrectas: incorrectas,\n        porcentajeAcierto: porcentaje,\n        fechasRealizacion: fechasUnicas,\n        preguntasMasFalladas: preguntasFalladas\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/testsService.ts\n"));

/***/ })

});