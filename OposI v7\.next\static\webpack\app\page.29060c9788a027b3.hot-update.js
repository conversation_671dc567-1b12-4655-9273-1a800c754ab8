"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/flashcards/FlashcardCollectionList.tsx":
/*!***************************************************************!*\
  !*** ./src/components/flashcards/FlashcardCollectionList.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiInbox_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiInbox,FiRefreshCw,FiTrash2!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n\n\n\nconst FlashcardCollectionList = (param)=>{\n    let { colecciones, coleccionSeleccionada, onSeleccionarColeccion, onEliminarColeccion, isLoading, deletingId } = param;\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-40\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (colecciones.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center p-8 border-2 border-dashed border-gray-300 rounded-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiInbox_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiInbox, {\n                    className: \"mx-auto text-6xl text-gray-400 mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500 text-lg\",\n                    children: \"No hay colecciones de flashcards disponibles.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-400 mt-1\",\n                    children: \"Crea una nueva colecci\\xf3n para empezar a estudiar.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, undefined);\n    }\n    const handleEliminarClick = (e, coleccionId)=>{\n        e.stopPropagation(); // Evitar que se seleccione la colección al hacer clic en eliminar\n        onEliminarColeccion(coleccionId);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6\",\n        children: colecciones.map((coleccion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border rounded-lg p-4 cursor-pointer transition-all flex flex-col justify-between relative \".concat((coleccionSeleccionada === null || coleccionSeleccionada === void 0 ? void 0 : coleccionSeleccionada.id) === coleccion.id ? 'border-orange-500 bg-orange-50 shadow-lg' : 'border-gray-200 hover:border-orange-300 hover:bg-orange-50/50 hover:shadow-md'),\n                onClick: ()=>onSeleccionarColeccion(coleccion),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: (e)=>handleEliminarClick(e, coleccion.id),\n                        disabled: deletingId === coleccion.id,\n                        className: \"absolute top-2 right-2 p-1 text-red-500 hover:bg-red-50 rounded transition-colors disabled:opacity-50 z-10\",\n                        title: \"Eliminar colecci\\xf3n\",\n                        children: deletingId === coleccion.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiInbox_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiRefreshCw, {\n                            size: 16,\n                            className: \"animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiInbox_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiTrash2, {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-lg mb-1 pr-8\",\n                                children: coleccion.titulo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, undefined),\n                            coleccion.descripcion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-2 break-words\",\n                                children: coleccion.descripcion\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 mt-2\",\n                                children: [\n                                    \"Flashcards: \",\n                                    typeof coleccion.numero_flashcards === 'number' ? coleccion.numero_flashcards : 'N/A'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-400\",\n                                children: [\n                                    \"Creada: \",\n                                    new Date(coleccion.creado_en).toLocaleDateString()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, coleccion.id, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                lineNumber: 48,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, undefined);\n};\n_c = FlashcardCollectionList;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlashcardCollectionList);\nvar _c;\n$RefreshReg$(_c, \"FlashcardCollectionList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/flashcards/FlashcardCollectionList.tsx\n"));

/***/ })

});