{"version": 3, "file": "Delimiters.js", "sourceRoot": "", "sources": ["../../../src/core/syntax/Delimiters.ts"], "names": [], "mappings": "AAAA,OAAO,SAAS,oBAAkC;AAElD,MAAM,CAAC,IAAM,WAAW,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;AAE/C,WAAW,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AACrC,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACtC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACpC,WAAW,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AACvC,WAAW,CAAC,SAAS,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;AAC7C,WAAW,CAAC,SAAS,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAC9C,WAAW,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AACrC,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACtC,WAAW,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACxC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC"}