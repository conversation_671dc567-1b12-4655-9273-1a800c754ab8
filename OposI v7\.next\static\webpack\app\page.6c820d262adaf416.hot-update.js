"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/formSchemas.ts":
/*!********************************!*\
  !*** ./src/lib/formSchemas.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   flashcardFormSchema: () => (/* binding */ flashcardFormSchema),\n/* harmony export */   generatorFormSchema: () => (/* binding */ generatorFormSchema),\n/* harmony export */   mindMapFormSchema: () => (/* binding */ mindMapFormSchema),\n/* harmony export */   preguntaFormSchema: () => (/* binding */ preguntaFormSchema),\n/* harmony export */   testFormSchema: () => (/* binding */ testFormSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/dist/esm/index.js\");\n\nconst preguntaFormSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    pregunta: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'La pregunta es obligatoria').max(500, 'Máximo 500 caracteres'),\n    documentos: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n        titulo: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1),\n        contenido: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1),\n        categoria: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional().nullable(),\n        numero_tema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n            zod__WEBPACK_IMPORTED_MODULE_0__.z.number().int().positive(),\n            zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n            zod__WEBPACK_IMPORTED_MODULE_0__.z[\"null\"](),\n            zod__WEBPACK_IMPORTED_MODULE_0__.z.undefined()\n        ]).optional(),\n        creado_en: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n        actualizado_en: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n        user_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n        tipo_original: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional()\n    })).min(1, 'Debes seleccionar al menos un documento')\n});\n// Esquema simplificado para los generadores que solo necesitan la petición\nconst generatorFormSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    peticion: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'La petición es obligatoria').max(500, 'Máximo 500 caracteres')\n});\nconst testFormSchema = generatorFormSchema;\nconst flashcardFormSchema = generatorFormSchema;\nconst mindMapFormSchema = generatorFormSchema;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/formSchemas.ts\n"));

/***/ })

});