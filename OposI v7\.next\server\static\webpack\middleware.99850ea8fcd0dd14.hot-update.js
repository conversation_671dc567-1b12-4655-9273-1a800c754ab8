"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(middleware)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1__);\n// src/middleware.ts\n\n\nasync function middleware(req) {\n    try {\n        // IMPORTANTE: Crear una respuesta base que Supabase puede modificar para añadir cookies\n        const res = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n        // IMPORTANTE: Inicializar el cliente Supabase para el middleware con req y res\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1__.createMiddlewareClient)({\n            req,\n            res\n        });\n        // IMPORTANTE: Obtener la sesión. Esto también refrescará el token si es necesario\n        // y actualizará las cookies en `res` si hay cambios.\n        const { data: { session } } = await supabase.auth.getSession();\n        const requestedPath = req.nextUrl.pathname;\n        // Verificar si hay una cookie de token de Supabase\n        const cookieHeader = req.headers.get('cookie') || '';\n        const hasSupabaseCookie = cookieHeader.includes('supabase.auth.token');\n        // Para dispositivos móviles, ser más permisivo con la detección de sesión\n        // Verificar también headers adicionales que pueden indicar una sesión activa\n        const authHeader = req.headers.get('authorization');\n        const hasAuthHeader = !!authHeader && authHeader.startsWith('Bearer ');\n        // Determinar si hay una sesión basada en múltiples fuentes\n        const hasSession = !!session || hasSupabaseCookie || hasAuthHeader;\n        // Definir rutas públicas que no requieren autenticación\n        const publicPaths = [\n            '/login'\n        ];\n        // Excluir rutas de API o recursos estáticos\n        if (requestedPath.startsWith('/api/') || requestedPath.startsWith('/_next/')) {\n            return res;\n        }\n        // --- LÓGICA DE AUTENTICACIÓN Y REDIRECCIÓN ---\n        // CASO 1: Usuario autenticado intentando acceder a /login\n        if (hasSession && requestedPath === '/login') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/', req.url));\n        }\n        // CASO 2: Usuario no autenticado intentando acceder a una ruta protegida\n        if (!hasSession && !publicPaths.includes(requestedPath)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/login', req.url));\n        }\n        // CASO 3: Permitir el acceso en todos los demás casos\n        // IMPORTANTE: Devolver la respuesta que puede tener cookies actualizadas\n        return res;\n    } catch (error) {\n        console.error('Error en middleware:', error);\n        // En caso de error, permitir el acceso pero sin cookies actualizadas\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n}\n// Configuración del matcher para definir en qué rutas se ejecutará el middleware.\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     *\n     * También puedes excluir rutas API específicas aquí si prefieres no hacerlo en el código del middleware,\n     * pero manejarlo en el código da más flexibilidad si algunas rutas API necesitan auth y otras no.\n     * Si todas las rutas /api/* están protegidas o no, puedes gestionarlo arriba.\n     *\n     * La expresión regular abajo intenta cubrir los casos más comunes:\n     */ '/((?!_next/static|_next/image|favicon.ico|manifest.json|robots.txt|.*\\\\..*).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});