"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/flashcards/FlashcardViewer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/flashcards/FlashcardViewer.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _barrel_optimize_names_FiAlertTriangle_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlertTriangle,FiEdit2,FiRefreshCw,FiTrash2!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./FlashcardCollectionList */ \"(app-pages-browser)/./src/components/flashcards/FlashcardCollectionList.tsx\");\n/* harmony import */ var _FlashcardStatistics__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FlashcardStatistics */ \"(app-pages-browser)/./src/components/flashcards/FlashcardStatistics.tsx\");\n/* harmony import */ var _FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./FlashcardStudyMode */ \"(app-pages-browser)/./src/components/flashcards/FlashcardStudyMode.tsx\");\n/* harmony import */ var _FlashcardEditModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./FlashcardEditModal */ \"(app-pages-browser)/./src/components/flashcards/FlashcardEditModal.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst FlashcardViewer = ()=>{\n    _s();\n    // Estado para las colecciones\n    const [colecciones, setColecciones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coleccionSeleccionada, setColeccionSeleccionada] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para las flashcards\n    const [flashcards, setFlashcards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [mostrarRespuesta, setMostrarRespuesta] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [respondiendo, setRespondiendo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estado para el modo de estudio\n    const [modoEstudio, setModoEstudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estado para estadísticas\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para carga y errores\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Estado para edición y eliminación de flashcards\n    const [flashcardEditando, setFlashcardEditando] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDeleteConfirm, setShowDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para eliminación de colecciones\n    const [showDeleteCollectionConfirm, setShowDeleteCollectionConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletingCollectionId, setDeletingCollectionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Cargar colecciones al montar el componente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashcardViewer.useEffect\": ()=>{\n            const cargarColecciones = {\n                \"FlashcardViewer.useEffect.cargarColecciones\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n                        setColecciones(data);\n                    } catch (error) {\n                        console.error('Error al cargar colecciones:', error);\n                        setError('No se pudieron cargar las colecciones de flashcards');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"FlashcardViewer.useEffect.cargarColecciones\"];\n            cargarColecciones();\n        }\n    }[\"FlashcardViewer.useEffect\"], []);\n    // Manejar la selección de una colección\n    const handleSeleccionarColeccion = async (coleccion)=>{\n        setIsLoading(true);\n        setError('');\n        setColeccionSeleccionada(coleccion);\n        setActiveIndex(0);\n        setMostrarRespuesta(false);\n        setRespondiendo(false);\n        setModoEstudio(false);\n        try {\n            // Cargar flashcards con su progreso\n            const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccion.id);\n            // Ordenar las flashcards: primero las que deben estudiarse hoy, luego el resto\n            const ordenadas = [\n                ...data\n            ].sort((a, b)=>{\n                if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                return 0;\n            });\n            setFlashcards(ordenadas);\n            // Cargar estadísticas\n            const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccion.id);\n            setEstadisticas(stats);\n        } catch (error) {\n            console.error('Error al cargar flashcards:', error);\n            setError('No se pudieron cargar las flashcards de esta colección');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Iniciar el modo de estudio\n    const iniciarModoEstudio = async ()=>{\n        setIsLoading(true);\n        try {\n            if (coleccionSeleccionada) {\n                // Recargar las flashcards para asegurarnos de tener los datos más recientes\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                // Filtrar solo las flashcards que deben estudiarse hoy\n                const flashcardsParaEstudiar = data.filter((flashcard)=>flashcard.debeEstudiar);\n                // Actualizar estadísticas\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n                // Verificar si el número de flashcards para estudiar coincide con las estadísticas\n                if (flashcardsParaEstudiar.length !== stats.paraHoy) {\n                    console.warn(\"Discrepancia en el conteo: \".concat(flashcardsParaEstudiar.length, \" flashcards filtradas vs \").concat(stats.paraHoy, \" en estad\\xedsticas\"));\n                }\n                // Si no hay flashcards para hoy, mostrar un mensaje\n                if (flashcardsParaEstudiar.length === 0) {\n                    if (data.length === 0) {\n                        alert('No hay flashcards en esta colección.');\n                    } else {\n                        alert('No hay flashcards programadas para estudiar hoy. Vuelve mañana o ajusta el progreso de las tarjetas.');\n                    }\n                    return; // Salir sin iniciar el modo estudio\n                }\n                // Usar solo las flashcards programadas para hoy\n                setFlashcards(flashcardsParaEstudiar);\n                // Iniciar el modo de estudio\n                setModoEstudio(true);\n                setActiveIndex(0);\n                setMostrarRespuesta(false);\n                setRespondiendo(false);\n            }\n        } catch (error) {\n            console.error('Error al iniciar modo de estudio:', error);\n            setError('No se pudo iniciar el modo de estudio');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Manejar la navegación entre flashcards\n    const handleNavigate = (direction)=>{\n        if (direction === 'prev' && activeIndex > 0) {\n            setActiveIndex(activeIndex - 1);\n            setMostrarRespuesta(false);\n        } else if (direction === 'next' && activeIndex < flashcards.length - 1) {\n            setActiveIndex(activeIndex + 1);\n            setMostrarRespuesta(false);\n        }\n    };\n    // Manejar la respuesta a una flashcard\n    const handleRespuesta = async (dificultad)=>{\n        if (!coleccionSeleccionada || flashcards.length === 0) return;\n        const flashcardId = flashcards[activeIndex].id;\n        setRespondiendo(true);\n        try {\n            // Registrar la respuesta\n            await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.registrarRespuestaFlashcard)(flashcardId, dificultad);\n            // Recargar las flashcards y estadísticas si estamos en la última tarjeta\n            if (activeIndex >= flashcards.length - 1 && coleccionSeleccionada) {\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                const flashcardsParaEstudiar = data.filter((flashcard)=>flashcard.debeEstudiar);\n                // Actualizar estadísticas\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n                if (flashcardsParaEstudiar.length > 0) {\n                    setFlashcards(flashcardsParaEstudiar);\n                    setActiveIndex(0);\n                } else {\n                    // Si no hay más flashcards para hoy, mostrar mensaje y salir del modo de estudio\n                    alert('¡Has completado todas las flashcards para hoy! Vuelve mañana para continuar estudiando.');\n                    setModoEstudio(false);\n                    // Ordenar las flashcards: primero las que deben estudiarse (aunque ya no haya ninguna), luego el resto\n                    const ordenadas = [\n                        ...data\n                    ].sort((a, b)=>{\n                        if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                        if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                        return 0;\n                    });\n                    setFlashcards(ordenadas);\n                }\n            } else {\n                // Avanzar a la siguiente flashcard\n                handleNavigate('next');\n            }\n        } catch (error) {\n            console.error('Error al registrar respuesta:', error);\n            setError('No se pudo registrar la respuesta');\n        } finally{\n            setRespondiendo(false);\n            setMostrarRespuesta(false);\n        }\n    };\n    // Salir del modo de estudio\n    const handleSalirModoEstudio = ()=>{\n        setModoEstudio(false);\n    };\n    // Manejar la edición de una flashcard\n    const handleEditarFlashcard = (flashcard)=>{\n        setFlashcardEditando(flashcard);\n        setShowEditModal(true);\n    };\n    // Manejar el guardado de una flashcard editada\n    const handleGuardarFlashcard = (flashcardActualizada)=>{\n        // Actualizar la flashcard en la lista local\n        setFlashcards((prev)=>prev.map((fc)=>fc.id === flashcardActualizada.id ? flashcardActualizada : fc));\n    };\n    // Manejar la eliminación de una flashcard\n    const handleEliminarFlashcard = async (flashcardId)=>{\n        setDeletingId(flashcardId);\n        let loadingToastId;\n        try {\n            loadingToastId = react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].loading('Eliminando flashcard...');\n            const success = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.eliminarFlashcard)(flashcardId);\n            if (success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success('Flashcard eliminada exitosamente', {\n                    id: loadingToastId\n                });\n                // Actualizar la lista local\n                setFlashcards((prev)=>prev.filter((fc)=>fc.id !== flashcardId));\n                // Recargar estadísticas\n                if (coleccionSeleccionada) {\n                    const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                    setEstadisticas(stats);\n                }\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error('Error al eliminar la flashcard', {\n                    id: loadingToastId\n                });\n            }\n        } catch (error) {\n            console.error('Error al eliminar flashcard:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error('Error al eliminar la flashcard', {\n                id: loadingToastId\n            });\n        } finally{\n            setDeletingId(null);\n            setShowDeleteConfirm(null);\n        }\n    };\n    // Manejar la eliminación de una colección completa\n    const handleEliminarColeccion = (coleccionId)=>{\n        setShowDeleteCollectionConfirm(coleccionId);\n    };\n    const confirmarEliminarColeccion = async (coleccionId)=>{\n        setDeletingCollectionId(coleccionId);\n        let loadingToastId;\n        try {\n            loadingToastId = react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].loading('Eliminando colección...');\n            const success = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.eliminarColeccionFlashcards)(coleccionId);\n            if (success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success('Colección eliminada exitosamente', {\n                    id: loadingToastId\n                });\n                // Actualizar la lista local de colecciones\n                setColecciones((prev)=>prev.filter((col)=>col.id !== coleccionId));\n                // Si la colección eliminada era la seleccionada, limpiar la selección\n                if ((coleccionSeleccionada === null || coleccionSeleccionada === void 0 ? void 0 : coleccionSeleccionada.id) === coleccionId) {\n                    setColeccionSeleccionada(null);\n                    setFlashcards([]);\n                    setEstadisticas(null);\n                }\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error('Error al eliminar la colección', {\n                    id: loadingToastId\n                });\n            }\n        } catch (error) {\n            console.error('Error al eliminar colección:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error('Error al eliminar la colección', {\n                id: loadingToastId\n            });\n        } finally{\n            setDeletingCollectionId(null);\n            setShowDeleteCollectionConfirm(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 307,\n                columnNumber: 9\n            }, undefined),\n            modoEstudio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                flashcards: flashcards,\n                activeIndex: activeIndex,\n                respondiendo: respondiendo,\n                onRespuesta: handleRespuesta,\n                onNavigate: handleNavigate,\n                onVolver: handleSalirModoEstudio\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 313,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"Mis Flashcards\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        colecciones: colecciones,\n                        coleccionSeleccionada: coleccionSeleccionada,\n                        onSeleccionarColeccion: handleSeleccionarColeccion,\n                        onEliminarColeccion: handleEliminarColeccion,\n                        isLoading: isLoading,\n                        deletingId: deletingCollectionId\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 11\n                    }, undefined),\n                    coleccionSeleccionada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: coleccionSeleccionada.titulo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardStatistics__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                estadisticas: estadisticas\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: iniciarModoEstudio,\n                                            className: \"bg-orange-500 hover:bg-orange-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                                            children: \"Estudiar (\".concat(estadisticas ? estadisticas.paraHoy : 0, \" para hoy)\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{},\n                                            className: \"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                                            children: \"Ver estad\\xedsticas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, undefined),\n                            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center items-center h-40\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 17\n                            }, undefined) : flashcards.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"No hay flashcards en esta colecci\\xf3n.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: flashcards.map((flashcard, index)=>{\n                                    var _flashcard_progreso, _flashcard_progreso1;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4 \".concat(flashcard.debeEstudiar ? 'border-orange-300 bg-orange-50' : 'border-gray-200'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            \"Tarjeta \",\n                                                            index + 1\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            ((_flashcard_progreso = flashcard.progreso) === null || _flashcard_progreso === void 0 ? void 0 : _flashcard_progreso.estado) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full text-xs \".concat(flashcard.progreso.estado === 'nuevo' ? 'bg-blue-100 text-blue-800' : flashcard.progreso.estado === 'aprendiendo' ? 'bg-yellow-100 text-yellow-800' : flashcard.progreso.estado === 'repasando' ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'),\n                                                                children: flashcard.progreso.estado\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            !((_flashcard_progreso1 = flashcard.progreso) === null || _flashcard_progreso1 === void 0 ? void 0 : _flashcard_progreso1.estado) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs\",\n                                                                children: \"nuevo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleEditarFlashcard(flashcard),\n                                                                        className: \"p-1 text-blue-500 hover:bg-blue-50 rounded transition-colors\",\n                                                                        title: \"Editar flashcard\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiEdit2, {\n                                                                            size: 14\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                            lineNumber: 409,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                        lineNumber: 404,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setShowDeleteConfirm(flashcard.id),\n                                                                        disabled: deletingId === flashcard.id,\n                                                                        className: \"p-1 text-red-500 hover:bg-red-50 rounded transition-colors disabled:opacity-50\",\n                                                                        title: \"Eliminar flashcard\",\n                                                                        children: deletingId === flashcard.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiRefreshCw, {\n                                                                            size: 14,\n                                                                            className: \"animate-spin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                            lineNumber: 418,\n                                                                            columnNumber: 33\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiTrash2, {\n                                                                            size: 14\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                            lineNumber: 420,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                        lineNumber: 411,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-2\",\n                                                children: flashcard.pregunta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-700 line-clamp-2\",\n                                                children: flashcard.respuesta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, flashcard.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 21\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 322,\n                columnNumber: 9\n            }, undefined),\n            flashcardEditando && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardEditModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                flashcard: flashcardEditando,\n                isOpen: showEditModal,\n                onClose: ()=>{\n                    setShowEditModal(false);\n                    setFlashcardEditando(null);\n                },\n                onSave: handleGuardarFlashcard\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 439,\n                columnNumber: 9\n            }, undefined),\n            showDeleteConfirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 max-w-md w-full mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiAlertTriangle, {\n                                    className: \"text-red-500 mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"Confirmar eliminaci\\xf3n\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"\\xbfEst\\xe1s seguro de que quieres eliminar esta flashcard? Esta acci\\xf3n no se puede deshacer y se perder\\xe1 todo el progreso asociado.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                            lineNumber: 459,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowDeleteConfirm(null),\n                                    className: \"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\",\n                                    disabled: deletingId !== null,\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleEliminarFlashcard(showDeleteConfirm),\n                                    className: \"px-4 py-2 bg-red-500 text-white hover:bg-red-600 rounded-lg transition-colors\",\n                                    disabled: deletingId !== null,\n                                    children: \"Eliminar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                    lineNumber: 453,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 452,\n                columnNumber: 9\n            }, undefined),\n            showDeleteCollectionConfirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 max-w-md w-full mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiAlertTriangle, {\n                                    className: \"text-red-500 mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"Confirmar eliminaci\\xf3n de colecci\\xf3n\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                            lineNumber: 487,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"\\xbfEst\\xe1s seguro de que quieres eliminar esta colecci\\xf3n completa? Esta acci\\xf3n no se puede deshacer y se eliminar\\xe1n:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"text-gray-600 mb-6 list-disc list-inside space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Todas las flashcards de la colecci\\xf3n\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Todo el progreso de estudio\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"El historial de revisiones\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Las estad\\xedsticas asociadas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowDeleteCollectionConfirm(null),\n                                    className: \"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\",\n                                    disabled: deletingCollectionId !== null,\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>confirmarEliminarColeccion(showDeleteCollectionConfirm),\n                                    className: \"px-4 py-2 bg-red-500 text-white hover:bg-red-600 rounded-lg transition-colors\",\n                                    disabled: deletingCollectionId !== null,\n                                    children: \"Eliminar colecci\\xf3n\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                    lineNumber: 486,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 485,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n        lineNumber: 305,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlashcardViewer, \"+HTyYSOPk7h+QHp9CprRrCNOQms=\");\n_c = FlashcardViewer;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlashcardViewer);\nvar _c;\n$RefreshReg$(_c, \"FlashcardViewer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2ZsYXNoY2FyZHMvRmxhc2hjYXJkVmlld2VyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQW1EO0FBVzNCO0FBQ3lEO0FBQ2pCO0FBQ1I7QUFDRjtBQUNBO0FBQ2xCO0FBRXBDLE1BQU1rQixrQkFBNEI7O0lBQ2hDLDhCQUE4QjtJQUM5QixNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBR25CLCtDQUFRQSxDQUF3QixFQUFFO0lBQ3hFLE1BQU0sQ0FBQ29CLHVCQUF1QkMseUJBQXlCLEdBQUdyQiwrQ0FBUUEsQ0FBNkI7SUFFL0YsNkJBQTZCO0lBQzdCLE1BQU0sQ0FBQ3NCLFlBQVlDLGNBQWMsR0FBR3ZCLCtDQUFRQSxDQUF5QixFQUFFO0lBQ3ZFLE1BQU0sQ0FBQ3dCLGFBQWFDLGVBQWUsR0FBR3pCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQzBCLGtCQUFrQkMsb0JBQW9CLEdBQUczQiwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUM0QixjQUFjQyxnQkFBZ0IsR0FBRzdCLCtDQUFRQSxDQUFDO0lBRWpELGlDQUFpQztJQUNqQyxNQUFNLENBQUM4QixhQUFhQyxlQUFlLEdBQUcvQiwrQ0FBUUEsQ0FBQztJQUUvQywyQkFBMkI7SUFDM0IsTUFBTSxDQUFDZ0MsY0FBY0MsZ0JBQWdCLEdBQUdqQywrQ0FBUUEsQ0FBTTtJQUV0RCw4QkFBOEI7SUFDOUIsTUFBTSxDQUFDa0MsV0FBV0MsYUFBYSxHQUFHbkMsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDb0MsT0FBT0MsU0FBUyxHQUFHckMsK0NBQVFBLENBQUM7SUFFbkMsa0RBQWtEO0lBQ2xELE1BQU0sQ0FBQ3NDLG1CQUFtQkMscUJBQXFCLEdBQUd2QywrQ0FBUUEsQ0FBOEI7SUFDeEYsTUFBTSxDQUFDd0MsZUFBZUMsaUJBQWlCLEdBQUd6QywrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUMwQyxtQkFBbUJDLHFCQUFxQixHQUFHM0MsK0NBQVFBLENBQWdCO0lBQzFFLE1BQU0sQ0FBQzRDLFlBQVlDLGNBQWMsR0FBRzdDLCtDQUFRQSxDQUFnQjtJQUU1RCx5Q0FBeUM7SUFDekMsTUFBTSxDQUFDOEMsNkJBQTZCQywrQkFBK0IsR0FBRy9DLCtDQUFRQSxDQUFnQjtJQUM5RixNQUFNLENBQUNnRCxzQkFBc0JDLHdCQUF3QixHQUFHakQsK0NBQVFBLENBQWdCO0lBRWhGLDZDQUE2QztJQUM3Q0MsZ0RBQVNBO3FDQUFDO1lBQ1IsTUFBTWlEOytEQUFvQjtvQkFDeEJmLGFBQWE7b0JBQ2IsSUFBSTt3QkFDRixNQUFNZ0IsT0FBTyxNQUFNakQsMkVBQTRCQTt3QkFDL0NpQixlQUFlZ0M7b0JBQ2pCLEVBQUUsT0FBT2YsT0FBTzt3QkFDZGdCLFFBQVFoQixLQUFLLENBQUMsZ0NBQWdDQTt3QkFDOUNDLFNBQVM7b0JBQ1gsU0FBVTt3QkFDUkYsYUFBYTtvQkFDZjtnQkFDRjs7WUFFQWU7UUFDRjtvQ0FBRyxFQUFFO0lBRUwsd0NBQXdDO0lBQ3hDLE1BQU1HLDZCQUE2QixPQUFPQztRQUN4Q25CLGFBQWE7UUFDYkUsU0FBUztRQUNUaEIseUJBQXlCaUM7UUFDekI3QixlQUFlO1FBQ2ZFLG9CQUFvQjtRQUNwQkUsZ0JBQWdCO1FBQ2hCRSxlQUFlO1FBRWYsSUFBSTtZQUNGLG9DQUFvQztZQUNwQyxNQUFNb0IsT0FBTyxNQUFNaEQsNEVBQTZCQSxDQUFDbUQsVUFBVUMsRUFBRTtZQUU3RCwrRUFBK0U7WUFDL0UsTUFBTUMsWUFBWTttQkFBSUw7YUFBSyxDQUFDTSxJQUFJLENBQUMsQ0FBQ0MsR0FBR0M7Z0JBQ25DLElBQUlELEVBQUVFLFlBQVksSUFBSSxDQUFDRCxFQUFFQyxZQUFZLEVBQUUsT0FBTyxDQUFDO2dCQUMvQyxJQUFJLENBQUNGLEVBQUVFLFlBQVksSUFBSUQsRUFBRUMsWUFBWSxFQUFFLE9BQU87Z0JBQzlDLE9BQU87WUFDVDtZQUVBckMsY0FBY2lDO1lBRWQsc0JBQXNCO1lBQ3RCLE1BQU1LLFFBQVEsTUFBTXhELDJFQUE0QkEsQ0FBQ2lELFVBQVVDLEVBQUU7WUFDN0R0QixnQkFBZ0I0QjtRQUNsQixFQUFFLE9BQU96QixPQUFPO1lBQ2RnQixRQUFRaEIsS0FBSyxDQUFDLCtCQUErQkE7WUFDN0NDLFNBQVM7UUFDWCxTQUFVO1lBQ1JGLGFBQWE7UUFDZjtJQUNGO0lBRUEsNkJBQTZCO0lBQzdCLE1BQU0yQixxQkFBcUI7UUFDekIzQixhQUFhO1FBRWIsSUFBSTtZQUNGLElBQUlmLHVCQUF1QjtnQkFDekIsNEVBQTRFO2dCQUM1RSxNQUFNK0IsT0FBTyxNQUFNaEQsNEVBQTZCQSxDQUFDaUIsc0JBQXNCbUMsRUFBRTtnQkFFekUsdURBQXVEO2dCQUN2RCxNQUFNUSx5QkFBeUJaLEtBQUthLE1BQU0sQ0FBQ0MsQ0FBQUEsWUFBYUEsVUFBVUwsWUFBWTtnQkFFOUUsMEJBQTBCO2dCQUMxQixNQUFNQyxRQUFRLE1BQU14RCwyRUFBNEJBLENBQUNlLHNCQUFzQm1DLEVBQUU7Z0JBQ3pFdEIsZ0JBQWdCNEI7Z0JBRWhCLG1GQUFtRjtnQkFDbkYsSUFBSUUsdUJBQXVCRyxNQUFNLEtBQUtMLE1BQU1NLE9BQU8sRUFBRTtvQkFDbkRmLFFBQVFnQixJQUFJLENBQUMsOEJBQXVGUCxPQUF6REUsdUJBQXVCRyxNQUFNLEVBQUMsNkJBQXlDLE9BQWRMLE1BQU1NLE9BQU8sRUFBQztnQkFDcEg7Z0JBRUEsb0RBQW9EO2dCQUNwRCxJQUFJSix1QkFBdUJHLE1BQU0sS0FBSyxHQUFHO29CQUN2QyxJQUFJZixLQUFLZSxNQUFNLEtBQUssR0FBRzt3QkFDckJHLE1BQU07b0JBQ1IsT0FBTzt3QkFDTEEsTUFBTTtvQkFDUjtvQkFDQSxRQUFRLG9DQUFvQztnQkFDOUM7Z0JBRUEsZ0RBQWdEO2dCQUNoRDlDLGNBQWN3QztnQkFFZCw2QkFBNkI7Z0JBQzdCaEMsZUFBZTtnQkFDZk4sZUFBZTtnQkFDZkUsb0JBQW9CO2dCQUNwQkUsZ0JBQWdCO1lBQ2xCO1FBQ0YsRUFBRSxPQUFPTyxPQUFPO1lBQ2RnQixRQUFRaEIsS0FBSyxDQUFDLHFDQUFxQ0E7WUFDbkRDLFNBQVM7UUFDWCxTQUFVO1lBQ1JGLGFBQWE7UUFDZjtJQUNGO0lBRUEseUNBQXlDO0lBQ3pDLE1BQU1tQyxpQkFBaUIsQ0FBQ0M7UUFDdEIsSUFBSUEsY0FBYyxVQUFVL0MsY0FBYyxHQUFHO1lBQzNDQyxlQUFlRCxjQUFjO1lBQzdCRyxvQkFBb0I7UUFDdEIsT0FBTyxJQUFJNEMsY0FBYyxVQUFVL0MsY0FBY0YsV0FBVzRDLE1BQU0sR0FBRyxHQUFHO1lBQ3RFekMsZUFBZUQsY0FBYztZQUM3Qkcsb0JBQW9CO1FBQ3RCO0lBQ0Y7SUFFQSx1Q0FBdUM7SUFDdkMsTUFBTTZDLGtCQUFrQixPQUFPQztRQUM3QixJQUFJLENBQUNyRCx5QkFBeUJFLFdBQVc0QyxNQUFNLEtBQUssR0FBRztRQUV2RCxNQUFNUSxjQUFjcEQsVUFBVSxDQUFDRSxZQUFZLENBQUMrQixFQUFFO1FBQzlDMUIsZ0JBQWdCO1FBRWhCLElBQUk7WUFDRix5QkFBeUI7WUFDekIsTUFBTXpCLDBFQUEyQkEsQ0FBQ3NFLGFBQWFEO1lBRS9DLHlFQUF5RTtZQUN6RSxJQUFJakQsZUFBZUYsV0FBVzRDLE1BQU0sR0FBRyxLQUFLOUMsdUJBQXVCO2dCQUNqRSxNQUFNK0IsT0FBTyxNQUFNaEQsNEVBQTZCQSxDQUFDaUIsc0JBQXNCbUMsRUFBRTtnQkFDekUsTUFBTVEseUJBQXlCWixLQUFLYSxNQUFNLENBQUNDLENBQUFBLFlBQWFBLFVBQVVMLFlBQVk7Z0JBRTlFLDBCQUEwQjtnQkFDMUIsTUFBTUMsUUFBUSxNQUFNeEQsMkVBQTRCQSxDQUFDZSxzQkFBc0JtQyxFQUFFO2dCQUN6RXRCLGdCQUFnQjRCO2dCQUVoQixJQUFJRSx1QkFBdUJHLE1BQU0sR0FBRyxHQUFHO29CQUNyQzNDLGNBQWN3QztvQkFDZHRDLGVBQWU7Z0JBQ2pCLE9BQU87b0JBQ0wsaUZBQWlGO29CQUNqRjRDLE1BQU07b0JBQ050QyxlQUFlO29CQUVmLHVHQUF1RztvQkFDdkcsTUFBTXlCLFlBQVk7MkJBQUlMO3FCQUFLLENBQUNNLElBQUksQ0FBQyxDQUFDQyxHQUFHQzt3QkFDbkMsSUFBSUQsRUFBRUUsWUFBWSxJQUFJLENBQUNELEVBQUVDLFlBQVksRUFBRSxPQUFPLENBQUM7d0JBQy9DLElBQUksQ0FBQ0YsRUFBRUUsWUFBWSxJQUFJRCxFQUFFQyxZQUFZLEVBQUUsT0FBTzt3QkFDOUMsT0FBTztvQkFDVDtvQkFFQXJDLGNBQWNpQztnQkFDaEI7WUFDRixPQUFPO2dCQUNMLG1DQUFtQztnQkFDbkNjLGVBQWU7WUFDakI7UUFDRixFQUFFLE9BQU9sQyxPQUFPO1lBQ2RnQixRQUFRaEIsS0FBSyxDQUFDLGlDQUFpQ0E7WUFDL0NDLFNBQVM7UUFDWCxTQUFVO1lBQ1JSLGdCQUFnQjtZQUNoQkYsb0JBQW9CO1FBQ3RCO0lBQ0Y7SUFFQSw0QkFBNEI7SUFDNUIsTUFBTWdELHlCQUF5QjtRQUM3QjVDLGVBQWU7SUFDakI7SUFFQSxzQ0FBc0M7SUFDdEMsTUFBTTZDLHdCQUF3QixDQUFDWDtRQUM3QjFCLHFCQUFxQjBCO1FBQ3JCeEIsaUJBQWlCO0lBQ25CO0lBRUEsK0NBQStDO0lBQy9DLE1BQU1vQyx5QkFBeUIsQ0FBQ0M7UUFDOUIsNENBQTRDO1FBQzVDdkQsY0FBY3dELENBQUFBLE9BQ1pBLEtBQUtDLEdBQUcsQ0FBQ0MsQ0FBQUEsS0FDUEEsR0FBRzFCLEVBQUUsS0FBS3VCLHFCQUFxQnZCLEVBQUUsR0FBR3VCLHVCQUF1Qkc7SUFHakU7SUFFQSwwQ0FBMEM7SUFDMUMsTUFBTUMsMEJBQTBCLE9BQU9SO1FBQ3JDN0IsY0FBYzZCO1FBQ2QsSUFBSVM7UUFFSixJQUFJO1lBQ0ZBLGlCQUFpQm5FLHVEQUFLQSxDQUFDb0UsT0FBTyxDQUFDO1lBRS9CLE1BQU1DLFVBQVUsTUFBTS9FLGdFQUFpQkEsQ0FBQ29FO1lBRXhDLElBQUlXLFNBQVM7Z0JBQ1hyRSx1REFBS0EsQ0FBQ3FFLE9BQU8sQ0FBQyxvQ0FBb0M7b0JBQUU5QixJQUFJNEI7Z0JBQWU7Z0JBRXZFLDRCQUE0QjtnQkFDNUI1RCxjQUFjd0QsQ0FBQUEsT0FBUUEsS0FBS2YsTUFBTSxDQUFDaUIsQ0FBQUEsS0FBTUEsR0FBRzFCLEVBQUUsS0FBS21CO2dCQUVsRCx3QkFBd0I7Z0JBQ3hCLElBQUl0RCx1QkFBdUI7b0JBQ3pCLE1BQU15QyxRQUFRLE1BQU14RCwyRUFBNEJBLENBQUNlLHNCQUFzQm1DLEVBQUU7b0JBQ3pFdEIsZ0JBQWdCNEI7Z0JBQ2xCO1lBQ0YsT0FBTztnQkFDTDdDLHVEQUFLQSxDQUFDb0IsS0FBSyxDQUFDLGtDQUFrQztvQkFBRW1CLElBQUk0QjtnQkFBZTtZQUNyRTtRQUNGLEVBQUUsT0FBTy9DLE9BQU87WUFDZGdCLFFBQVFoQixLQUFLLENBQUMsZ0NBQWdDQTtZQUM5Q3BCLHVEQUFLQSxDQUFDb0IsS0FBSyxDQUFDLGtDQUFrQztnQkFBRW1CLElBQUk0QjtZQUFlO1FBQ3JFLFNBQVU7WUFDUnRDLGNBQWM7WUFDZEYscUJBQXFCO1FBQ3ZCO0lBQ0Y7SUFFQSxtREFBbUQ7SUFDbkQsTUFBTTJDLDBCQUEwQixDQUFDQztRQUMvQnhDLCtCQUErQndDO0lBQ2pDO0lBRUEsTUFBTUMsNkJBQTZCLE9BQU9EO1FBQ3hDdEMsd0JBQXdCc0M7UUFDeEIsSUFBSUo7UUFFSixJQUFJO1lBQ0ZBLGlCQUFpQm5FLHVEQUFLQSxDQUFDb0UsT0FBTyxDQUFDO1lBRS9CLE1BQU1DLFVBQVUsTUFBTTlFLDBFQUEyQkEsQ0FBQ2dGO1lBRWxELElBQUlGLFNBQVM7Z0JBQ1hyRSx1REFBS0EsQ0FBQ3FFLE9BQU8sQ0FBQyxvQ0FBb0M7b0JBQUU5QixJQUFJNEI7Z0JBQWU7Z0JBRXZFLDJDQUEyQztnQkFDM0NoRSxlQUFlNEQsQ0FBQUEsT0FBUUEsS0FBS2YsTUFBTSxDQUFDeUIsQ0FBQUEsTUFBT0EsSUFBSWxDLEVBQUUsS0FBS2dDO2dCQUVyRCxzRUFBc0U7Z0JBQ3RFLElBQUluRSxDQUFBQSxrQ0FBQUEsNENBQUFBLHNCQUF1Qm1DLEVBQUUsTUFBS2dDLGFBQWE7b0JBQzdDbEUseUJBQXlCO29CQUN6QkUsY0FBYyxFQUFFO29CQUNoQlUsZ0JBQWdCO2dCQUNsQjtZQUNGLE9BQU87Z0JBQ0xqQix1REFBS0EsQ0FBQ29CLEtBQUssQ0FBQyxrQ0FBa0M7b0JBQUVtQixJQUFJNEI7Z0JBQWU7WUFDckU7UUFDRixFQUFFLE9BQU8vQyxPQUFPO1lBQ2RnQixRQUFRaEIsS0FBSyxDQUFDLGdDQUFnQ0E7WUFDOUNwQix1REFBS0EsQ0FBQ29CLEtBQUssQ0FBQyxrQ0FBa0M7Z0JBQUVtQixJQUFJNEI7WUFBZTtRQUNyRSxTQUFVO1lBQ1JsQyx3QkFBd0I7WUFDeEJGLCtCQUErQjtRQUNqQztJQUNGO0lBRUEscUJBQ0UsOERBQUMyQztRQUFJQyxXQUFVOztZQUNadkQsdUJBQ0MsOERBQUNzRDtnQkFBSUMsV0FBVTswQkFDWnZEOzs7Ozs7WUFJSk4sNEJBQ0MsOERBQUNoQiwyREFBa0JBO2dCQUNqQlEsWUFBWUE7Z0JBQ1pFLGFBQWFBO2dCQUNiSSxjQUFjQTtnQkFDZGdFLGFBQWFwQjtnQkFDYnFCLFlBQVl2QjtnQkFDWndCLFVBQVVuQjs7Ozs7MENBR1osOERBQUNlOztrQ0FDQyw4REFBQ0s7d0JBQUdKLFdBQVU7a0NBQTBCOzs7Ozs7a0NBRXhDLDhEQUFDL0UsZ0VBQXVCQTt3QkFDdEJNLGFBQWFBO3dCQUNiRSx1QkFBdUJBO3dCQUN2QjRFLHdCQUF3QjNDO3dCQUN4QjRDLHFCQUFxQlg7d0JBQ3JCcEQsV0FBV0E7d0JBQ1hVLFlBQVlJOzs7Ozs7b0JBR2I1Qix1Q0FDQyw4REFBQ3NFOzswQ0FDQyw4REFBQ1E7Z0NBQUdQLFdBQVU7MENBQThCdkUsc0JBQXNCK0UsTUFBTTs7Ozs7OzBDQUV4RSw4REFBQ3RGLDREQUFtQkE7Z0NBQUNtQixjQUFjQTs7Ozs7OzBDQUVuQyw4REFBQzBEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7a0RBQ0MsNEVBQUNVOzRDQUNDQyxTQUFTdkM7NENBQ1Q2QixXQUFVO3NEQUVULGFBQXFELE9BQXhDM0QsZUFBZUEsYUFBYW1DLE9BQU8sR0FBRyxHQUFFOzs7Ozs7Ozs7OztrREFHMUQsOERBQUN1QjtrREFDQyw0RUFBQ1U7NENBQ0NDLFNBQVMsS0FBb0Q7NENBQzdEVixXQUFVO3NEQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs0QkFNSnpELDBCQUNDLDhEQUFDd0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVOzs7Ozs7Ozs7OzRDQUVmckUsV0FBVzRDLE1BQU0sS0FBSyxrQkFDeEIsOERBQUN3QjtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ1c7b0NBQUVYLFdBQVU7OENBQWdCOzs7Ozs7Ozs7OzBEQUcvQiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ1pyRSxXQUFXMEQsR0FBRyxDQUFDLENBQUNmLFdBQVdzQzt3Q0FZbkJ0QyxxQkFlQ0E7eURBMUJSLDhEQUFDeUI7d0NBRUNDLFdBQVcseUJBSVYsT0FIQzFCLFVBQVVMLFlBQVksR0FDbEIsbUNBQ0E7OzBEQUdOLDhEQUFDOEI7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDYTt3REFBS2IsV0FBVTs7NERBQXdCOzREQUFTWSxRQUFROzs7Ozs7O2tFQUN6RCw4REFBQ2I7d0RBQUlDLFdBQVU7OzREQUNaMUIsRUFBQUEsc0JBQUFBLFVBQVV3QyxRQUFRLGNBQWxCeEMsMENBQUFBLG9CQUFvQnlDLE1BQU0sbUJBQ3pCLDhEQUFDRjtnRUFDQ2IsV0FBVyxrQ0FRVixPQVBDMUIsVUFBVXdDLFFBQVEsQ0FBQ0MsTUFBTSxLQUFLLFVBQzFCLDhCQUNBekMsVUFBVXdDLFFBQVEsQ0FBQ0MsTUFBTSxLQUFLLGdCQUM5QixrQ0FDQXpDLFVBQVV3QyxRQUFRLENBQUNDLE1BQU0sS0FBSyxjQUM5QixrQ0FDQTswRUFHTHpDLFVBQVV3QyxRQUFRLENBQUNDLE1BQU07Ozs7Ozs0REFHN0IsR0FBQ3pDLHVCQUFBQSxVQUFVd0MsUUFBUSxjQUFsQnhDLDJDQUFBQSxxQkFBb0J5QyxNQUFNLG1CQUMxQiw4REFBQ0Y7Z0VBQUtiLFdBQVU7MEVBQTJEOzs7Ozs7MEVBTTdFLDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNTO3dFQUNDQyxTQUFTLElBQU16QixzQkFBc0JYO3dFQUNyQzBCLFdBQVU7d0VBQ1ZnQixPQUFNO2tGQUVOLDRFQUFDbkcsdUhBQU9BOzRFQUFDb0csTUFBTTs7Ozs7Ozs7Ozs7a0ZBRWpCLDhEQUFDUjt3RUFDQ0MsU0FBUyxJQUFNMUQscUJBQXFCc0IsVUFBVVYsRUFBRTt3RUFDaERzRCxVQUFVakUsZUFBZXFCLFVBQVVWLEVBQUU7d0VBQ3JDb0MsV0FBVTt3RUFDVmdCLE9BQU07a0ZBRUwvRCxlQUFlcUIsVUFBVVYsRUFBRSxpQkFDMUIsOERBQUM1QywySEFBV0E7NEVBQUNpRyxNQUFNOzRFQUFJakIsV0FBVTs7Ozs7c0dBRWpDLDhEQUFDbEYsd0hBQVFBOzRFQUFDbUcsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBTTFCLDhEQUFDRTtnREFBR25CLFdBQVU7MERBQW9CMUIsVUFBVThDLFFBQVE7Ozs7OzswREFDcEQsOERBQUNUO2dEQUFFWCxXQUFVOzBEQUFzQzFCLFVBQVUrQyxTQUFTOzs7Ozs7O3VDQXhEakUvQyxVQUFVVixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQW1FaENqQixtQ0FDQyw4REFBQ3ZCLDJEQUFrQkE7Z0JBQ2pCa0QsV0FBVzNCO2dCQUNYMkUsUUFBUXpFO2dCQUNSMEUsU0FBUztvQkFDUHpFLGlCQUFpQjtvQkFDakJGLHFCQUFxQjtnQkFDdkI7Z0JBQ0E0RSxRQUFRdEM7Ozs7OztZQUtYbkMsbUNBQ0MsOERBQUNnRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNqRiwrSEFBZUE7b0NBQUNpRixXQUFVOzs7Ozs7OENBQzNCLDhEQUFDTztvQ0FBR1AsV0FBVTs4Q0FBd0I7Ozs7Ozs7Ozs7OztzQ0FHeEMsOERBQUNXOzRCQUFFWCxXQUFVO3NDQUFxQjs7Ozs7O3NDQUlsQyw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDUztvQ0FDQ0MsU0FBUyxJQUFNMUQscUJBQXFCO29DQUNwQ2dELFdBQVU7b0NBQ1ZrQixVQUFVakUsZUFBZTs4Q0FDMUI7Ozs7Ozs4Q0FHRCw4REFBQ3dEO29DQUNDQyxTQUFTLElBQU1uQix3QkFBd0J4QztvQ0FDdkNpRCxXQUFVO29DQUNWa0IsVUFBVWpFLGVBQWU7OENBQzFCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVNSRSw2Q0FDQyw4REFBQzRDO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ2pGLCtIQUFlQTtvQ0FBQ2lGLFdBQVU7Ozs7Ozs4Q0FDM0IsOERBQUNPO29DQUFHUCxXQUFVOzhDQUF3Qjs7Ozs7Ozs7Ozs7O3NDQUd4Qyw4REFBQ1c7NEJBQUVYLFdBQVU7c0NBQXFCOzs7Ozs7c0NBSWxDLDhEQUFDeUI7NEJBQUd6QixXQUFVOzs4Q0FDWiw4REFBQzBCOzhDQUFHOzs7Ozs7OENBQ0osOERBQUNBOzhDQUFHOzs7Ozs7OENBQ0osOERBQUNBOzhDQUFHOzs7Ozs7OENBQ0osOERBQUNBOzhDQUFHOzs7Ozs7Ozs7Ozs7c0NBR04sOERBQUMzQjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNTO29DQUNDQyxTQUFTLElBQU10RCwrQkFBK0I7b0NBQzlDNEMsV0FBVTtvQ0FDVmtCLFVBQVU3RCx5QkFBeUI7OENBQ3BDOzs7Ozs7OENBR0QsOERBQUNvRDtvQ0FDQ0MsU0FBUyxJQUFNYiwyQkFBMkIxQztvQ0FDMUM2QyxXQUFVO29DQUNWa0IsVUFBVTdELHlCQUF5Qjs4Q0FDcEM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBU2Y7R0F4Zk0vQjtLQUFBQTtBQTBmTixpRUFBZUEsZUFBZUEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcT3Bvc0kgdjdcXHNyY1xcY29tcG9uZW50c1xcZmxhc2hjYXJkc1xcRmxhc2hjYXJkVmlld2VyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7XG4gIG9idGVuZXJDb2xlY2Npb25lc0ZsYXNoY2FyZHMsXG4gIG9idGVuZXJGbGFzaGNhcmRzUGFyYUVzdHVkaWFyLFxuICByZWdpc3RyYXJSZXNwdWVzdGFGbGFzaGNhcmQsXG4gIG9idGVuZXJFc3RhZGlzdGljYXNDb2xlY2Npb24sXG4gIGVsaW1pbmFyRmxhc2hjYXJkLFxuICBlbGltaW5hckNvbGVjY2lvbkZsYXNoY2FyZHMsXG4gIENvbGVjY2lvbkZsYXNoY2FyZHMsXG4gIEZsYXNoY2FyZENvblByb2dyZXNvLFxuICBEaWZpY3VsdGFkUmVzcHVlc3RhXG59IGZyb20gJ0AvbGliL3N1cGFiYXNlJztcbmltcG9ydCB7IEZpRWRpdDIsIEZpVHJhc2gyLCBGaUFsZXJ0VHJpYW5nbGUsIEZpUmVmcmVzaEN3IH0gZnJvbSAncmVhY3QtaWNvbnMvZmknO1xuaW1wb3J0IEZsYXNoY2FyZENvbGxlY3Rpb25MaXN0IGZyb20gJy4vRmxhc2hjYXJkQ29sbGVjdGlvbkxpc3QnO1xuaW1wb3J0IEZsYXNoY2FyZFN0YXRpc3RpY3MgZnJvbSAnLi9GbGFzaGNhcmRTdGF0aXN0aWNzJztcbmltcG9ydCBGbGFzaGNhcmRTdHVkeU1vZGUgZnJvbSAnLi9GbGFzaGNhcmRTdHVkeU1vZGUnO1xuaW1wb3J0IEZsYXNoY2FyZEVkaXRNb2RhbCBmcm9tICcuL0ZsYXNoY2FyZEVkaXRNb2RhbCc7XG5pbXBvcnQgdG9hc3QgZnJvbSAncmVhY3QtaG90LXRvYXN0JztcblxuY29uc3QgRmxhc2hjYXJkVmlld2VyOiBSZWFjdC5GQyA9ICgpID0+IHtcbiAgLy8gRXN0YWRvIHBhcmEgbGFzIGNvbGVjY2lvbmVzXG4gIGNvbnN0IFtjb2xlY2Npb25lcywgc2V0Q29sZWNjaW9uZXNdID0gdXNlU3RhdGU8Q29sZWNjaW9uRmxhc2hjYXJkc1tdPihbXSk7XG4gIGNvbnN0IFtjb2xlY2Npb25TZWxlY2Npb25hZGEsIHNldENvbGVjY2lvblNlbGVjY2lvbmFkYV0gPSB1c2VTdGF0ZTxDb2xlY2Npb25GbGFzaGNhcmRzIHwgbnVsbD4obnVsbCk7XG5cbiAgLy8gRXN0YWRvIHBhcmEgbGFzIGZsYXNoY2FyZHNcbiAgY29uc3QgW2ZsYXNoY2FyZHMsIHNldEZsYXNoY2FyZHNdID0gdXNlU3RhdGU8Rmxhc2hjYXJkQ29uUHJvZ3Jlc29bXT4oW10pO1xuICBjb25zdCBbYWN0aXZlSW5kZXgsIHNldEFjdGl2ZUluZGV4XSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBbbW9zdHJhclJlc3B1ZXN0YSwgc2V0TW9zdHJhclJlc3B1ZXN0YV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtyZXNwb25kaWVuZG8sIHNldFJlc3BvbmRpZW5kb10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gRXN0YWRvIHBhcmEgZWwgbW9kbyBkZSBlc3R1ZGlvXG4gIGNvbnN0IFttb2RvRXN0dWRpbywgc2V0TW9kb0VzdHVkaW9dID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIEVzdGFkbyBwYXJhIGVzdGFkw61zdGljYXNcbiAgY29uc3QgW2VzdGFkaXN0aWNhcywgc2V0RXN0YWRpc3RpY2FzXSA9IHVzZVN0YXRlPGFueT4obnVsbCk7XG5cbiAgLy8gRXN0YWRvIHBhcmEgY2FyZ2EgeSBlcnJvcmVzXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGUoJycpO1xuXG4gIC8vIEVzdGFkbyBwYXJhIGVkaWNpw7NuIHkgZWxpbWluYWNpw7NuIGRlIGZsYXNoY2FyZHNcbiAgY29uc3QgW2ZsYXNoY2FyZEVkaXRhbmRvLCBzZXRGbGFzaGNhcmRFZGl0YW5kb10gPSB1c2VTdGF0ZTxGbGFzaGNhcmRDb25Qcm9ncmVzbyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbc2hvd0VkaXRNb2RhbCwgc2V0U2hvd0VkaXRNb2RhbF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93RGVsZXRlQ29uZmlybSwgc2V0U2hvd0RlbGV0ZUNvbmZpcm1dID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtkZWxldGluZ0lkLCBzZXREZWxldGluZ0lkXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuXG4gIC8vIEVzdGFkbyBwYXJhIGVsaW1pbmFjacOzbiBkZSBjb2xlY2Npb25lc1xuICBjb25zdCBbc2hvd0RlbGV0ZUNvbGxlY3Rpb25Db25maXJtLCBzZXRTaG93RGVsZXRlQ29sbGVjdGlvbkNvbmZpcm1dID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtkZWxldGluZ0NvbGxlY3Rpb25JZCwgc2V0RGVsZXRpbmdDb2xsZWN0aW9uSWRdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG5cbiAgLy8gQ2FyZ2FyIGNvbGVjY2lvbmVzIGFsIG1vbnRhciBlbCBjb21wb25lbnRlXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgY2FyZ2FyQ29sZWNjaW9uZXMgPSBhc3luYyAoKSA9PiB7XG4gICAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgb2J0ZW5lckNvbGVjY2lvbmVzRmxhc2hjYXJkcygpO1xuICAgICAgICBzZXRDb2xlY2Npb25lcyhkYXRhKTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIGNhcmdhciBjb2xlY2Npb25lczonLCBlcnJvcik7XG4gICAgICAgIHNldEVycm9yKCdObyBzZSBwdWRpZXJvbiBjYXJnYXIgbGFzIGNvbGVjY2lvbmVzIGRlIGZsYXNoY2FyZHMnKTtcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgICB9XG4gICAgfTtcblxuICAgIGNhcmdhckNvbGVjY2lvbmVzKCk7XG4gIH0sIFtdKTtcblxuICAvLyBNYW5lamFyIGxhIHNlbGVjY2nDs24gZGUgdW5hIGNvbGVjY2nDs25cbiAgY29uc3QgaGFuZGxlU2VsZWNjaW9uYXJDb2xlY2Npb24gPSBhc3luYyAoY29sZWNjaW9uOiBDb2xlY2Npb25GbGFzaGNhcmRzKSA9PiB7XG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgIHNldEVycm9yKCcnKTtcbiAgICBzZXRDb2xlY2Npb25TZWxlY2Npb25hZGEoY29sZWNjaW9uKTtcbiAgICBzZXRBY3RpdmVJbmRleCgwKTtcbiAgICBzZXRNb3N0cmFyUmVzcHVlc3RhKGZhbHNlKTtcbiAgICBzZXRSZXNwb25kaWVuZG8oZmFsc2UpO1xuICAgIHNldE1vZG9Fc3R1ZGlvKGZhbHNlKTtcblxuICAgIHRyeSB7XG4gICAgICAvLyBDYXJnYXIgZmxhc2hjYXJkcyBjb24gc3UgcHJvZ3Jlc29cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCBvYnRlbmVyRmxhc2hjYXJkc1BhcmFFc3R1ZGlhcihjb2xlY2Npb24uaWQpIGFzIEZsYXNoY2FyZENvblByb2dyZXNvW107XG5cbiAgICAgIC8vIE9yZGVuYXIgbGFzIGZsYXNoY2FyZHM6IHByaW1lcm8gbGFzIHF1ZSBkZWJlbiBlc3R1ZGlhcnNlIGhveSwgbHVlZ28gZWwgcmVzdG9cbiAgICAgIGNvbnN0IG9yZGVuYWRhcyA9IFsuLi5kYXRhXS5zb3J0KChhLCBiKSA9PiB7XG4gICAgICAgIGlmIChhLmRlYmVFc3R1ZGlhciAmJiAhYi5kZWJlRXN0dWRpYXIpIHJldHVybiAtMTtcbiAgICAgICAgaWYgKCFhLmRlYmVFc3R1ZGlhciAmJiBiLmRlYmVFc3R1ZGlhcikgcmV0dXJuIDE7XG4gICAgICAgIHJldHVybiAwO1xuICAgICAgfSk7XG5cbiAgICAgIHNldEZsYXNoY2FyZHMob3JkZW5hZGFzKTtcblxuICAgICAgLy8gQ2FyZ2FyIGVzdGFkw61zdGljYXNcbiAgICAgIGNvbnN0IHN0YXRzID0gYXdhaXQgb2J0ZW5lckVzdGFkaXN0aWNhc0NvbGVjY2lvbihjb2xlY2Npb24uaWQpO1xuICAgICAgc2V0RXN0YWRpc3RpY2FzKHN0YXRzKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWwgY2FyZ2FyIGZsYXNoY2FyZHM6JywgZXJyb3IpO1xuICAgICAgc2V0RXJyb3IoJ05vIHNlIHB1ZGllcm9uIGNhcmdhciBsYXMgZmxhc2hjYXJkcyBkZSBlc3RhIGNvbGVjY2nDs24nKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gSW5pY2lhciBlbCBtb2RvIGRlIGVzdHVkaW9cbiAgY29uc3QgaW5pY2lhck1vZG9Fc3R1ZGlvID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldElzTG9hZGluZyh0cnVlKTtcblxuICAgIHRyeSB7XG4gICAgICBpZiAoY29sZWNjaW9uU2VsZWNjaW9uYWRhKSB7XG4gICAgICAgIC8vIFJlY2FyZ2FyIGxhcyBmbGFzaGNhcmRzIHBhcmEgYXNlZ3VyYXJub3MgZGUgdGVuZXIgbG9zIGRhdG9zIG3DoXMgcmVjaWVudGVzXG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCBvYnRlbmVyRmxhc2hjYXJkc1BhcmFFc3R1ZGlhcihjb2xlY2Npb25TZWxlY2Npb25hZGEuaWQpO1xuXG4gICAgICAgIC8vIEZpbHRyYXIgc29sbyBsYXMgZmxhc2hjYXJkcyBxdWUgZGViZW4gZXN0dWRpYXJzZSBob3lcbiAgICAgICAgY29uc3QgZmxhc2hjYXJkc1BhcmFFc3R1ZGlhciA9IGRhdGEuZmlsdGVyKGZsYXNoY2FyZCA9PiBmbGFzaGNhcmQuZGViZUVzdHVkaWFyKTtcblxuICAgICAgICAvLyBBY3R1YWxpemFyIGVzdGFkw61zdGljYXNcbiAgICAgICAgY29uc3Qgc3RhdHMgPSBhd2FpdCBvYnRlbmVyRXN0YWRpc3RpY2FzQ29sZWNjaW9uKGNvbGVjY2lvblNlbGVjY2lvbmFkYS5pZCk7XG4gICAgICAgIHNldEVzdGFkaXN0aWNhcyhzdGF0cyk7XG5cbiAgICAgICAgLy8gVmVyaWZpY2FyIHNpIGVsIG7Dum1lcm8gZGUgZmxhc2hjYXJkcyBwYXJhIGVzdHVkaWFyIGNvaW5jaWRlIGNvbiBsYXMgZXN0YWTDrXN0aWNhc1xuICAgICAgICBpZiAoZmxhc2hjYXJkc1BhcmFFc3R1ZGlhci5sZW5ndGggIT09IHN0YXRzLnBhcmFIb3kpIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oYERpc2NyZXBhbmNpYSBlbiBlbCBjb250ZW86ICR7Zmxhc2hjYXJkc1BhcmFFc3R1ZGlhci5sZW5ndGh9IGZsYXNoY2FyZHMgZmlsdHJhZGFzIHZzICR7c3RhdHMucGFyYUhveX0gZW4gZXN0YWTDrXN0aWNhc2ApO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gU2kgbm8gaGF5IGZsYXNoY2FyZHMgcGFyYSBob3ksIG1vc3RyYXIgdW4gbWVuc2FqZVxuICAgICAgICBpZiAoZmxhc2hjYXJkc1BhcmFFc3R1ZGlhci5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICBpZiAoZGF0YS5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICAgIGFsZXJ0KCdObyBoYXkgZmxhc2hjYXJkcyBlbiBlc3RhIGNvbGVjY2nDs24uJyk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGFsZXJ0KCdObyBoYXkgZmxhc2hjYXJkcyBwcm9ncmFtYWRhcyBwYXJhIGVzdHVkaWFyIGhveS4gVnVlbHZlIG1hw7FhbmEgbyBhanVzdGEgZWwgcHJvZ3Jlc28gZGUgbGFzIHRhcmpldGFzLicpO1xuICAgICAgICAgIH1cbiAgICAgICAgICByZXR1cm47IC8vIFNhbGlyIHNpbiBpbmljaWFyIGVsIG1vZG8gZXN0dWRpb1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gVXNhciBzb2xvIGxhcyBmbGFzaGNhcmRzIHByb2dyYW1hZGFzIHBhcmEgaG95XG4gICAgICAgIHNldEZsYXNoY2FyZHMoZmxhc2hjYXJkc1BhcmFFc3R1ZGlhcik7XG5cbiAgICAgICAgLy8gSW5pY2lhciBlbCBtb2RvIGRlIGVzdHVkaW9cbiAgICAgICAgc2V0TW9kb0VzdHVkaW8odHJ1ZSk7XG4gICAgICAgIHNldEFjdGl2ZUluZGV4KDApO1xuICAgICAgICBzZXRNb3N0cmFyUmVzcHVlc3RhKGZhbHNlKTtcbiAgICAgICAgc2V0UmVzcG9uZGllbmRvKGZhbHNlKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWwgaW5pY2lhciBtb2RvIGRlIGVzdHVkaW86JywgZXJyb3IpO1xuICAgICAgc2V0RXJyb3IoJ05vIHNlIHB1ZG8gaW5pY2lhciBlbCBtb2RvIGRlIGVzdHVkaW8nKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gTWFuZWphciBsYSBuYXZlZ2FjacOzbiBlbnRyZSBmbGFzaGNhcmRzXG4gIGNvbnN0IGhhbmRsZU5hdmlnYXRlID0gKGRpcmVjdGlvbjogJ3ByZXYnIHwgJ25leHQnKSA9PiB7XG4gICAgaWYgKGRpcmVjdGlvbiA9PT0gJ3ByZXYnICYmIGFjdGl2ZUluZGV4ID4gMCkge1xuICAgICAgc2V0QWN0aXZlSW5kZXgoYWN0aXZlSW5kZXggLSAxKTtcbiAgICAgIHNldE1vc3RyYXJSZXNwdWVzdGEoZmFsc2UpO1xuICAgIH0gZWxzZSBpZiAoZGlyZWN0aW9uID09PSAnbmV4dCcgJiYgYWN0aXZlSW5kZXggPCBmbGFzaGNhcmRzLmxlbmd0aCAtIDEpIHtcbiAgICAgIHNldEFjdGl2ZUluZGV4KGFjdGl2ZUluZGV4ICsgMSk7XG4gICAgICBzZXRNb3N0cmFyUmVzcHVlc3RhKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gTWFuZWphciBsYSByZXNwdWVzdGEgYSB1bmEgZmxhc2hjYXJkXG4gIGNvbnN0IGhhbmRsZVJlc3B1ZXN0YSA9IGFzeW5jIChkaWZpY3VsdGFkOiBEaWZpY3VsdGFkUmVzcHVlc3RhKSA9PiB7XG4gICAgaWYgKCFjb2xlY2Npb25TZWxlY2Npb25hZGEgfHwgZmxhc2hjYXJkcy5sZW5ndGggPT09IDApIHJldHVybjtcblxuICAgIGNvbnN0IGZsYXNoY2FyZElkID0gZmxhc2hjYXJkc1thY3RpdmVJbmRleF0uaWQ7XG4gICAgc2V0UmVzcG9uZGllbmRvKHRydWUpO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIFJlZ2lzdHJhciBsYSByZXNwdWVzdGFcbiAgICAgIGF3YWl0IHJlZ2lzdHJhclJlc3B1ZXN0YUZsYXNoY2FyZChmbGFzaGNhcmRJZCwgZGlmaWN1bHRhZCk7XG5cbiAgICAgIC8vIFJlY2FyZ2FyIGxhcyBmbGFzaGNhcmRzIHkgZXN0YWTDrXN0aWNhcyBzaSBlc3RhbW9zIGVuIGxhIMO6bHRpbWEgdGFyamV0YVxuICAgICAgaWYgKGFjdGl2ZUluZGV4ID49IGZsYXNoY2FyZHMubGVuZ3RoIC0gMSAmJiBjb2xlY2Npb25TZWxlY2Npb25hZGEpIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IG9idGVuZXJGbGFzaGNhcmRzUGFyYUVzdHVkaWFyKGNvbGVjY2lvblNlbGVjY2lvbmFkYS5pZCkgYXMgRmxhc2hjYXJkQ29uUHJvZ3Jlc29bXTtcbiAgICAgICAgY29uc3QgZmxhc2hjYXJkc1BhcmFFc3R1ZGlhciA9IGRhdGEuZmlsdGVyKGZsYXNoY2FyZCA9PiBmbGFzaGNhcmQuZGViZUVzdHVkaWFyKTtcblxuICAgICAgICAvLyBBY3R1YWxpemFyIGVzdGFkw61zdGljYXNcbiAgICAgICAgY29uc3Qgc3RhdHMgPSBhd2FpdCBvYnRlbmVyRXN0YWRpc3RpY2FzQ29sZWNjaW9uKGNvbGVjY2lvblNlbGVjY2lvbmFkYS5pZCk7XG4gICAgICAgIHNldEVzdGFkaXN0aWNhcyhzdGF0cyk7XG5cbiAgICAgICAgaWYgKGZsYXNoY2FyZHNQYXJhRXN0dWRpYXIubGVuZ3RoID4gMCkge1xuICAgICAgICAgIHNldEZsYXNoY2FyZHMoZmxhc2hjYXJkc1BhcmFFc3R1ZGlhcik7XG4gICAgICAgICAgc2V0QWN0aXZlSW5kZXgoMCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgLy8gU2kgbm8gaGF5IG3DoXMgZmxhc2hjYXJkcyBwYXJhIGhveSwgbW9zdHJhciBtZW5zYWplIHkgc2FsaXIgZGVsIG1vZG8gZGUgZXN0dWRpb1xuICAgICAgICAgIGFsZXJ0KCfCoUhhcyBjb21wbGV0YWRvIHRvZGFzIGxhcyBmbGFzaGNhcmRzIHBhcmEgaG95ISBWdWVsdmUgbWHDsWFuYSBwYXJhIGNvbnRpbnVhciBlc3R1ZGlhbmRvLicpO1xuICAgICAgICAgIHNldE1vZG9Fc3R1ZGlvKGZhbHNlKTtcblxuICAgICAgICAgIC8vIE9yZGVuYXIgbGFzIGZsYXNoY2FyZHM6IHByaW1lcm8gbGFzIHF1ZSBkZWJlbiBlc3R1ZGlhcnNlIChhdW5xdWUgeWEgbm8gaGF5YSBuaW5ndW5hKSwgbHVlZ28gZWwgcmVzdG9cbiAgICAgICAgICBjb25zdCBvcmRlbmFkYXMgPSBbLi4uZGF0YV0uc29ydCgoYSwgYikgPT4ge1xuICAgICAgICAgICAgaWYgKGEuZGViZUVzdHVkaWFyICYmICFiLmRlYmVFc3R1ZGlhcikgcmV0dXJuIC0xO1xuICAgICAgICAgICAgaWYgKCFhLmRlYmVFc3R1ZGlhciAmJiBiLmRlYmVFc3R1ZGlhcikgcmV0dXJuIDE7XG4gICAgICAgICAgICByZXR1cm4gMDtcbiAgICAgICAgICB9KTtcblxuICAgICAgICAgIHNldEZsYXNoY2FyZHMob3JkZW5hZGFzKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gQXZhbnphciBhIGxhIHNpZ3VpZW50ZSBmbGFzaGNhcmRcbiAgICAgICAgaGFuZGxlTmF2aWdhdGUoJ25leHQnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWwgcmVnaXN0cmFyIHJlc3B1ZXN0YTonLCBlcnJvcik7XG4gICAgICBzZXRFcnJvcignTm8gc2UgcHVkbyByZWdpc3RyYXIgbGEgcmVzcHVlc3RhJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldFJlc3BvbmRpZW5kbyhmYWxzZSk7XG4gICAgICBzZXRNb3N0cmFyUmVzcHVlc3RhKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gU2FsaXIgZGVsIG1vZG8gZGUgZXN0dWRpb1xuICBjb25zdCBoYW5kbGVTYWxpck1vZG9Fc3R1ZGlvID0gKCkgPT4ge1xuICAgIHNldE1vZG9Fc3R1ZGlvKGZhbHNlKTtcbiAgfTtcblxuICAvLyBNYW5lamFyIGxhIGVkaWNpw7NuIGRlIHVuYSBmbGFzaGNhcmRcbiAgY29uc3QgaGFuZGxlRWRpdGFyRmxhc2hjYXJkID0gKGZsYXNoY2FyZDogRmxhc2hjYXJkQ29uUHJvZ3Jlc28pID0+IHtcbiAgICBzZXRGbGFzaGNhcmRFZGl0YW5kbyhmbGFzaGNhcmQpO1xuICAgIHNldFNob3dFZGl0TW9kYWwodHJ1ZSk7XG4gIH07XG5cbiAgLy8gTWFuZWphciBlbCBndWFyZGFkbyBkZSB1bmEgZmxhc2hjYXJkIGVkaXRhZGFcbiAgY29uc3QgaGFuZGxlR3VhcmRhckZsYXNoY2FyZCA9IChmbGFzaGNhcmRBY3R1YWxpemFkYTogRmxhc2hjYXJkQ29uUHJvZ3Jlc28pID0+IHtcbiAgICAvLyBBY3R1YWxpemFyIGxhIGZsYXNoY2FyZCBlbiBsYSBsaXN0YSBsb2NhbFxuICAgIHNldEZsYXNoY2FyZHMocHJldiA9PlxuICAgICAgcHJldi5tYXAoZmMgPT5cbiAgICAgICAgZmMuaWQgPT09IGZsYXNoY2FyZEFjdHVhbGl6YWRhLmlkID8gZmxhc2hjYXJkQWN0dWFsaXphZGEgOiBmY1xuICAgICAgKVxuICAgICk7XG4gIH07XG5cbiAgLy8gTWFuZWphciBsYSBlbGltaW5hY2nDs24gZGUgdW5hIGZsYXNoY2FyZFxuICBjb25zdCBoYW5kbGVFbGltaW5hckZsYXNoY2FyZCA9IGFzeW5jIChmbGFzaGNhcmRJZDogc3RyaW5nKSA9PiB7XG4gICAgc2V0RGVsZXRpbmdJZChmbGFzaGNhcmRJZCk7XG4gICAgbGV0IGxvYWRpbmdUb2FzdElkOiBzdHJpbmcgfCB1bmRlZmluZWQ7XG5cbiAgICB0cnkge1xuICAgICAgbG9hZGluZ1RvYXN0SWQgPSB0b2FzdC5sb2FkaW5nKCdFbGltaW5hbmRvIGZsYXNoY2FyZC4uLicpO1xuXG4gICAgICBjb25zdCBzdWNjZXNzID0gYXdhaXQgZWxpbWluYXJGbGFzaGNhcmQoZmxhc2hjYXJkSWQpO1xuXG4gICAgICBpZiAoc3VjY2Vzcykge1xuICAgICAgICB0b2FzdC5zdWNjZXNzKCdGbGFzaGNhcmQgZWxpbWluYWRhIGV4aXRvc2FtZW50ZScsIHsgaWQ6IGxvYWRpbmdUb2FzdElkIH0pO1xuXG4gICAgICAgIC8vIEFjdHVhbGl6YXIgbGEgbGlzdGEgbG9jYWxcbiAgICAgICAgc2V0Rmxhc2hjYXJkcyhwcmV2ID0+IHByZXYuZmlsdGVyKGZjID0+IGZjLmlkICE9PSBmbGFzaGNhcmRJZCkpO1xuXG4gICAgICAgIC8vIFJlY2FyZ2FyIGVzdGFkw61zdGljYXNcbiAgICAgICAgaWYgKGNvbGVjY2lvblNlbGVjY2lvbmFkYSkge1xuICAgICAgICAgIGNvbnN0IHN0YXRzID0gYXdhaXQgb2J0ZW5lckVzdGFkaXN0aWNhc0NvbGVjY2lvbihjb2xlY2Npb25TZWxlY2Npb25hZGEuaWQpO1xuICAgICAgICAgIHNldEVzdGFkaXN0aWNhcyhzdGF0cyk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRvYXN0LmVycm9yKCdFcnJvciBhbCBlbGltaW5hciBsYSBmbGFzaGNhcmQnLCB7IGlkOiBsb2FkaW5nVG9hc3RJZCB9KTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWwgZWxpbWluYXIgZmxhc2hjYXJkOicsIGVycm9yKTtcbiAgICAgIHRvYXN0LmVycm9yKCdFcnJvciBhbCBlbGltaW5hciBsYSBmbGFzaGNhcmQnLCB7IGlkOiBsb2FkaW5nVG9hc3RJZCB9KTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0RGVsZXRpbmdJZChudWxsKTtcbiAgICAgIHNldFNob3dEZWxldGVDb25maXJtKG51bGwpO1xuICAgIH1cbiAgfTtcblxuICAvLyBNYW5lamFyIGxhIGVsaW1pbmFjacOzbiBkZSB1bmEgY29sZWNjacOzbiBjb21wbGV0YVxuICBjb25zdCBoYW5kbGVFbGltaW5hckNvbGVjY2lvbiA9IChjb2xlY2Npb25JZDogc3RyaW5nKSA9PiB7XG4gICAgc2V0U2hvd0RlbGV0ZUNvbGxlY3Rpb25Db25maXJtKGNvbGVjY2lvbklkKTtcbiAgfTtcblxuICBjb25zdCBjb25maXJtYXJFbGltaW5hckNvbGVjY2lvbiA9IGFzeW5jIChjb2xlY2Npb25JZDogc3RyaW5nKSA9PiB7XG4gICAgc2V0RGVsZXRpbmdDb2xsZWN0aW9uSWQoY29sZWNjaW9uSWQpO1xuICAgIGxldCBsb2FkaW5nVG9hc3RJZDogc3RyaW5nIHwgdW5kZWZpbmVkO1xuXG4gICAgdHJ5IHtcbiAgICAgIGxvYWRpbmdUb2FzdElkID0gdG9hc3QubG9hZGluZygnRWxpbWluYW5kbyBjb2xlY2Npw7NuLi4uJyk7XG5cbiAgICAgIGNvbnN0IHN1Y2Nlc3MgPSBhd2FpdCBlbGltaW5hckNvbGVjY2lvbkZsYXNoY2FyZHMoY29sZWNjaW9uSWQpO1xuXG4gICAgICBpZiAoc3VjY2Vzcykge1xuICAgICAgICB0b2FzdC5zdWNjZXNzKCdDb2xlY2Npw7NuIGVsaW1pbmFkYSBleGl0b3NhbWVudGUnLCB7IGlkOiBsb2FkaW5nVG9hc3RJZCB9KTtcblxuICAgICAgICAvLyBBY3R1YWxpemFyIGxhIGxpc3RhIGxvY2FsIGRlIGNvbGVjY2lvbmVzXG4gICAgICAgIHNldENvbGVjY2lvbmVzKHByZXYgPT4gcHJldi5maWx0ZXIoY29sID0+IGNvbC5pZCAhPT0gY29sZWNjaW9uSWQpKTtcblxuICAgICAgICAvLyBTaSBsYSBjb2xlY2Npw7NuIGVsaW1pbmFkYSBlcmEgbGEgc2VsZWNjaW9uYWRhLCBsaW1waWFyIGxhIHNlbGVjY2nDs25cbiAgICAgICAgaWYgKGNvbGVjY2lvblNlbGVjY2lvbmFkYT8uaWQgPT09IGNvbGVjY2lvbklkKSB7XG4gICAgICAgICAgc2V0Q29sZWNjaW9uU2VsZWNjaW9uYWRhKG51bGwpO1xuICAgICAgICAgIHNldEZsYXNoY2FyZHMoW10pO1xuICAgICAgICAgIHNldEVzdGFkaXN0aWNhcyhudWxsKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdG9hc3QuZXJyb3IoJ0Vycm9yIGFsIGVsaW1pbmFyIGxhIGNvbGVjY2nDs24nLCB7IGlkOiBsb2FkaW5nVG9hc3RJZCB9KTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWwgZWxpbWluYXIgY29sZWNjacOzbjonLCBlcnJvcik7XG4gICAgICB0b2FzdC5lcnJvcignRXJyb3IgYWwgZWxpbWluYXIgbGEgY29sZWNjacOzbicsIHsgaWQ6IGxvYWRpbmdUb2FzdElkIH0pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXREZWxldGluZ0NvbGxlY3Rpb25JZChudWxsKTtcbiAgICAgIHNldFNob3dEZWxldGVDb2xsZWN0aW9uQ29uZmlybShudWxsKTtcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHAtNFwiPlxuICAgICAge2Vycm9yICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtMTAwIGJvcmRlciBib3JkZXItcmVkLTQwMCB0ZXh0LXJlZC03MDAgcHgtNCBweS0zIHJvdW5kZWQgbWItNFwiPlxuICAgICAgICAgIHtlcnJvcn1cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7bW9kb0VzdHVkaW8gPyAoXG4gICAgICAgIDxGbGFzaGNhcmRTdHVkeU1vZGVcbiAgICAgICAgICBmbGFzaGNhcmRzPXtmbGFzaGNhcmRzfVxuICAgICAgICAgIGFjdGl2ZUluZGV4PXthY3RpdmVJbmRleH1cbiAgICAgICAgICByZXNwb25kaWVuZG89e3Jlc3BvbmRpZW5kb31cbiAgICAgICAgICBvblJlc3B1ZXN0YT17aGFuZGxlUmVzcHVlc3RhfVxuICAgICAgICAgIG9uTmF2aWdhdGU9e2hhbmRsZU5hdmlnYXRlfVxuICAgICAgICAgIG9uVm9sdmVyPXtoYW5kbGVTYWxpck1vZG9Fc3R1ZGlvfVxuICAgICAgICAvPlxuICAgICAgKSA6IChcbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIG1iLTRcIj5NaXMgRmxhc2hjYXJkczwvaDI+XG5cbiAgICAgICAgICA8Rmxhc2hjYXJkQ29sbGVjdGlvbkxpc3RcbiAgICAgICAgICAgIGNvbGVjY2lvbmVzPXtjb2xlY2Npb25lc31cbiAgICAgICAgICAgIGNvbGVjY2lvblNlbGVjY2lvbmFkYT17Y29sZWNjaW9uU2VsZWNjaW9uYWRhfVxuICAgICAgICAgICAgb25TZWxlY2Npb25hckNvbGVjY2lvbj17aGFuZGxlU2VsZWNjaW9uYXJDb2xlY2Npb259XG4gICAgICAgICAgICBvbkVsaW1pbmFyQ29sZWNjaW9uPXtoYW5kbGVFbGltaW5hckNvbGVjY2lvbn1cbiAgICAgICAgICAgIGlzTG9hZGluZz17aXNMb2FkaW5nfVxuICAgICAgICAgICAgZGVsZXRpbmdJZD17ZGVsZXRpbmdDb2xsZWN0aW9uSWR9XG4gICAgICAgICAgLz5cblxuICAgICAgICAgIHtjb2xlY2Npb25TZWxlY2Npb25hZGEgJiYgKFxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCBtYi00XCI+e2NvbGVjY2lvblNlbGVjY2lvbmFkYS50aXR1bG99PC9oMz5cblxuICAgICAgICAgICAgICA8Rmxhc2hjYXJkU3RhdGlzdGljcyBlc3RhZGlzdGljYXM9e2VzdGFkaXN0aWNhc30gLz5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIG1iLTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtpbmljaWFyTW9kb0VzdHVkaW99XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLW9yYW5nZS01MDAgaG92ZXI6Ymctb3JhbmdlLTYwMCB0ZXh0LXdoaXRlIGZvbnQtYm9sZCBweS0yIHB4LTQgcm91bmRlZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6c2hhZG93LW91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7YEVzdHVkaWFyICgke2VzdGFkaXN0aWNhcyA/IGVzdGFkaXN0aWNhcy5wYXJhSG95IDogMH0gcGFyYSBob3kpYH1cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHsvKiBJbXBsZW1lbnRhciB2ZXIgZXN0YWTDrXN0aWNhcyBkZXRhbGxhZGFzICovfX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS01MDAgaG92ZXI6YmctYmx1ZS02MDAgdGV4dC13aGl0ZSBmb250LWJvbGQgcHktMiBweC00IHJvdW5kZWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnNoYWRvdy1vdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgVmVyIGVzdGFkw61zdGljYXNcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7aXNMb2FkaW5nID8gKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBpdGVtcy1jZW50ZXIgaC00MFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtOCB3LTggYm9yZGVyLWItMiBib3JkZXItZ3JheS05MDBcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSA6IGZsYXNoY2FyZHMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcC00XCI+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+Tm8gaGF5IGZsYXNoY2FyZHMgZW4gZXN0YSBjb2xlY2Npw7NuLjwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgIHtmbGFzaGNhcmRzLm1hcCgoZmxhc2hjYXJkLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAga2V5PXtmbGFzaGNhcmQuaWR9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgYm9yZGVyIHJvdW5kZWQtbGcgcC00ICR7XG4gICAgICAgICAgICAgICAgICAgICAgICBmbGFzaGNhcmQuZGViZUVzdHVkaWFyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1vcmFuZ2UtMzAwIGJnLW9yYW5nZS01MCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLWdyYXktMjAwJ1xuICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1zdGFydCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5UYXJqZXRhIHtpbmRleCArIDF9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2ZsYXNoY2FyZC5wcm9ncmVzbz8uZXN0YWRvICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtMiBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXhzICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZsYXNoY2FyZC5wcm9ncmVzby5lc3RhZG8gPT09ICdudWV2bydcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogZmxhc2hjYXJkLnByb2dyZXNvLmVzdGFkbyA9PT0gJ2FwcmVuZGllbmRvJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLXllbGxvdy0xMDAgdGV4dC15ZWxsb3ctODAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogZmxhc2hjYXJkLnByb2dyZXNvLmVzdGFkbyA9PT0gJ3JlcGFzYW5kbydcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1vcmFuZ2UtMTAwIHRleHQtb3JhbmdlLTgwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zmxhc2hjYXJkLnByb2dyZXNvLmVzdGFkb31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHshZmxhc2hjYXJkLnByb2dyZXNvPy5lc3RhZG8gJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAgcHgtMiBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXhzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBudWV2b1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogQm90b25lcyBkZSBhY2Npw7NuICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRWRpdGFyRmxhc2hjYXJkKGZsYXNoY2FyZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTEgdGV4dC1ibHVlLTUwMCBob3ZlcjpiZy1ibHVlLTUwIHJvdW5kZWQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJFZGl0YXIgZmxhc2hjYXJkXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RmlFZGl0MiBzaXplPXsxNH0gLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93RGVsZXRlQ29uZmlybShmbGFzaGNhcmQuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2RlbGV0aW5nSWQgPT09IGZsYXNoY2FyZC5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMSB0ZXh0LXJlZC01MDAgaG92ZXI6YmctcmVkLTUwIHJvdW5kZWQgdHJhbnNpdGlvbi1jb2xvcnMgZGlzYWJsZWQ6b3BhY2l0eS01MFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIkVsaW1pbmFyIGZsYXNoY2FyZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2RlbGV0aW5nSWQgPT09IGZsYXNoY2FyZC5pZCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEZpUmVmcmVzaEN3IHNpemU9ezE0fSBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW5cIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEZpVHJhc2gyIHNpemU9ezE0fSAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gbWItMlwiPntmbGFzaGNhcmQucHJlZ3VudGF9PC9oND5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS03MDAgbGluZS1jbGFtcC0yXCI+e2ZsYXNoY2FyZC5yZXNwdWVzdGF9PC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogTW9kYWwgZGUgZWRpY2nDs24gKi99XG4gICAgICB7Zmxhc2hjYXJkRWRpdGFuZG8gJiYgKFxuICAgICAgICA8Rmxhc2hjYXJkRWRpdE1vZGFsXG4gICAgICAgICAgZmxhc2hjYXJkPXtmbGFzaGNhcmRFZGl0YW5kb31cbiAgICAgICAgICBpc09wZW49e3Nob3dFZGl0TW9kYWx9XG4gICAgICAgICAgb25DbG9zZT17KCkgPT4ge1xuICAgICAgICAgICAgc2V0U2hvd0VkaXRNb2RhbChmYWxzZSk7XG4gICAgICAgICAgICBzZXRGbGFzaGNhcmRFZGl0YW5kbyhudWxsKTtcbiAgICAgICAgICB9fVxuICAgICAgICAgIG9uU2F2ZT17aGFuZGxlR3VhcmRhckZsYXNoY2FyZH1cbiAgICAgICAgLz5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBEacOhbG9nbyBkZSBjb25maXJtYWNpw7NuIGRlIGVsaW1pbmFjacOzbiBkZSBmbGFzaGNhcmQgKi99XG4gICAgICB7c2hvd0RlbGV0ZUNvbmZpcm0gJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB6LTUwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHAtNiBtYXgtdy1tZCB3LWZ1bGwgbXgtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBtYi00XCI+XG4gICAgICAgICAgICAgIDxGaUFsZXJ0VHJpYW5nbGUgY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwIG1yLTNcIiAvPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkXCI+Q29uZmlybWFyIGVsaW1pbmFjacOzbjwvaDM+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi02XCI+XG4gICAgICAgICAgICAgIMK/RXN0w6FzIHNlZ3VybyBkZSBxdWUgcXVpZXJlcyBlbGltaW5hciBlc3RhIGZsYXNoY2FyZD8gRXN0YSBhY2Npw7NuIG5vIHNlIHB1ZWRlIGRlc2hhY2VyIHkgc2UgcGVyZGVyw6EgdG9kbyBlbCBwcm9ncmVzbyBhc29jaWFkby5cbiAgICAgICAgICAgIDwvcD5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0RlbGV0ZUNvbmZpcm0obnVsbCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIHRleHQtZ3JheS02MDAgaG92ZXI6YmctZ3JheS0xMDAgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2RlbGV0aW5nSWQgIT09IG51bGx9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBDYW5jZWxhclxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUVsaW1pbmFyRmxhc2hjYXJkKHNob3dEZWxldGVDb25maXJtKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTIgYmctcmVkLTUwMCB0ZXh0LXdoaXRlIGhvdmVyOmJnLXJlZC02MDAgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2RlbGV0aW5nSWQgIT09IG51bGx9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBFbGltaW5hclxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBEacOhbG9nbyBkZSBjb25maXJtYWNpw7NuIGRlIGVsaW1pbmFjacOzbiBkZSBjb2xlY2Npw7NuICovfVxuICAgICAge3Nob3dEZWxldGVDb2xsZWN0aW9uQ29uZmlybSAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjayBiZy1vcGFjaXR5LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHotNTBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgcC02IG1heC13LW1kIHctZnVsbCBteC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIG1iLTRcIj5cbiAgICAgICAgICAgICAgPEZpQWxlcnRUcmlhbmdsZSBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDAgbXItM1wiIC8+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGRcIj5Db25maXJtYXIgZWxpbWluYWNpw7NuIGRlIGNvbGVjY2nDs248L2gzPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNlwiPlxuICAgICAgICAgICAgICDCv0VzdMOhcyBzZWd1cm8gZGUgcXVlIHF1aWVyZXMgZWxpbWluYXIgZXN0YSBjb2xlY2Npw7NuIGNvbXBsZXRhPyBFc3RhIGFjY2nDs24gbm8gc2UgcHVlZGUgZGVzaGFjZXIgeSBzZSBlbGltaW5hcsOhbjpcbiAgICAgICAgICAgIDwvcD5cblxuICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNiBsaXN0LWRpc2MgbGlzdC1pbnNpZGUgc3BhY2UteS0xXCI+XG4gICAgICAgICAgICAgIDxsaT5Ub2RhcyBsYXMgZmxhc2hjYXJkcyBkZSBsYSBjb2xlY2Npw7NuPC9saT5cbiAgICAgICAgICAgICAgPGxpPlRvZG8gZWwgcHJvZ3Jlc28gZGUgZXN0dWRpbzwvbGk+XG4gICAgICAgICAgICAgIDxsaT5FbCBoaXN0b3JpYWwgZGUgcmV2aXNpb25lczwvbGk+XG4gICAgICAgICAgICAgIDxsaT5MYXMgZXN0YWTDrXN0aWNhcyBhc29jaWFkYXM8L2xpPlxuICAgICAgICAgICAgPC91bD5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0RlbGV0ZUNvbGxlY3Rpb25Db25maXJtKG51bGwpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiB0ZXh0LWdyYXktNjAwIGhvdmVyOmJnLWdyYXktMTAwIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtkZWxldGluZ0NvbGxlY3Rpb25JZCAhPT0gbnVsbH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIENhbmNlbGFyXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gY29uZmlybWFyRWxpbWluYXJDb2xlY2Npb24oc2hvd0RlbGV0ZUNvbGxlY3Rpb25Db25maXJtKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTIgYmctcmVkLTUwMCB0ZXh0LXdoaXRlIGhvdmVyOmJnLXJlZC02MDAgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2RlbGV0aW5nQ29sbGVjdGlvbklkICE9PSBudWxsfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgRWxpbWluYXIgY29sZWNjacOzblxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBGbGFzaGNhcmRWaWV3ZXI7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIm9idGVuZXJDb2xlY2Npb25lc0ZsYXNoY2FyZHMiLCJvYnRlbmVyRmxhc2hjYXJkc1BhcmFFc3R1ZGlhciIsInJlZ2lzdHJhclJlc3B1ZXN0YUZsYXNoY2FyZCIsIm9idGVuZXJFc3RhZGlzdGljYXNDb2xlY2Npb24iLCJlbGltaW5hckZsYXNoY2FyZCIsImVsaW1pbmFyQ29sZWNjaW9uRmxhc2hjYXJkcyIsIkZpRWRpdDIiLCJGaVRyYXNoMiIsIkZpQWxlcnRUcmlhbmdsZSIsIkZpUmVmcmVzaEN3IiwiRmxhc2hjYXJkQ29sbGVjdGlvbkxpc3QiLCJGbGFzaGNhcmRTdGF0aXN0aWNzIiwiRmxhc2hjYXJkU3R1ZHlNb2RlIiwiRmxhc2hjYXJkRWRpdE1vZGFsIiwidG9hc3QiLCJGbGFzaGNhcmRWaWV3ZXIiLCJjb2xlY2Npb25lcyIsInNldENvbGVjY2lvbmVzIiwiY29sZWNjaW9uU2VsZWNjaW9uYWRhIiwic2V0Q29sZWNjaW9uU2VsZWNjaW9uYWRhIiwiZmxhc2hjYXJkcyIsInNldEZsYXNoY2FyZHMiLCJhY3RpdmVJbmRleCIsInNldEFjdGl2ZUluZGV4IiwibW9zdHJhclJlc3B1ZXN0YSIsInNldE1vc3RyYXJSZXNwdWVzdGEiLCJyZXNwb25kaWVuZG8iLCJzZXRSZXNwb25kaWVuZG8iLCJtb2RvRXN0dWRpbyIsInNldE1vZG9Fc3R1ZGlvIiwiZXN0YWRpc3RpY2FzIiwic2V0RXN0YWRpc3RpY2FzIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsImZsYXNoY2FyZEVkaXRhbmRvIiwic2V0Rmxhc2hjYXJkRWRpdGFuZG8iLCJzaG93RWRpdE1vZGFsIiwic2V0U2hvd0VkaXRNb2RhbCIsInNob3dEZWxldGVDb25maXJtIiwic2V0U2hvd0RlbGV0ZUNvbmZpcm0iLCJkZWxldGluZ0lkIiwic2V0RGVsZXRpbmdJZCIsInNob3dEZWxldGVDb2xsZWN0aW9uQ29uZmlybSIsInNldFNob3dEZWxldGVDb2xsZWN0aW9uQ29uZmlybSIsImRlbGV0aW5nQ29sbGVjdGlvbklkIiwic2V0RGVsZXRpbmdDb2xsZWN0aW9uSWQiLCJjYXJnYXJDb2xlY2Npb25lcyIsImRhdGEiLCJjb25zb2xlIiwiaGFuZGxlU2VsZWNjaW9uYXJDb2xlY2Npb24iLCJjb2xlY2Npb24iLCJpZCIsIm9yZGVuYWRhcyIsInNvcnQiLCJhIiwiYiIsImRlYmVFc3R1ZGlhciIsInN0YXRzIiwiaW5pY2lhck1vZG9Fc3R1ZGlvIiwiZmxhc2hjYXJkc1BhcmFFc3R1ZGlhciIsImZpbHRlciIsImZsYXNoY2FyZCIsImxlbmd0aCIsInBhcmFIb3kiLCJ3YXJuIiwiYWxlcnQiLCJoYW5kbGVOYXZpZ2F0ZSIsImRpcmVjdGlvbiIsImhhbmRsZVJlc3B1ZXN0YSIsImRpZmljdWx0YWQiLCJmbGFzaGNhcmRJZCIsImhhbmRsZVNhbGlyTW9kb0VzdHVkaW8iLCJoYW5kbGVFZGl0YXJGbGFzaGNhcmQiLCJoYW5kbGVHdWFyZGFyRmxhc2hjYXJkIiwiZmxhc2hjYXJkQWN0dWFsaXphZGEiLCJwcmV2IiwibWFwIiwiZmMiLCJoYW5kbGVFbGltaW5hckZsYXNoY2FyZCIsImxvYWRpbmdUb2FzdElkIiwibG9hZGluZyIsInN1Y2Nlc3MiLCJoYW5kbGVFbGltaW5hckNvbGVjY2lvbiIsImNvbGVjY2lvbklkIiwiY29uZmlybWFyRWxpbWluYXJDb2xlY2Npb24iLCJjb2wiLCJkaXYiLCJjbGFzc05hbWUiLCJvblJlc3B1ZXN0YSIsIm9uTmF2aWdhdGUiLCJvblZvbHZlciIsImgyIiwib25TZWxlY2Npb25hckNvbGVjY2lvbiIsIm9uRWxpbWluYXJDb2xlY2Npb24iLCJoMyIsInRpdHVsbyIsImJ1dHRvbiIsIm9uQ2xpY2siLCJwIiwiaW5kZXgiLCJzcGFuIiwicHJvZ3Jlc28iLCJlc3RhZG8iLCJ0aXRsZSIsInNpemUiLCJkaXNhYmxlZCIsImg0IiwicHJlZ3VudGEiLCJyZXNwdWVzdGEiLCJpc09wZW4iLCJvbkNsb3NlIiwib25TYXZlIiwidWwiLCJsaSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/flashcards/FlashcardViewer.tsx\n"));

/***/ })

});