"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiCheck,FiCheckSquare,FiChevronRight,FiFileText,FiLayers,FiList,FiLogOut,FiMessageSquare,FiUpload!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _components_DocumentSelector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/DocumentSelector */ \"(app-pages-browser)/./src/components/DocumentSelector.tsx\");\n/* harmony import */ var _components_QuestionForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/QuestionForm */ \"(app-pages-browser)/./src/components/QuestionForm.tsx\");\n/* harmony import */ var _components_DocumentUploader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/DocumentUploader */ \"(app-pages-browser)/./src/components/DocumentUploader.tsx\");\n/* harmony import */ var _components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/MindMapGenerator */ \"(app-pages-browser)/./src/components/MindMapGenerator.tsx\");\n/* harmony import */ var _components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/FlashcardGenerator */ \"(app-pages-browser)/./src/components/FlashcardGenerator.tsx\");\n/* harmony import */ var _components_flashcards_FlashcardViewer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/flashcards/FlashcardViewer */ \"(app-pages-browser)/./src/components/flashcards/FlashcardViewer.tsx\");\n/* harmony import */ var _components_TestGenerator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../components/TestGenerator */ \"(app-pages-browser)/./src/components/TestGenerator.tsx\");\n/* harmony import */ var _components_TestViewer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../components/TestViewer */ \"(app-pages-browser)/./src/components/TestViewer.tsx\");\n/* harmony import */ var _components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../components/MobileDebugInfo */ \"(app-pages-browser)/./src/components/MobileDebugInfo.tsx\");\n/* harmony import */ var _components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../components/DiagnosticPanel */ \"(app-pages-browser)/./src/components/DiagnosticPanel.tsx\");\n/* harmony import */ var _components_AuthDebug__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../components/AuthDebug */ \"(app-pages-browser)/./src/components/AuthDebug.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\", _this = undefined, _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar TabButton = function TabButton(_ref) {\n    var active = _ref.active, onClick = _ref.onClick, icon = _ref.icon, label = _ref.label, color = _ref.color;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        className: \"flex items-center px-4 py-3 rounded-lg transition-all duration-200 font-medium text-sm \".concat(active ? \"text-white \".concat(color, \" shadow-md\") : 'text-gray-600 hover:bg-gray-100 hover:text-gray-800'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 5\n            }, _this),\n            label,\n            active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_17__.FiChevronRight, {\n                className: \"ml-2\"\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 16\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 3\n    }, _this);\n};\n_c1 = TabButton;\n_c = TabButton;\nfunction Home() {\n    _s();\n    _s1();\n    var _user$email, _this2 = this;\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]), documentosSeleccionados = _useState[0], setDocumentosSeleccionados = _useState[1];\n    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), mostrarUploader = _useState2[0], setMostrarUploader = _useState2[1];\n    var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('preguntas'), activeTab = _useState3[0], setActiveTab = _useState3[1];\n    var _useState4 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), showUploadSuccess = _useState4[0], setShowUploadSuccess = _useState4[1];\n    var _useAuth = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_15__.useAuth)(), cerrarSesion = _useAuth.cerrarSesion, user = _useAuth.user, isLoading = _useAuth.isLoading;\n    var router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // No necesitamos verificar autenticación aquí, el middleware ya lo hace\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Home.useEffect\": function() {\n            console.log('[HomePage] Auth state - isLoading:', isLoading, 'User:', !!user);\n        }\n    }[\"Home.useEffect\"], [\n        user,\n        isLoading\n    ]);\n    // Si está cargando o no hay usuario, mostrar pantalla de carga\n    if (isLoading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 7\n        }, this);\n    }\n    var handleUploadSuccess = function handleUploadSuccess() {\n        setShowUploadSuccess(true);\n        setTimeout(function() {\n            return setShowUploadSuccess(false);\n        }, 3000);\n    };\n    var handleLogout = /*#__PURE__*/ function() {\n        var _ref2 = (0,C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee() {\n            return C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee$(_context) {\n                while(1)switch(_context.prev = _context.next){\n                    case 0:\n                        _context.next = 2;\n                        return cerrarSesion();\n                    case 2:\n                    case \"end\":\n                        return _context.stop();\n                }\n            }, _callee);\n        }));\n        return function handleLogout() {\n            return _ref2.apply(this, arguments);\n        };\n    }();\n    var tabs = [\n        {\n            id: 'preguntas',\n            label: 'Preguntas y Respuestas',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_17__.FiMessageSquare, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 63\n            }, this),\n            color: 'bg-blue-600'\n        },\n        {\n            id: 'mapas',\n            label: 'Mapas Mentales',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_17__.FiLayers, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 51\n            }, this),\n            color: 'bg-purple-600'\n        },\n        {\n            id: 'flashcards',\n            label: 'Generar Flashcards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_17__.FiFileText, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 60\n            }, this),\n            color: 'bg-orange-500'\n        },\n        {\n            id: 'tests',\n            label: 'Generar Tests',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_17__.FiList, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 50\n            }, this),\n            color: 'bg-indigo-600'\n        },\n        {\n            id: 'misFlashcards',\n            label: 'Mis Flashcards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_17__.FiBook, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 59\n            }, this),\n            color: 'bg-emerald-600'\n        },\n        {\n            id: 'misTests',\n            label: 'Mis Tests',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_17__.FiCheckSquare, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 49\n            }, this),\n            color: 'bg-pink-600'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                        children: \"OposiAI\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Tu asistente inteligente para oposiciones\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Hola, \",\n                                            (_user$email = user.email) === null || _user$email === void 0 ? void 0 : _user$email.split('@')[0]\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 102,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"button\", {\n                                        onClick: function onClick() {\n                                            return setMostrarUploader(!mostrarUploader);\n                                        },\n                                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_17__.FiUpload, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this),\n                                            mostrarUploader ? 'Ocultar formulario' : 'Nuevo documento'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_17__.FiLogOut, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cerrar sesi\\xF3n\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(_components_AuthDebug__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this),\n                    mostrarUploader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"div\", {\n                        className: \"mb-8 transition-all duration-300 ease-in-out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            onSuccess: handleUploadSuccess\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this),\n                    showUploadSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_17__.FiCheck, {\n                                className: \"text-green-500 mr-2\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this),\n                            \"Documento subido exitosamente\"\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm p-4 sticky top-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"h2\", {\n                                            className: \"text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4 px-2\",\n                                            children: \"Men\\xFA de Estudio\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"nav\", {\n                                            className: \"space-y-1\",\n                                            children: tabs.map(function(tab) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(TabButton, {\n                                                    active: activeTab === tab.id,\n                                                    onClick: function onClick() {\n                                                        return setActiveTab(tab.id);\n                                                    },\n                                                    icon: tab.icon,\n                                                    label: tab.label,\n                                                    color: tab.color\n                                                }, tab.id, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, _this2);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"div\", {\n                                            className: \"mt-8 pt-6 border-t border-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-2\",\n                                                    children: \"Documentos Seleccionados\"\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    onSelectionChange: setDocumentosSeleccionados\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            activeTab === 'preguntas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(_components_QuestionForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 177,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === 'mapas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 181,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === 'flashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === 'tests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(_components_TestGenerator__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === 'misTests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(_components_TestViewer__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 192,\n                                                columnNumber: 46\n                                            }, this),\n                                            activeTab === 'misFlashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(_components_flashcards_FlashcardViewer__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 194,\n                                                columnNumber: 51\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200 mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500 text-sm\",\n                                    children: [\n                                        \"\\xA9 \",\n                                        new Date().getFullYear(),\n                                        \" OposiAI - Asistente para Oposiciones\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"div\", {\n                                className: \"mt-4 md:mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"T\\xE9rminos\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Privacidad\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Contacto\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 218,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 211,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxDEV)(_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"clDTlQWFFTIVZfcpGjL/YJjs5II=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_15__.useAuth,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_15__.useAuth,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_15__.useAuth,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_15__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c3 = Home;\n_s1(Home, \"LF6hBl5V53q5abOaRwPSUcde4Ro=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_15__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c2 = Home;\nvar _c, _c2;\n$RefreshReg$(_c, \"TabButton\");\n$RefreshReg$(_c2, \"Home\");\nvar _c1, _c3;\n$RefreshReg$(_c1, \"TabButton\");\n$RefreshReg$(_c3, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/AuthDebug.tsx":
/*!**************************************!*\
  !*** ./src/components/AuthDebug.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthDebug)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\nvar _s = $RefreshSig$();\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\AuthDebug.tsx\", _s1 = $RefreshSig$();\n\n\n\nfunction AuthDebug() {\n    _s();\n    _s1();\n    var _useAuth = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)(), user = _useAuth.user, session = _useAuth.session, isLoading = _useAuth.isLoading, error = _useAuth.error;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"div\", {\n        className: \"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"h3\", {\n                className: \"font-bold\",\n                children: \"Debug de Autenticaci\\xF3n\"\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 9,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"div\", {\n                className: \"text-sm mt-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"strong\", {\n                                children: \"Usuario:\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 11,\n                                columnNumber: 12\n                            }, this),\n                            \" \",\n                            user ? user.email : 'No autenticado'\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"strong\", {\n                                children: \"Sesi\\xF3n:\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 12,\n                                columnNumber: 12\n                            }, this),\n                            \" \",\n                            session ? 'Activa' : 'Inactiva'\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"strong\", {\n                                children: \"Cargando:\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 13,\n                                columnNumber: 12\n                            }, this),\n                            \" \",\n                            isLoading ? 'Sí' : 'No'\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"strong\", {\n                                children: \"Error:\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 14,\n                                columnNumber: 12\n                            }, this),\n                            \" \",\n                            error || 'Ninguno'\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"strong\", {\n                                children: \"Token:\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 15,\n                                columnNumber: 12\n                            }, this),\n                            \" \",\n                            session !== null && session !== void 0 && session.access_token ? 'Presente' : 'Ausente'\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"strong\", {\n                                children: \"Expira:\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 16,\n                                columnNumber: 12\n                            }, this),\n                            \" \",\n                            session !== null && session !== void 0 && session.expires_at ? new Date(session.expires_at * 1000).toLocaleString() : 'N/A'\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthDebug, \"ez6/zLt8ck8nI+i8shgmqHLbNqo=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth\n    ];\n});\n_c1 = AuthDebug;\n_s1(AuthDebug, \"C69GEkuftf0IbekoRwYnY+vPI3k=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth\n    ];\n});\n_c = AuthDebug;\nvar _c;\n$RefreshReg$(_c, \"AuthDebug\");\nvar _c1;\n$RefreshReg$(_c1, \"AuthDebug\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AuthDebug.tsx\n"));

/***/ })

});