/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/gemini/route";
exports.ids = ["app/api/gemini/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgemini%2Froute&page=%2Fapi%2Fgemini%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgemini%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgemini%2Froute&page=%2Fapi%2Fgemini%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgemini%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_OposI_v7_src_app_api_gemini_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/gemini/route.ts */ \"(rsc)/./src/app/api/gemini/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/gemini/route\",\n        pathname: \"/api/gemini\",\n        filename: \"route\",\n        bundlePath: \"app/api/gemini/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\api\\\\gemini\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_naata_Documents_augment_projects_OposI_OposI_v7_src_app_api_gemini_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgemini%2Froute&page=%2Fapi%2Fgemini%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgemini%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/gemini/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/gemini/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_gemini__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/gemini */ \"(rsc)/./src/lib/gemini.ts\");\n/* harmony import */ var _lib_gemini_questionService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/gemini/questionService */ \"(rsc)/./src/lib/gemini/questionService.ts\");\n/* harmony import */ var _lib_zodSchemas__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/zodSchemas */ \"(rsc)/./src/lib/zodSchemas.ts\");\n\n\n\n\n\n// API route for Gemini actions\nasync function POST(req) {\n    try {\n        // Crear cliente de Supabase usando la implementación correcta\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabaseClient)();\n        const { data: { user }, error: userError } = await supabase.auth.getUser();\n        // Diagnóstico mejorado para debugging\n        console.log('User check:', {\n            hasUser: !!user,\n            userError: userError?.message,\n            userId: user?.id,\n            cookies: req.headers.get('cookie')?.includes('supabase') ? 'present' : 'missing'\n        });\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized',\n                debug: {\n                    userError: userError?.message,\n                    hasCookies: !!req.headers.get('cookie')\n                }\n            }, {\n                status: 401\n            });\n        }\n        const body = await req.json();\n        console.log('📥 Datos recibidos en API Gemini:', JSON.stringify(body, null, 2));\n        // Validación robusta de entrada\n        const parseResult = _lib_zodSchemas__WEBPACK_IMPORTED_MODULE_4__.ApiGeminiInputSchema.safeParse(body);\n        if (!parseResult.success) {\n            console.error('❌ Error de validación en API Gemini:', parseResult.error.errors);\n            console.error('📄 Datos que causaron el error:', JSON.stringify(body, null, 2));\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Datos inválidos',\n                detalles: parseResult.error.errors,\n                datosRecibidos: body\n            }, {\n                status: 400\n            });\n        }\n        console.log('✅ Validación exitosa en API Gemini');\n        // Compatibilidad: si viene pregunta+documentos, es para obtenerRespuestaIA\n        if (body.pregunta && body.documentos) {\n            const result = await (0,_lib_gemini_questionService__WEBPACK_IMPORTED_MODULE_3__.obtenerRespuestaIA)(body.pregunta, body.documentos);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                result\n            });\n        }\n        const { action, peticion, contextos } = body;\n        let result;\n        switch(action){\n            case 'generarTest':\n                result = await (0,_lib_gemini__WEBPACK_IMPORTED_MODULE_2__.generarTest)(peticion, contextos);\n                break;\n            case 'generarFlashcards':\n                result = await (0,_lib_gemini__WEBPACK_IMPORTED_MODULE_2__.generarFlashcards)(peticion, contextos);\n                break;\n            case 'generarMapaMental':\n                result = await (0,_lib_gemini__WEBPACK_IMPORTED_MODULE_2__.generarMapaMental)(peticion, contextos);\n                break;\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Acción no soportada'\n                }, {\n                    status: 400\n                });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            result\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9nZW1pbmkvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXdEO0FBQ1c7QUFDYztBQUNmO0FBQ1Y7QUFFeEQsK0JBQStCO0FBQ3hCLGVBQWVPLEtBQUtDLEdBQWdCO0lBQ3pDLElBQUk7UUFDRiw4REFBOEQ7UUFDOUQsTUFBTUMsV0FBVyxNQUFNUixnRkFBMEJBO1FBRWpELE1BQU0sRUFBRVMsTUFBTSxFQUFFQyxJQUFJLEVBQUUsRUFBRUMsT0FBT0MsU0FBUyxFQUFFLEdBQUcsTUFBTUosU0FBU0ssSUFBSSxDQUFDQyxPQUFPO1FBRXhFLHNDQUFzQztRQUN0Q0MsUUFBUUMsR0FBRyxDQUFDLGVBQWU7WUFDekJDLFNBQVMsQ0FBQyxDQUFDUDtZQUNYRSxXQUFXQSxXQUFXTTtZQUN0QkMsUUFBUVQsTUFBTVU7WUFDZEMsU0FBU2QsSUFBSWUsT0FBTyxDQUFDQyxHQUFHLENBQUMsV0FBV0MsU0FBUyxjQUFjLFlBQVk7UUFDekU7UUFFQSxJQUFJLENBQUNkLE1BQU07WUFDVCxPQUFPWCxxREFBWUEsQ0FBQzBCLElBQUksQ0FBQztnQkFDdkJkLE9BQU87Z0JBQ1BlLE9BQU87b0JBQ0xkLFdBQVdBLFdBQVdNO29CQUN0QlMsWUFBWSxDQUFDLENBQUNwQixJQUFJZSxPQUFPLENBQUNDLEdBQUcsQ0FBQztnQkFDaEM7WUFDRixHQUFHO2dCQUFFSyxRQUFRO1lBQUk7UUFDbkI7UUFFQSxNQUFNQyxPQUFPLE1BQU10QixJQUFJa0IsSUFBSTtRQUMzQlYsUUFBUUMsR0FBRyxDQUFDLHFDQUFxQ2MsS0FBS0MsU0FBUyxDQUFDRixNQUFNLE1BQU07UUFFNUUsZ0NBQWdDO1FBQ2hDLE1BQU1HLGNBQWMzQixpRUFBb0JBLENBQUM0QixTQUFTLENBQUNKO1FBQ25ELElBQUksQ0FBQ0csWUFBWUUsT0FBTyxFQUFFO1lBQ3hCbkIsUUFBUUosS0FBSyxDQUFDLHdDQUF3Q3FCLFlBQVlyQixLQUFLLENBQUN3QixNQUFNO1lBQzlFcEIsUUFBUUosS0FBSyxDQUFDLG1DQUFtQ21CLEtBQUtDLFNBQVMsQ0FBQ0YsTUFBTSxNQUFNO1lBQzVFLE9BQU85QixxREFBWUEsQ0FBQzBCLElBQUksQ0FBQztnQkFDdkJkLE9BQU87Z0JBQ1B5QixVQUFVSixZQUFZckIsS0FBSyxDQUFDd0IsTUFBTTtnQkFDbENFLGdCQUFnQlI7WUFDbEIsR0FBRztnQkFBRUQsUUFBUTtZQUFJO1FBQ25CO1FBRUFiLFFBQVFDLEdBQUcsQ0FBQztRQUNaLDJFQUEyRTtRQUMzRSxJQUFJYSxLQUFLUyxRQUFRLElBQUlULEtBQUtVLFVBQVUsRUFBRTtZQUNwQyxNQUFNQyxTQUFTLE1BQU1wQywrRUFBa0JBLENBQUN5QixLQUFLUyxRQUFRLEVBQUVULEtBQUtVLFVBQVU7WUFDdEUsT0FBT3hDLHFEQUFZQSxDQUFDMEIsSUFBSSxDQUFDO2dCQUFFZTtZQUFPO1FBQ3BDO1FBQ0EsTUFBTSxFQUFFQyxNQUFNLEVBQUVDLFFBQVEsRUFBRUMsU0FBUyxFQUFFLEdBQUdkO1FBQ3hDLElBQUlXO1FBQ0osT0FBUUM7WUFDTixLQUFLO2dCQUNIRCxTQUFTLE1BQU12Qyx3REFBV0EsQ0FBQ3lDLFVBQVVDO2dCQUNyQztZQUNGLEtBQUs7Z0JBQ0hILFNBQVMsTUFBTXRDLDhEQUFpQkEsQ0FBQ3dDLFVBQVVDO2dCQUMzQztZQUNGLEtBQUs7Z0JBQ0hILFNBQVMsTUFBTXJDLDhEQUFpQkEsQ0FBQ3VDLFVBQVVDO2dCQUMzQztZQUNGO2dCQUNFLE9BQU81QyxxREFBWUEsQ0FBQzBCLElBQUksQ0FBQztvQkFBRWQsT0FBTztnQkFBc0IsR0FBRztvQkFBRWlCLFFBQVE7Z0JBQUk7UUFDN0U7UUFDQSxPQUFPN0IscURBQVlBLENBQUMwQixJQUFJLENBQUM7WUFBRWU7UUFBTztJQUNwQyxFQUFFLE9BQU83QixPQUFPO1FBQ2QsT0FBT1oscURBQVlBLENBQUMwQixJQUFJLENBQUM7WUFBRWQsT0FBTyxNQUFpQk8sT0FBTztRQUFDLEdBQUc7WUFBRVUsUUFBUTtRQUFJO0lBQzlFO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXE9wb3NJIHY3XFxzcmNcXGFwcFxcYXBpXFxnZW1pbmlcXHJvdXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcic7XG5pbXBvcnQgeyBjcmVhdGVTZXJ2ZXJTdXBhYmFzZUNsaWVudCB9IGZyb20gJ0AvbGliL3N1cGFiYXNlL3NlcnZlcic7XG5pbXBvcnQgeyBnZW5lcmFyVGVzdCwgZ2VuZXJhckZsYXNoY2FyZHMsIGdlbmVyYXJNYXBhTWVudGFsIH0gZnJvbSAnQC9saWIvZ2VtaW5pJztcbmltcG9ydCB7IG9idGVuZXJSZXNwdWVzdGFJQSB9IGZyb20gJ0AvbGliL2dlbWluaS9xdWVzdGlvblNlcnZpY2UnO1xuaW1wb3J0IHsgQXBpR2VtaW5pSW5wdXRTY2hlbWEgfSBmcm9tICdAL2xpYi96b2RTY2hlbWFzJztcblxuLy8gQVBJIHJvdXRlIGZvciBHZW1pbmkgYWN0aW9uc1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxOiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIC8vIENyZWFyIGNsaWVudGUgZGUgU3VwYWJhc2UgdXNhbmRvIGxhIGltcGxlbWVudGFjacOzbiBjb3JyZWN0YVxuICAgIGNvbnN0IHN1cGFiYXNlID0gYXdhaXQgY3JlYXRlU2VydmVyU3VwYWJhc2VDbGllbnQoKTtcblxuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0sIGVycm9yOiB1c2VyRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpO1xuXG4gICAgLy8gRGlhZ27Ds3N0aWNvIG1lam9yYWRvIHBhcmEgZGVidWdnaW5nXG4gICAgY29uc29sZS5sb2coJ1VzZXIgY2hlY2s6Jywge1xuICAgICAgaGFzVXNlcjogISF1c2VyLFxuICAgICAgdXNlckVycm9yOiB1c2VyRXJyb3I/Lm1lc3NhZ2UsXG4gICAgICB1c2VySWQ6IHVzZXI/LmlkLFxuICAgICAgY29va2llczogcmVxLmhlYWRlcnMuZ2V0KCdjb29raWUnKT8uaW5jbHVkZXMoJ3N1cGFiYXNlJykgPyAncHJlc2VudCcgOiAnbWlzc2luZydcbiAgICB9KTtcblxuICAgIGlmICghdXNlcikge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgZXJyb3I6ICdVbmF1dGhvcml6ZWQnLFxuICAgICAgICBkZWJ1Zzoge1xuICAgICAgICAgIHVzZXJFcnJvcjogdXNlckVycm9yPy5tZXNzYWdlLFxuICAgICAgICAgIGhhc0Nvb2tpZXM6ICEhcmVxLmhlYWRlcnMuZ2V0KCdjb29raWUnKVxuICAgICAgICB9XG4gICAgICB9LCB7IHN0YXR1czogNDAxIH0pO1xuICAgIH1cblxuICAgIGNvbnN0IGJvZHkgPSBhd2FpdCByZXEuanNvbigpO1xuICAgIGNvbnNvbGUubG9nKCfwn5OlIERhdG9zIHJlY2liaWRvcyBlbiBBUEkgR2VtaW5pOicsIEpTT04uc3RyaW5naWZ5KGJvZHksIG51bGwsIDIpKTtcblxuICAgIC8vIFZhbGlkYWNpw7NuIHJvYnVzdGEgZGUgZW50cmFkYVxuICAgIGNvbnN0IHBhcnNlUmVzdWx0ID0gQXBpR2VtaW5pSW5wdXRTY2hlbWEuc2FmZVBhcnNlKGJvZHkpO1xuICAgIGlmICghcGFyc2VSZXN1bHQuc3VjY2Vzcykge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIEVycm9yIGRlIHZhbGlkYWNpw7NuIGVuIEFQSSBHZW1pbmk6JywgcGFyc2VSZXN1bHQuZXJyb3IuZXJyb3JzKTtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ/Cfk4QgRGF0b3MgcXVlIGNhdXNhcm9uIGVsIGVycm9yOicsIEpTT04uc3RyaW5naWZ5KGJvZHksIG51bGwsIDIpKTtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgIGVycm9yOiAnRGF0b3MgaW52w6FsaWRvcycsXG4gICAgICAgIGRldGFsbGVzOiBwYXJzZVJlc3VsdC5lcnJvci5lcnJvcnMsXG4gICAgICAgIGRhdG9zUmVjaWJpZG9zOiBib2R5XG4gICAgICB9LCB7IHN0YXR1czogNDAwIH0pO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCfinIUgVmFsaWRhY2nDs24gZXhpdG9zYSBlbiBBUEkgR2VtaW5pJyk7XG4gICAgLy8gQ29tcGF0aWJpbGlkYWQ6IHNpIHZpZW5lIHByZWd1bnRhK2RvY3VtZW50b3MsIGVzIHBhcmEgb2J0ZW5lclJlc3B1ZXN0YUlBXG4gICAgaWYgKGJvZHkucHJlZ3VudGEgJiYgYm9keS5kb2N1bWVudG9zKSB7XG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBvYnRlbmVyUmVzcHVlc3RhSUEoYm9keS5wcmVndW50YSwgYm9keS5kb2N1bWVudG9zKTtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IHJlc3VsdCB9KTtcbiAgICB9XG4gICAgY29uc3QgeyBhY3Rpb24sIHBldGljaW9uLCBjb250ZXh0b3MgfSA9IGJvZHk7XG4gICAgbGV0IHJlc3VsdDtcbiAgICBzd2l0Y2ggKGFjdGlvbikge1xuICAgICAgY2FzZSAnZ2VuZXJhclRlc3QnOlxuICAgICAgICByZXN1bHQgPSBhd2FpdCBnZW5lcmFyVGVzdChwZXRpY2lvbiwgY29udGV4dG9zKTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlICdnZW5lcmFyRmxhc2hjYXJkcyc6XG4gICAgICAgIHJlc3VsdCA9IGF3YWl0IGdlbmVyYXJGbGFzaGNhcmRzKHBldGljaW9uLCBjb250ZXh0b3MpO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgJ2dlbmVyYXJNYXBhTWVudGFsJzpcbiAgICAgICAgcmVzdWx0ID0gYXdhaXQgZ2VuZXJhck1hcGFNZW50YWwocGV0aWNpb24sIGNvbnRleHRvcyk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdBY2Npw7NuIG5vIHNvcG9ydGFkYScgfSwgeyBzdGF0dXM6IDQwMCB9KTtcbiAgICB9XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgcmVzdWx0IH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAoZXJyb3IgYXMgRXJyb3IpLm1lc3NhZ2UgfSwgeyBzdGF0dXM6IDUwMCB9KTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImNyZWF0ZVNlcnZlclN1cGFiYXNlQ2xpZW50IiwiZ2VuZXJhclRlc3QiLCJnZW5lcmFyRmxhc2hjYXJkcyIsImdlbmVyYXJNYXBhTWVudGFsIiwib2J0ZW5lclJlc3B1ZXN0YUlBIiwiQXBpR2VtaW5pSW5wdXRTY2hlbWEiLCJQT1NUIiwicmVxIiwic3VwYWJhc2UiLCJkYXRhIiwidXNlciIsImVycm9yIiwidXNlckVycm9yIiwiYXV0aCIsImdldFVzZXIiLCJjb25zb2xlIiwibG9nIiwiaGFzVXNlciIsIm1lc3NhZ2UiLCJ1c2VySWQiLCJpZCIsImNvb2tpZXMiLCJoZWFkZXJzIiwiZ2V0IiwiaW5jbHVkZXMiLCJqc29uIiwiZGVidWciLCJoYXNDb29raWVzIiwic3RhdHVzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJwYXJzZVJlc3VsdCIsInNhZmVQYXJzZSIsInN1Y2Nlc3MiLCJlcnJvcnMiLCJkZXRhbGxlcyIsImRhdG9zUmVjaWJpZG9zIiwicHJlZ3VudGEiLCJkb2N1bWVudG9zIiwicmVzdWx0IiwiYWN0aW9uIiwicGV0aWNpb24iLCJjb250ZXh0b3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/gemini/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/prompts.ts":
/*!*******************************!*\
  !*** ./src/config/prompts.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PROMPT_FLASHCARDS: () => (/* binding */ PROMPT_FLASHCARDS),\n/* harmony export */   PROMPT_MAPAS_MENTALES: () => (/* binding */ PROMPT_MAPAS_MENTALES),\n/* harmony export */   PROMPT_PREGUNTAS: () => (/* binding */ PROMPT_PREGUNTAS),\n/* harmony export */   PROMPT_TESTS: () => (/* binding */ PROMPT_TESTS)\n/* harmony export */ });\n/**\n * Configuración de prompts personalizados para cada funcionalidad de la aplicación\n *\n * Este archivo centraliza todos los prompts que se utilizan en la aplicación,\n * permitiendo personalizarlos fácilmente sin tener que modificar el código de los servicios.\n */ /**\n * Prompt para la pantalla de preguntas y respuestas\n *\n * Variables disponibles:\n * - {documentos}: Contenido de los documentos seleccionados\n * - {pregunta}: Pregunta del usuario\n */ const PROMPT_PREGUNTAS = `\nEres \"Mentor Opositor AI\", un preparador de oposiciones virtual altamente cualificado y con amplia experiencia. Tu misión principal es ayudar al usuario a comprender a fondo los temas del temario, resolver sus dudas y, en última instancia, maximizar sus posibilidades de obtener una plaza. Tu tono debe ser profesional, claro, didáctico, motivador y empático.\n\nResponde SIEMPRE en español.\n\nCONTEXTO DEL TEMARIO (Información base para tus explicaciones):\n{documentos}\n\nPREGUNTA DEL OPOSITOR/A:\n{pregunta}\n\nINSTRUCCIONES DETALLADAS PARA ACTUAR COMO \"MENTOR OPOSITOR AI\":\n\nI. PRINCIPIOS GENERALES DE RESPUESTA:\n\n1.  Adaptabilidad de la Extensión y Tono Inicial:\n    -   Inicio de Respuesta: Ve al grano. No es necesario comenzar cada respuesta con frases como \"¡Excelente pregunta!\". Puedes usar una frase validando la pregunta o mostrando empatía de forma ocasional y variada, solo si realmente aporta valor o la pregunta es particularmente compleja o bien formulada. En la mayoría de los casos, es mejor empezar directamente con la información solicitada.\n    -   Preguntas Específicas sobre Contenido: Si la pregunta es sobre un concepto, definición, detalle del temario, o pide una explicación profunda de una sección, puedes extenderte para asegurar una comprensión completa, siempre basándote en el CONTEXTO.\n    -   Preguntas sobre Estructura, Planificación o Consejos Generales: Si la pregunta es sobre cómo abordar el estudio de un tema, cuáles son sus apartados principales, o pide consejos generales, sé estratégico y conciso. Evita resumir todo el contenido del tema. Céntrate en el método, la estructura o los puntos clave de forma resumida.\n    -   Claridad ante Todo: Independientemente de la extensión, la claridad y la precisión son primordiales.\n\n2.  Respuesta Basada en el Contexto (Precisión Absoluta):\n    -   Tu respuesta DEBE basarse ESTRICTA y ÚNICAMENTE en la información proporcionada en el \"CONTEXTO DEL TEMARIO\".\n    -   Si la información necesaria no está en el contexto, indícalo claramente (e.g., \"El temario que me has proporcionado aborda X de esta manera... Para un detalle más exhaustivo sobre Y, sería necesario consultar fuentes complementarias.\"). NO INVENTES INFORMACIÓN.\n    -   Cita textualmente partes relevantes del contexto solo cuando sea indispensable para la precisión o para ilustrar un punto crucial, introduciéndolas de forma natural.\n\nII. FORMATO DE LISTAS JERÁRQUICAS (CUANDO APLIQUE):\nAl presentar información estructurada, como los apartados de un tema, utiliza el siguiente formato de lista jerárquica ESTRICTO:\nEjemplo de formato:\n1.  Apartado Principal Uno\n    a)  Subapartado Nivel 1\n        -   Elemento Nivel 2 (con un guion y espacio)\n            *   Detalle Nivel 3 (con un asterisco y espacio)\n    b)  Otro Subapartado Nivel 1\n2.  Apartado Principal Dos\n    a)  Subapartado...\n\n-   Utiliza números seguidos de un punto (1., 2.) para el nivel más alto.\n-   Utiliza letras minúsculas seguidas de un paréntesis (a), b)) para el segundo nivel, indentado.\n-   Utiliza un guion seguido de un espacio ('- ') para el tercer nivel, indentado bajo el anterior.\n-   Utiliza un asterisco seguido de un espacio ('* ') para el cuarto nivel (o niveles subsiguientes), indentado bajo el anterior.\n-   Asegúrate de que la indentación sea clara para reflejar la jerarquía.\n-   NO uses formato markdown de énfasis (como dobles asteriscos) para los títulos de los elementos de la lista en TU SALIDA; la propia estructura jerárquica y la numeración/viñeta son suficientes.\n\nIII. TIPOS DE RESPUESTA Y ENFOQUES ESPECÍFICOS:\n\nA.  Si la PREGUNTA es sobre \"CUÁLES SON LOS APARTADOS DE UN TEMA\" o \"ESTRUCTURA DEL TEMA\":\n    -   Formato de Respuesta: Utiliza el FORMATO DE LISTAS JERÁRQUICAS detallado en la sección II.\n    -   Contenido por Elemento de Lista:\n        1.  Apartados Principales (Nivel 1 - Números): Indica su título exacto o una paráfrasis muy fiel. A continuación, en 1-2 frases concisas, describe su propósito general.\n        2.  Subapartados (Nivel 2 - Letras): Solo el título o idea principal en muy pocas palabras.\n        3.  Niveles Inferiores (Guion, Asterisco): Solo el título o idea principal en muy pocas palabras.\n    -   El objetivo es mostrar la ESTRUCTURA, no detallar el contenido aquí.\n    -   Sugerencia General de Abordaje (Opcional y Muy Breve al final): Puedes añadir una frase sugiriendo un orden de estudio.\n    -   Qué EVITAR: Descripciones largas del contenido de cada elemento de la lista. Párrafos extensos dentro de la lista.\n\nB.  Si la PREGUNTA es sobre CÓMO ESTUDIAR UN TEMA (enfoque metodológico):\n    -   Enfoque Estratégico y Conciso:\n        1.  Visión General Breve.\n        2.  Para cada bloque principal del tema (puedes usar el Nivel 1 del formato de lista): Indica brevemente su objetivo (1-2 frases) y sugiere 1-2 acciones o técnicas de estudio clave y concretas.\n        3.  Menciona 2-3 Puntos Transversales Críticos (si los hay).\n        4.  Consejo General Final.\n    -   Qué EVITAR: Resumir detalladamente el contenido al explicar la técnica. Uso excesivo de énfasis.\n\nC.  Si la PREGUNTA es sobre un CONCEPTO ESPECÍFICO, DETALLE DEL TEMARIO o PIDE UNA EXPLICACIÓN PROFUNDA:\n    -   Enfoque Explicativo y Didáctico (Puedes Extenderte):\n        (Mantener las sub-instrucciones de explicación detallada: Definición, Terminología, Relevancia, Puntos Clave, Ejemplos, Conexiones).\n        -   Si necesitas desglosar una explicación en múltiples puntos, puedes usar el FORMATO DE LISTAS JERÁRQUICAS de la sección II.\n\nIV. ESTILO Y CIERRE (PARA TODAS LAS RESPUESTAS):\n\n1.  Claridad y Estructura: Utiliza párrafos bien definidos. Cuando uses listas, sigue el formato especificado.\n2.  Tono: Profesional, didáctico, paciente, motivador y positivo. Sé directo y ve al grano, especialmente al inicio de la respuesta.\n3.  Cierre:\n    -   Finaliza ofreciendo más ayuda o preguntando si la explicación ha sido clara (e.g., \"¿Queda clara la estructura así?\", \"¿Necesitas que profundicemos en algún punto de estos apartados?\").\n    -   Termina con una frase de ánimo variada y natural, no siempre la misma.\n\nPRIORIDAD MÁXIMA: La exactitud basada en el CONTEXTO es innegociable. La adaptabilidad en la extensión y el formato deben servir para mejorar la claridad y utilidad de la respuesta, no para introducir información no contextual.\n\n`;\n/**\n * Prompt para la generación de flashcards\n *\n * Variables disponibles:\n * - {documentos}: Contenido de los documentos seleccionados\n * - {cantidad}: Número de flashcards a generar\n * - {instrucciones}: Instrucciones adicionales (opcional)\n */ const PROMPT_FLASHCARDS = `\nEres \"Mentor Opositor AI\", un preparador de oposiciones virtual altamente cualificado. Tu tarea es crear un conjunto de flashcards (tarjetas de estudio) basadas en el contenido proporcionado. Estas flashcards serán utilizadas por un estudiante para repasar conceptos clave.\n\nCONTEXTO DEL TEMARIO (Información base para tus flashcards):\n{documentos}\n\nPETICIÓN DEL USUARIO:\nGenera {cantidad} flashcards de alta calidad.\n{instrucciones}\n\nINSTRUCCIONES PARA CREAR FLASHCARDS:\n\n1. Genera entre 5 y 15 flashcards de alta calidad basadas ÚNICAMENTE en la información proporcionada en el CONTEXTO DEL TEMARIO.\n2. Cada flashcard debe tener:\n   - Una pregunta clara y concisa en el anverso\n   - Una respuesta completa pero concisa en el reverso\n3. Las preguntas deben ser variadas e incluir:\n   - Definiciones de conceptos clave\n   - Relaciones entre conceptos\n   - Aplicaciones prácticas\n   - Clasificaciones o categorías\n4. Las respuestas deben:\n   - Ser precisas y basadas estrictamente en el contenido del CONTEXTO\n   - Incluir la información esencial sin ser excesivamente largas\n   - Estar redactadas de forma clara y didáctica\n5. NO inventes información que no esté en el CONTEXTO.\n6. Responde SIEMPRE en español.\n\nFORMATO DE RESPUESTA:\nDebes proporcionar tu respuesta en formato JSON, con un array de objetos donde cada objeto representa una flashcard con las propiedades \"pregunta\" y \"respuesta\". Ejemplo:\n\n[\n  {\n    \"pregunta\": \"¿Qué es X concepto?\",\n    \"respuesta\": \"X concepto es...\"\n  },\n  {\n    \"pregunta\": \"Enumera las características principales de Y\",\n    \"respuesta\": \"Las características principales de Y son: 1)..., 2)..., 3)...\"\n  }\n]\n\nIMPORTANTE: Tu respuesta debe contener ÚNICAMENTE el array JSON, sin texto adicional antes o después. No incluyas marcadores de código ni la palabra json antes del array.\n`;\n/**\n * Prompt para la generación de mapas mentales\n *\n * Variables disponibles:\n * - {documentos}: Contenido de los documentos seleccionados\n * - {instrucciones}: Instrucciones adicionales (opcional)\n */ const PROMPT_MAPAS_MENTALES = `\nEres \"Mentor Opositor AI\", un preparador de oposiciones virtual altamente cualificado. Tu tarea es crear un mapa mental basado en el contenido proporcionado. Este mapa mental será utilizado por un estudiante para visualizar la estructura y las relaciones entre los conceptos.\n\nCONTEXTO DEL TEMARIO (Información base para tu mapa mental):\n{documentos}\n\nPETICIÓN DEL USUARIO (Tema principal y estructura deseada del mapa mental):\nGenera un mapa mental sobre el tema proporcionado.\n{instrucciones}\n\nINSTRUCCIONES EXTREMADAMENTE DETALLADAS PARA EL CÓDIGO D3.JS:\n\n**A. ESTRUCTURA DEL ARCHIVO Y CONFIGURACIÓN BÁSICA:**\n1.  **HTML Completo:** Genera un solo archivo \\`<!DOCTYPE html>...</html>\\`.\n2.  **CSS Integrado:** Todo el CSS debe estar dentro de etiquetas \\`<style>\\` en el \\`<head>\\`.\n3.  **JavaScript Integrado:** Todo el JavaScript debe estar dentro de una etiqueta \\`<script>\\` antes de cerrar \\`</body>\\`.\n4.  **D3.js CDN:** Carga D3.js v7 (o la más reciente v7.x) desde su CDN oficial: \\`https://d3js.org/d3.v7.min.js\\`.\n5.  **SVG y Body:**\n    *   \\`body { margin: 0; overflow: hidden; font-family: sans-serif; background-color: #f0f2f5; }\\`.\n    *   El \\`<svg>\\` debe ocupar toda la ventana: \\`width: 100vw; height: 100vh;\\`.\n    *   Añade un grupo principal \\`<g class=\"main-group\">\\` dentro del SVG para aplicar transformaciones de zoom/pan.\n    *   **NUEVO:** Define una duración para las transiciones: \\`const duration = 750;\\`.\n\n**B. ESTRUCTURA DE DATOS PARA D3.JS:**\n1.  **Jerarquía JSON:** Extrae los conceptos del CONTEXTO y organízalos en una estructura jerárquica JSON.\n2.  **Propiedades del Nodo de Datos:** Cada objeto en tu estructura de datos DEBE tener:\n    *   \\`name\\`: (string) El texto a mostrar en el nodo.\n    *   \\`id\\`: (string) Un identificador ÚNICO y ESTABLE para este nodo (e.g., \"concepto-raiz\", \"hijo1-concepto-raiz\").\n    *   \\`children\\`: (array, opcional) Un array de objetos nodo hijos.\n    *   **NUEVO:** \\`_children\\`: (array, opcional, inicialmente null o undefined) Se usará para guardar los hijos cuando un nodo esté colapsado.\n3.  **Jerarquía D3:** Usa \\`let root = d3.hierarchy(datosJSON);\\`.\n4.  **NUEVO: Colapsar Nodos Inicialmente (Opcional, pero bueno para el rendimiento si el árbol es grande):**\n    *   Define una función \\`collapse(node)\\` que mueve \\`node.children\\` a \\`node._children\\` para todos los descendientes excepto el nodo raíz y sus hijos directos.\n    *   Llama a \\`root.descendants().forEach(collapse);\\` después de crear la jerarquía y antes del primer renderizado, si quieres que el árbol empiece parcialmente colapsado. O puedes dejarlo todo expandido y que el usuario colapse.\n    *   **Alternativa más simple para inicio:** Colapsa todos los nodos a partir de cierta profundidad (ej. profundidad > 1).\n      \\`root.each(d => { if (d.depth > 1) { if (d.children) { d._children = d.children; d.children = null; } } });\\`\n\n**C. LAYOUT DEL ÁRBOL (D3.JS TREE):**\n1.  **Tipo de Layout:** Usa \\`d3.tree()\\`.\n2.  **Espaciado de Nodos (\\`nodeSize\\`):**\n    *   \\`const nodeVerticalSeparation = 80;\\`.\n    *   \\`const nodeHorizontalSeparation = 250;\\`.\n    *   \\`const treeLayout = d3.tree().nodeSize([nodeVerticalSeparation, nodeHorizontalSeparation]);\\`.\n3.  **Posición Inicial:** Guarda la posición inicial de la raíz con validación:\n    \\`const viewportHeight = window.innerHeight || 600;\n     const viewportWidth = window.innerWidth || 800;\n     root.x0 = isNaN(viewportHeight / 2) ? 300 : viewportHeight / 2;\n     root.y0 = 0;\\` (Ajusta y0 si la raíz no empieza en el borde).\n\n// ******** INICIO FUNCIÓN TEXT WRAPPING ********\nfunction wrapText(textElement, width, lineHeight) {\n    let totalComputedHeight = 0;\n    textElement.each(function() { \n        const textNode = d3.select(this);\n        const words = textNode.text().split(/\\s+/).reverse();\n        let word;\n        let line = []; // Declared 'line' here\n        textNode.text(null); \n\n        let tspan = textNode.append(\"tspan\")\n            .attr(\"x\", 0) \n            .attr(\"dy\", lineHeight + \"px\"); \n        let numLines = 1;\n\n        while (word = words.pop()) {\n            line.push(word);\n            tspan.text(line.join(\" \"));\n            if (tspan.node().getComputedTextLength() > width && line.length > 1) {\n                line.pop();\n                tspan.text(line.join(\" \"));\n                line = [word]; // Reset line for the new tspan\n                tspan = textNode.append(\"tspan\")\n                    .attr(\"x\", 0)\n                    .attr(\"dy\", lineHeight + \"px\") \n                    .text(word);\n                numLines++;\n            }\n        }\n        totalComputedHeight = numLines * lineHeight;\n    });\n    return totalComputedHeight; \n}\n// ******** FIN FUNCIÓN TEXT WRAPPING ********\n\n**D. FUNCIÓN \\`update(sourceNode)\\` (VITAL PARA INTERACTIVIDAD):**\n   Esta función será la responsable de renderizar/actualizar el árbol cada vez que se expanda/colapse un nodo.\n   \\`sourceNode\\` es el nodo que fue clickeado.\n\n1.  **Calcular Nuevo Layout:**\n    *   \\`const treeData = treeLayout(root);\\`.\n    *   \\`const nodes = treeData.descendants();\\`.\n    *   \\`const links = treeData.links();\\`.\n    *   **Orientación (Ajustar Coordenadas):** Asegúrate de que después del layout, los nodos se posicionen horizontalmente. \\`nodes.forEach(d => { d.y = d.depth * nodeHorizontalSeparation; });\\` (Si \\`nodeSize\\` no lo hace directamente, o si quieres controlar la separación de niveles manualmente).\n    *   **VALIDACIÓN CRÍTICA:** Asegúrate de que todas las coordenadas sean números válidos:\n        \\`nodes.forEach(d => {\n          d.x = isNaN(d.x) ? 0 : d.x;\n          d.y = isNaN(d.y) ? d.depth * nodeHorizontalSeparation : d.y;\n          d.x0 = d.x0 || d.x;\n          d.y0 = d.y0 || d.y;\n        });\\`\n\n2.  **NODOS:**\n    *   Selección: \\`const node = g.selectAll(\"g.node\").data(nodes, d => d.data.id);\\`.\n    *   **Nodos Entrantes (\\`nodeEnter\\`):**\n        *   Crea el grupo principal del nodo:\n            \\`const nodeEnter = node.enter().append(\"g\")\n                .attr(\"class\", d => \"node depth-\" + d.depth) // Añade clase de profundidad\n                .attr(\"transform\", d => \\`translate(\\${sourceNode.y0 || 0},\\${sourceNode.x0 || 0})\\`) // Posición inicial validada\n                .on(\"click\", handleClick);\\`\n        *   **Cálculo de Dimensiones, Text Wrapping y Creación de Elementos Internos (Rect y Text):**\n            \\`nodeEnter.each(function(d) {\n                const nodeGroup = d3.select(this);\n                const horizontalPadding = 12;\n                const verticalPadding = 8;\n                const maxNodeTextWidth = 150;\n                const lineHeight = 12; // Asumiendo font-size 12px, so 1.2em = 12px\n\n                d.rectWidth = maxNodeTextWidth + 2 * horizontalPadding;\n\n                // Añade el rectángulo primero (visualmente detrás del texto)\n                const rectElement = nodeGroup.append(\"rect\")\n                    .attr(\"rx\", \"3\")\n                    .attr(\"ry\", \"3\")\n                    .style(\"stroke-width\", \"1px\")\n                    .style(\"fill\", () => d._children ? \"#aec7e8\" : \"#fff\") // Usa función para acceder a 'd'\n                    .style(\"stroke\", \"#777\");\n\n                // Añade el elemento de texto\n                const textElement = nodeGroup.append(\"text\")\n                    .attr(\"text-anchor\", \"middle\")\n                    .style(\"font-size\", \"10px\") \n                    .style(\"fill\", \"#333\")\n                    .text(d.data.name); // Nombre del nodo\n\n                // Aplica text wrapping y obtén la altura calculada del texto\n                const computedTextHeight = wrapText(textElement, maxNodeTextWidth, lineHeight);\n                \n                // Calcula la altura final del rectángulo\n                d.rectHeight = Math.max(computedTextHeight + 2 * verticalPadding, 30); // Altura mínima de 30px\n\n                // Ajusta la posición Y del elemento de texto para centrarlo verticalmente\n                // El primer tspan dentro de wrapText tendrá un dy de 'lineHeight'\n                // por lo que el 'y' del textElement debe ser la parte superior del área de texto.\n                const textBlockYOffset = -d.rectHeight / 2 + verticalPadding;\n                textElement.attr(\"y\", textBlockYOffset);\n\n                // Ahora establece las dimensiones y posición del rectángulo\n                rectElement\n                    .attr(\"width\", d.rectWidth)\n                    .attr(\"height\", d.rectHeight)\n                    .attr(\"x\", -d.rectWidth / 2)\n                    .attr(\"y\", -d.rectHeight / 2);\n            });\\`\n    *   **Nodos Actualizados (\\`nodeUpdate\\`):**\n        *   **VALIDACIÓN DE COORDENADAS:** \\`const validX = isNaN(d.x) ? 0 : d.x; const validY = isNaN(d.y) ? 0 : d.y;\\`\n        *   Transición a la nueva posición: \\`node.merge(nodeEnter).transition().duration(duration).attr(\"transform\", d => \\`translate(\\${isNaN(d.y) ? 0 : d.y},\\${isNaN(d.x) ? 0 : d.x})\\`);\\`.\n        *   Actualiza el color del rectángulo si cambia el estado colapsable: \\`node.merge(nodeEnter).select(\"rect\").style(\"fill\", d => d._children ? \"#aec7e8\" : \"#fff\");\\`.\n    *   **Nodos Salientes (\\`nodeExit\\`):**\n        *   **VALIDACIÓN DE POSICIÓN FINAL:** \\`const finalX = isNaN(sourceNode.x) ? 0 : sourceNode.x; const finalY = isNaN(sourceNode.y) ? 0 : sourceNode.y;\\`\n        *   Transición a la posición del nodo padre: \\`nodeExit.transition().duration(duration).attr(\"transform\", \\`translate(\\${finalY},\\${finalX})\\`).remove();\\`.\n        *   Reduce la opacidad del rectángulo y texto a 0.\n\n3.  **ENLACES:**\n    *   Selección: \\`const link = g.selectAll(\"path.link\").data(links, d => d.target.data.id);\\`.\n    *   **Enlaces Entrantes (\\`linkEnter\\`):**\n        *   Añade \\`<path class=\"link\">\\`.\n        *   **VALIDACIÓN DE POSICIÓN INICIAL:**\n            \\`const sourceInitialX = isNaN(sourceNode.x0) ? 0 : sourceNode.x0;\n             const sourceInitialY = isNaN(sourceNode.y0) ? 0 : sourceNode.y0;\n             const sourceInitialWidth = isNaN(sourceNode.rectWidth) ? 20 : (sourceNode.rectWidth || 20);\\`\n        *   Posición inicial desde el padre: \\`linkEnter.insert(\"path\", \"g\").attr(\"class\", \"link\").attr(\"d\", d => { const o = {x: sourceInitialX, y: sourceInitialY, rectWidth: sourceInitialWidth }; return diagonal({source: o, target: o}); }).style(\"fill\", \"none\").style(\"stroke\", \"#ccc\").style(\"stroke-width\", \"1.5px\");\\`\n    *   **Enlaces Actualizados (\\`linkUpdate\\`):**\n        *   Transición a la nueva posición: \\`link.merge(linkEnter).transition().duration(duration).attr(\"d\", diagonal);\\`.\n    *   **Enlaces Salientes (\\`linkExit\\`):**\n        *   **VALIDACIÓN DE POSICIÓN FINAL:**\n            \\`const sourceFinalX = isNaN(sourceNode.x) ? 0 : sourceNode.x;\n             const sourceFinalY = isNaN(sourceNode.y) ? 0 : sourceNode.y;\n             const sourceFinalWidth = isNaN(sourceNode.rectWidth) ? 20 : (sourceNode.rectWidth || 20);\\`\n        *   Transición a la posición del padre y remove: \\`linkExit.transition().duration(duration).attr(\"d\", d => { const o = {x: sourceFinalX, y: sourceFinalY, rectWidth: sourceFinalWidth }; return diagonal({source: o, target: o}); }).remove();\\`.\n\n4.  **Guardar Posiciones Antiguas:**\n    *   Al final de \\`update\\`: \\`nodes.forEach(d => { d.x0 = d.x; d.y0 = d.y; });\\`.\n\n**E. FUNCIÓN \\`diagonal(linkObject)\\` (PARA DIBUJAR ENLACES A BORDES DE RECTÁNGULOS):**\n   Debe generar un path string para el atributo \\`d\\` del path.\n   \\`\\`\\`javascript\n   function diagonal({ source, target }) {\n     // source y target son nodos con propiedades x, y, rectWidth\n     // VALIDACIÓN CRÍTICA: Asegurar que todos los valores sean números válidos\n     const sourceX = isNaN(source.x) ? 0 : source.x;\n     const sourceY = isNaN(source.y) ? 0 : source.y;\n     const targetX = isNaN(target.x) ? 0 : target.x;\n     const targetY = isNaN(target.y) ? 0 : target.y;\n     const sourceWidth = isNaN(source.rectWidth) ? 20 : (source.rectWidth || 20);\n     const targetWidth = isNaN(target.rectWidth) ? 20 : (target.rectWidth || 20);\n\n     const sx = sourceY + sourceWidth / 2;\n     const sy = sourceX;\n     const tx = targetY - targetWidth / 2;\n     const ty = targetX;\n\n     // Validar que los puntos calculados sean números válidos\n     const validSx = isNaN(sx) ? 0 : sx;\n     const validSy = isNaN(sy) ? 0 : sy;\n     const validTx = isNaN(tx) ? 0 : tx;\n     const validTy = isNaN(ty) ? 0 : ty;\n\n     // Path curvado simple\n     return \\`M \\${validSx} \\${validSy}\n             C \\${(validSx + validTx) / 2} \\${validSy},\n               \\${(validSx + validTx) / 2} \\${validTy},\n               \\${validTx} \\${validTy}\\`;\n   }\n   \\`\\`\\`\n\n**F. FUNCIÓN \\`handleClick(event, d)\\` (MANEJADOR DE CLIC EN NODO):**\n   \\`\\`\\`javascript\n   function handleClick(event, d) {\n     if (d.children) { // Si está expandido, colapsar\n       d._children = d.children;\n       d.children = null;\n     } else if (d._children) { // Si está colapsado y tiene hijos ocultos, expandir\n       d.children = d._children;\n       d._children = null;\n     }\n     // Si es un nodo hoja (sin d.children ni d._children), no hacer nada o una acción específica.\n     // Para este caso, solo expandir/colapsar.\n     update(d); // Llama a update con el nodo clickeado como 'sourceNode'\n   }\n   \\`\\`\\`\n\n**G. VISUALIZACIÓN INICIAL Y ZOOM/PAN:**\n1.  Llama a \\`update(root);\\` para el primer renderizado.\n2.  **Cálculo de Extensiones y Escala Inicial (Adaptar del prompt anterior):**\n    *   NECESITAS calcular las dimensiones del árbol DESPUÉS de que el layout inicial (\\`update(root)\\`) haya asignado \\`rectWidth\\` y \\`rectHeight\\` a los nodos visibles.\n    *   Obtén minX, maxX, minYActual, maxYActual de los nodos en root.descendants() que no estén colapsados (o de todos para un cálculo más simple que puede ser ajustado por el zoom).\n    *   Considera el \\`rectWidth/2\\` y \\`rectHeight/2\\` para los bordes.\n3.  **Traslación y Escala:**\n    *   Calcula \\`initialScale\\`, \\`initialTranslateX\\`, \\`initialTranslateY\\` como en el prompt anterior, pero usando el \\`<g class=\"main-group\">\\` para el zoom.\n    *   \\`const zoom = d3.zoom().scaleExtent([0.1, 3]).on(\"zoom\", (event) => mainGroup.attr(\"transform\", event.transform));\\`\n    *   \\`svg.call(zoom);\\`.\n    *   \\`svg.call(zoom.transform, d3.zoomIdentity.translate(initialTranslateX, initialTranslateY).scale(initialScale));\\`.\n\n**H. MANEJO DE REDIMENSIONAMIENTO DE VENTANA (Como en el prompt anterior):**\n    *   Reajusta el SVG y recalcula la transformación de zoom/pan para centrar.\n\n**I. ESTILO CSS:**\n   \\`\\`\\`css\n   .node text { font: 10px sans-serif; pointer-events: none; }\n   .link { fill: none; stroke: #ccc; stroke-width: 1.5px; }\n   .node rect { cursor: pointer; }\n   .node rect:hover { stroke-opacity: 1; stroke-width: 2px; }\n   /* Colores por profundidad (opcional) */\n   .node.depth-0 rect { fill: #d1e5f0; stroke: #67a9cf; }\n   .node.depth-1 rect { fill: #fddbc7; stroke: #ef8a62; }\n   .node.depth-2 rect { fill: #e0f3f8; stroke: #92c5de; }\n   .node.depth-3 rect { fill: #f7f7f7; stroke: #bababa; }\n   \\`\\`\\`\n   Asegúrate de añadir la clase de profundidad al grupo del nodo:\n   \\`nodeEnter.attr(\"class\", d => \"node depth-\" + d.depth)\\`\n\n**J. REVISIÓN FINAL ANTES DE GENERAR (PARA LA IA):**\n*   ¿Se usa una función \\`update(sourceNode)\\` para manejar todas las actualizaciones del DOM? SÍ.\n*   ¿La función \\`handleClick\\` alterna entre \\`d.children\\` y \\`d._children\\` y luego llama a \\`update(d)\\`? SÍ.\n*   ¿Los nodos y enlaces entrantes aparecen desde la posición del padre (\\`sourceNode\\`)? SÍ.\n*   ¿Los nodos y enlaces salientes se mueven hacia la posición del padre antes de eliminarse? SÍ.\n*   ¿Se usan transiciones D3 con una \\`duration\\` constante? SÍ.\n*   ¿Se almacenan y usan \\`x0\\`, \\`y0\\` para las posiciones iniciales/finales de las transiciones? SÍ.\n*   ¿La función \\`diagonal\\` calcula correctamente los puntos de inicio/fin en los bordes de los rectángulos? SÍ.\n*   ¿El cálculo dinámico de \\`rectWidth\\` y \\`rectHeight\\` se realiza para cada nodo al entrar? SÍ.\n\n**RESTRICCIONES IMPORTANTES:**\n-   Tu respuesta DEBE SER ÚNICAMENTE el código HTML completo. Sin explicaciones, comentarios introductorios o finales fuera del código.\n-   Sigue las instrucciones de D3.js al pie de la letra, especialmente el patrón Enter-Update-Exit dentro de la función \\`update\\`.\n-   **CRÍTICO:** SIEMPRE valida que las coordenadas y dimensiones sean números válidos usando \\`isNaN()\\` antes de usarlas en transformaciones SVG. Esto evita errores como \\`translate(NaN,NaN)\\` o \\`scale(NaN)\\`.\n-   **CRÍTICO:** Usa valores por defecto seguros (como 0, 20, 300) cuando los cálculos resulten en NaN o undefined.\n\n`;\n/**\n * Prompt para la generación de tests\n *\n * Variables disponibles:\n * - {documentos}: Contenido de los documentos seleccionados\n * - {cantidad}: Número de preguntas a generar\n * - {instrucciones}: Instrucciones adicionales (opcional)\n */ const PROMPT_TESTS = `\nEres \"Mentor Opositor AI\", un preparador de oposiciones virtual altamente cualificado. Tu tarea es crear un conjunto de preguntas de test de opción múltiple (4 opciones, 1 correcta) basadas en el contenido proporcionado. Estas preguntas serán utilizadas por un estudiante para evaluar su comprensión del temario.\n\nCONTEXTO DEL TEMARIO (Información base para tus preguntas):\n{documentos}\n\nPETICIÓN DEL USUARIO:\nGenera {cantidad} preguntas de test de alta calidad.\nInstrucciones específicas del usuario: {instrucciones}\n\nINSTRUCCIONES PARA CREAR PREGUNTAS DE TEST:\n\n1.  Genera EXACTAMENTE la {cantidad}, que solicite el usuario, de preguntas de test de alta calidad.\n2.  BASA TODAS las preguntas y opciones de respuesta ESTRICTA y ÚNICAMENTE en la información proporcionada en el \"CONTEXTO DEL TEMARIO\".\n3.  ENFOCA cada pregunta según las \"Instrucciones específicas del usuario\" ({instrucciones}). Si las instrucciones piden centrarse en \"artículos, sus números y su contenido\", entonces CADA pregunta debe tratar directamente sobre:\n    a)  El número de un artículo específico y lo que establece.\n    b)  El contenido principal de un artículo específico, preguntando a qué artículo pertenece o detalles clave.\n    c)  La relación entre un concepto y el artículo que lo regula.\n    EVITA preguntas generales sobre historia, contexto de aprobación de leyes, o interpretaciones amplias a menos que las \"Instrucciones específicas del usuario\" ({instrucciones}) lo indiquen explícitamente.\n4.  Cada objeto de pregunta en el array JSON resultante debe tener las siguientes propiedades DIRECTAS:\n    -   \"pregunta\": (string) El texto de la pregunta.\n    -   \"opcion_a\": (string) El texto para la opción A.\n    -   \"opcion_b\": (string) El texto para la opción B.\n    -   \"opcion_c\": (string) El texto para la opción C.\n    -   \"opcion_d\": (string) El texto para la opción D.\n    -   \"respuesta_correcta\": (string) Debe ser 'a', 'b', 'c', o 'd', indicando cuál de las opciones es la correcta.\n    NO anides las opciones (opcion_a, opcion_b, etc.) dentro de otro objeto llamado \"opciones\". Deben ser propiedades directas del objeto de la pregunta.\n5.  Las preguntas deben ser claras, concisas y evaluar la comprensión de conceptos clave, detalles importantes, relaciones, etc., SIEMPRE dentro del enfoque solicitado en {instrucciones}.\n6.  Las opciones de respuesta deben ser plausibles y estar basadas en el contexto, pero solo una debe ser inequívocamente correcta según el temario proporcionado y el enfoque de la pregunta.\n7.  NO inventes información que no esté en el CONTEXTO.\n8.  Responde SIEMPRE en español.\n\nFORMATO DE RESPUESTA:\nDebes proporcionar tu respuesta en formato JSON, con un array de objetos donde cada objeto representa una pregunta con las propiedades directas \"pregunta\", \"opcion_a\", \"opcion_b\", \"opcion_c\", \"opcion_d\" y \"respuesta_correcta\". Ejemplo:\n\n[\n  {\n    \"pregunta\": \"¿Qué establece el Artículo X de la Ley Y sobre Z?\",\n    \"opcion_a\": \"Opción A relacionada con el artículo X\",\n    \"opcion_b\": \"Opción B relacionada con el artículo X (correcta)\",\n    \"opcion_c\": \"Opción C relacionada con el artículo X\",\n    \"opcion_d\": \"Opción D relacionada con el artículo X\",\n    \"respuesta_correcta\": \"b\"\n  },\n  {\n    \"pregunta\": \"El concepto de [concepto clave] se regula principalmente en el artículo:\",\n    \"opcion_a\": \"Artículo A\",\n    \"opcion_b\": \"Artículo B\",\n    \"opcion_c\": \"Artículo C (correcta)\",\n    \"opcion_d\": \"Artículo D\",\n    \"respuesta_correcta\": \"c\"\n  }\n]\n\nIMPORTANTE: Tu respuesta debe contener ÚNICAMENTE el array JSON, sin texto adicional antes o después. No incluyas marcadores de código ni la palabra json antes del array.\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/config/prompts.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini.ts":
/*!***************************!*\
  !*** ./src/lib/gemini.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   genAI: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.genAI),\n/* harmony export */   generarFlashcards: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.generarFlashcards),\n/* harmony export */   generarMapaMental: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.generarMapaMental),\n/* harmony export */   generarTest: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.generarTest),\n/* harmony export */   model: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.model),\n/* harmony export */   obtenerRespuestaIA: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.obtenerRespuestaIA),\n/* harmony export */   prepararDocumentos: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos),\n/* harmony export */   truncarContenido: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.truncarContenido)\n/* harmony export */ });\n/* harmony import */ var _gemini_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./gemini/index */ \"(rsc)/./src/lib/gemini/index.ts\");\n// Este archivo es un punto de entrada para mantener la compatibilidad con el código existente\n// Redirige todas las exportaciones a la nueva estructura modular\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2dlbWluaS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBQSw4RkFBOEY7QUFDOUYsaUVBQWlFO0FBRWxDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcc3JjXFxsaWJcXGdlbWluaS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFc3RlIGFyY2hpdm8gZXMgdW4gcHVudG8gZGUgZW50cmFkYSBwYXJhIG1hbnRlbmVyIGxhIGNvbXBhdGliaWxpZGFkIGNvbiBlbCBjw7NkaWdvIGV4aXN0ZW50ZVxuLy8gUmVkaXJpZ2UgdG9kYXMgbGFzIGV4cG9ydGFjaW9uZXMgYSBsYSBudWV2YSBlc3RydWN0dXJhIG1vZHVsYXJcblxuZXhwb3J0ICogZnJvbSBcIi4vZ2VtaW5pL2luZGV4XCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/flashcardGenerator.ts":
/*!**********************************************!*\
  !*** ./src/lib/gemini/flashcardGenerator.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generarFlashcards: () => (/* binding */ generarFlashcards)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _config_prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../config/prompts */ \"(rsc)/./src/config/prompts.ts\");\n\n\n/**\n * Genera flashcards a partir de los documentos\n */ async function generarFlashcards(documentos, cantidad = 10, instrucciones) {\n    try {\n        // Preparar el contenido de los documentos\n        const contenidoDocumentos = (0,_geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos)(documentos);\n        if (!contenidoDocumentos) {\n            throw new Error(\"No se han proporcionado documentos para generar flashcards.\");\n        }\n        // Construir el prompt para la IA usando el prompt personalizado\n        // Reemplazar las variables en el prompt\n        let prompt = _config_prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPT_FLASHCARDS.replace('{documentos}', contenidoDocumentos).replace('{cantidad}', cantidad.toString());\n        // Añadir instrucciones adicionales si se proporcionan\n        if (instrucciones) {\n            prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\\n- ${instrucciones}`);\n        } else {\n            prompt = prompt.replace('{instrucciones}', '');\n        }\n        // Generar las flashcards\n        const result = await _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model.generateContent(prompt);\n        const response = result.response.text();\n        // Extraer el JSON de la respuesta\n        const jsonMatch = response.match(/\\[\\s*\\{[\\s\\S]*\\}\\s*\\]/);\n        if (!jsonMatch) {\n            throw new Error(\"No se pudo extraer el formato JSON de la respuesta.\");\n        }\n        const flashcardsJson = jsonMatch[0];\n        const flashcards = JSON.parse(flashcardsJson);\n        // Validar el formato\n        if (!Array.isArray(flashcards) || flashcards.length === 0) {\n            throw new Error(\"El formato de las flashcards generadas no es válido.\");\n        }\n        return flashcards;\n    } catch (error) {\n        console.error('Error al generar flashcards:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/flashcardGenerator.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/geminiClient.ts":
/*!****************************************!*\
  !*** ./src/lib/gemini/geminiClient.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   genAI: () => (/* binding */ genAI),\n/* harmony export */   model: () => (/* binding */ model),\n/* harmony export */   prepararDocumentos: () => (/* binding */ prepararDocumentos),\n/* harmony export */   truncarContenido: () => (/* binding */ truncarContenido)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n// Configuración de la API de Gemini\nconst API_KEY = process.env.GEMINI_API_KEY || '';\nconst MODEL_NAME = 'gemini-2.0-flash-thinking-exp-01-21';\n// Verificar que la API key esté configurada\nif (!API_KEY) {\n    console.error('GEMINI_API_KEY no está configurada en las variables de entorno');\n}\n// Inicializar el cliente de Gemini\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(API_KEY);\nconst model = genAI.getGenerativeModel({\n    model: MODEL_NAME,\n    safetySettings: [\n        {\n            category: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmCategory.HARM_CATEGORY_HARASSMENT,\n            threshold: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE\n        },\n        {\n            category: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmCategory.HARM_CATEGORY_HATE_SPEECH,\n            threshold: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE\n        },\n        {\n            category: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,\n            threshold: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE\n        },\n        {\n            category: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,\n            threshold: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE\n        }\n    ]\n});\n// Ya no usamos instrucciones base globales, cada servicio usa su propio prompt personalizado\n/**\n * Trunca el contenido de un documento si es demasiado largo\n */ function truncarContenido(contenido, maxLength = 25000) {\n    // Verificar que el contenido sea una cadena válida\n    if (contenido === undefined || contenido === null) {\n        console.warn('Se intentó truncar un contenido undefined o null');\n        return '';\n    }\n    // Asegurarse de que el contenido sea una cadena\n    const contenidoStr = String(contenido);\n    if (contenidoStr.length <= maxLength) {\n        return contenidoStr;\n    }\n    return contenidoStr.substring(0, maxLength) + `\\n\\n[CONTENIDO TRUNCADO: El documento original es más largo. Esta es una versión reducida para procesamiento.]`;\n}\n// --- Nueva lógica de Chunking ---\nconst CHUNK_SIZE = 5000; // Caracteres\nconst CHUNK_OVERLAP = 200; // Caracteres\nconst MAX_TOTAL_CONTEXT_LENGTH = 50000; // Caracteres\nfunction createTextChunks(documentTitle, content) {\n    if (!content) {\n        return [];\n    }\n    const contentStr = String(content);\n    const chunks = [];\n    let chunkIndex = 0;\n    let currentIndex = 0;\n    while(currentIndex < contentStr.length){\n        const endIndex = Math.min(currentIndex + CHUNK_SIZE, contentStr.length);\n        const text = contentStr.substring(currentIndex, endIndex);\n        chunks.push({\n            originalDocumentTitle: documentTitle,\n            chunkIndex: chunkIndex + 1,\n            text\n        });\n        chunkIndex++;\n        if (endIndex === contentStr.length) {\n            break;\n        }\n        currentIndex += CHUNK_SIZE - CHUNK_OVERLAP;\n        // Asegurar que no haya un bucle infinito si CHUNK_OVERLAP >= CHUNK_SIZE\n        if (currentIndex >= endIndex && endIndex < contentStr.length) {\n            currentIndex = endIndex; // Avanzar al menos hasta el final del chunk actual\n        }\n    }\n    return chunks;\n}\n/**\n * Prepara los documentos para enviarlos al modelo, dividiéndolos en chunks.\n */ function prepararDocumentos(documentos) {\n    if (!documentos || !Array.isArray(documentos) || documentos.length === 0) {\n        console.warn('No se proporcionaron documentos válidos para prepararDocumentos');\n        return '';\n    }\n    try {\n        const allChunks = [];\n        for (const doc of documentos){\n            if (!doc || typeof doc !== 'object' || !doc.titulo || doc.contenido === undefined || doc.contenido === null) {\n                console.warn('Documento inválido, sin título o contenido en prepararDocumentos:', doc);\n                continue;\n            }\n            const fullOriginalTitle = `${doc.categoria ? `[${doc.categoria}] ` : ''}${doc.numero_tema ? `Tema ${doc.numero_tema}: ` : ''}${doc.titulo}`;\n            const documentChunks = createTextChunks(fullOriginalTitle.trim(), doc.contenido);\n            allChunks.push(...documentChunks);\n        }\n        if (allChunks.length === 0) {\n            console.warn('No se generaron chunks a partir de los documentos proporcionados.');\n            return '';\n        }\n        let fullContext = allChunks.map((chunk)=>{\n            return `\n=== INICIO CHUNK: ${chunk.originalDocumentTitle} - Parte ${chunk.chunkIndex} ===\n${chunk.text}\n=== FIN CHUNK: ${chunk.originalDocumentTitle} - Parte ${chunk.chunkIndex} ===\n`;\n        }).join('\\n\\n');\n        if (fullContext.length > MAX_TOTAL_CONTEXT_LENGTH) {\n            console.warn(`El contexto combinado (${fullContext.length} caracteres) excede el máximo de ${MAX_TOTAL_CONTEXT_LENGTH}. Se truncará.`);\n            fullContext = fullContext.substring(0, MAX_TOTAL_CONTEXT_LENGTH) + `\\n\\n[CONTEXTO GENERAL TRUNCADO: El contenido combinado de todos los chunks excedió el límite máximo (${MAX_TOTAL_CONTEXT_LENGTH} caracteres).]`;\n        }\n        return fullContext;\n    } catch (error) {\n        console.error('Error al preparar documentos con chunks:', error);\n        return '';\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2dlbWluaS9nZW1pbmlDbGllbnQudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNkY7QUFFN0Ysb0NBQW9DO0FBQ3BDLE1BQU1HLFVBQVVDLFFBQVFDLEdBQUcsQ0FBQ0MsY0FBYyxJQUFJO0FBQzlDLE1BQU1DLGFBQWE7QUFFbkIsNENBQTRDO0FBQzVDLElBQUksQ0FBQ0osU0FBUztJQUNaSyxRQUFRQyxLQUFLLENBQUM7QUFDaEI7QUFFQSxtQ0FBbUM7QUFDNUIsTUFBTUMsUUFBUSxJQUFJVixxRUFBa0JBLENBQUNHLFNBQVM7QUFDOUMsTUFBTVEsUUFBUUQsTUFBTUUsa0JBQWtCLENBQUM7SUFDNUNELE9BQU9KO0lBQ1BNLGdCQUFnQjtRQUNkO1lBQ0VDLFVBQVViLCtEQUFZQSxDQUFDYyx3QkFBd0I7WUFDL0NDLFdBQVdkLHFFQUFrQkEsQ0FBQ2Usc0JBQXNCO1FBQ3REO1FBQ0E7WUFDRUgsVUFBVWIsK0RBQVlBLENBQUNpQix5QkFBeUI7WUFDaERGLFdBQVdkLHFFQUFrQkEsQ0FBQ2Usc0JBQXNCO1FBQ3REO1FBQ0E7WUFDRUgsVUFBVWIsK0RBQVlBLENBQUNrQiwrQkFBK0I7WUFDdERILFdBQVdkLHFFQUFrQkEsQ0FBQ2Usc0JBQXNCO1FBQ3REO1FBQ0E7WUFDRUgsVUFBVWIsK0RBQVlBLENBQUNtQiwrQkFBK0I7WUFDdERKLFdBQVdkLHFFQUFrQkEsQ0FBQ2Usc0JBQXNCO1FBQ3REO0tBQ0Q7QUFDSCxHQUFHO0FBRUgsNkZBQTZGO0FBRTdGOztDQUVDLEdBQ00sU0FBU0ksaUJBQWlCQyxTQUFvQyxFQUFFQyxZQUFvQixLQUFLO0lBQzlGLG1EQUFtRDtJQUNuRCxJQUFJRCxjQUFjRSxhQUFhRixjQUFjLE1BQU07UUFDakRkLFFBQVFpQixJQUFJLENBQUM7UUFDYixPQUFPO0lBQ1Q7SUFFQSxnREFBZ0Q7SUFDaEQsTUFBTUMsZUFBZUMsT0FBT0w7SUFFNUIsSUFBSUksYUFBYUUsTUFBTSxJQUFJTCxXQUFXO1FBQ3BDLE9BQU9HO0lBQ1Q7SUFFQSxPQUFPQSxhQUFhRyxTQUFTLENBQUMsR0FBR04sYUFDL0IsQ0FBQyw4R0FBOEcsQ0FBQztBQUNwSDtBQUVBLG1DQUFtQztBQUVuQyxNQUFNTyxhQUFhLE1BQU0sYUFBYTtBQUN0QyxNQUFNQyxnQkFBZ0IsS0FBSyxhQUFhO0FBQ3hDLE1BQU1DLDJCQUEyQixPQUFPLGFBQWE7QUFRckQsU0FBU0MsaUJBQ1BDLGFBQXFCLEVBQ3JCQyxPQUFrQztJQUVsQyxJQUFJLENBQUNBLFNBQVM7UUFDWixPQUFPLEVBQUU7SUFDWDtJQUNBLE1BQU1DLGFBQWFULE9BQU9RO0lBQzFCLE1BQU1FLFNBQTBCLEVBQUU7SUFDbEMsSUFBSUMsYUFBYTtJQUNqQixJQUFJQyxlQUFlO0lBRW5CLE1BQU9BLGVBQWVILFdBQVdSLE1BQU0sQ0FBRTtRQUN2QyxNQUFNWSxXQUFXQyxLQUFLQyxHQUFHLENBQUNILGVBQWVULFlBQVlNLFdBQVdSLE1BQU07UUFDdEUsTUFBTWUsT0FBT1AsV0FBV1AsU0FBUyxDQUFDVSxjQUFjQztRQUNoREgsT0FBT08sSUFBSSxDQUFDO1lBQ1ZDLHVCQUF1Qlg7WUFDdkJJLFlBQVlBLGFBQWE7WUFDekJLO1FBQ0Y7UUFFQUw7UUFDQSxJQUFJRSxhQUFhSixXQUFXUixNQUFNLEVBQUU7WUFDbEM7UUFDRjtRQUNBVyxnQkFBZ0JULGFBQWFDO1FBQzdCLHdFQUF3RTtRQUN4RSxJQUFJUSxnQkFBZ0JDLFlBQVlBLFdBQVdKLFdBQVdSLE1BQU0sRUFBRTtZQUMxRFcsZUFBZUMsVUFBVSxtREFBbUQ7UUFDaEY7SUFDRjtJQUNBLE9BQU9IO0FBQ1Q7QUFFQTs7Q0FFQyxHQUNNLFNBQVNTLG1CQUFtQkMsVUFBNkY7SUFDOUgsSUFBSSxDQUFDQSxjQUFjLENBQUNDLE1BQU1DLE9BQU8sQ0FBQ0YsZUFBZUEsV0FBV25CLE1BQU0sS0FBSyxHQUFHO1FBQ3hFcEIsUUFBUWlCLElBQUksQ0FBQztRQUNiLE9BQU87SUFDVDtJQUVBLElBQUk7UUFDRixNQUFNeUIsWUFBNkIsRUFBRTtRQUVyQyxLQUFLLE1BQU1DLE9BQU9KLFdBQVk7WUFDNUIsSUFBSSxDQUFDSSxPQUFPLE9BQU9BLFFBQVEsWUFBWSxDQUFDQSxJQUFJQyxNQUFNLElBQUlELElBQUk3QixTQUFTLEtBQUtFLGFBQWEyQixJQUFJN0IsU0FBUyxLQUFLLE1BQU07Z0JBQzNHZCxRQUFRaUIsSUFBSSxDQUFDLHFFQUFxRTBCO2dCQUNsRjtZQUNGO1lBRUEsTUFBTUUsb0JBQW9CLEdBQUdGLElBQUlHLFNBQVMsR0FBRyxDQUFDLENBQUMsRUFBRUgsSUFBSUcsU0FBUyxDQUFDLEVBQUUsQ0FBQyxHQUFHLEtBQUtILElBQUlJLFdBQVcsR0FBRyxDQUFDLEtBQUssRUFBRUosSUFBSUksV0FBVyxDQUFDLEVBQUUsQ0FBQyxHQUFHLEtBQUtKLElBQUlDLE1BQU0sRUFBRTtZQUMzSSxNQUFNSSxpQkFBaUJ2QixpQkFBaUJvQixrQkFBa0JJLElBQUksSUFBSU4sSUFBSTdCLFNBQVM7WUFDL0U0QixVQUFVTixJQUFJLElBQUlZO1FBQ3BCO1FBRUEsSUFBSU4sVUFBVXRCLE1BQU0sS0FBSyxHQUFHO1lBQzFCcEIsUUFBUWlCLElBQUksQ0FBQztZQUNiLE9BQU87UUFDVDtRQUVBLElBQUlpQyxjQUFjUixVQUFVUyxHQUFHLENBQUNDLENBQUFBO1lBQzlCLE9BQU8sQ0FBQztrQkFDSSxFQUFFQSxNQUFNZixxQkFBcUIsQ0FBQyxTQUFTLEVBQUVlLE1BQU10QixVQUFVLENBQUM7QUFDNUUsRUFBRXNCLE1BQU1qQixJQUFJLENBQUM7ZUFDRSxFQUFFaUIsTUFBTWYscUJBQXFCLENBQUMsU0FBUyxFQUFFZSxNQUFNdEIsVUFBVSxDQUFDO0FBQ3pFLENBQUM7UUFDRyxHQUFHdUIsSUFBSSxDQUFDO1FBRVIsSUFBSUgsWUFBWTlCLE1BQU0sR0FBR0ksMEJBQTBCO1lBQ2pEeEIsUUFBUWlCLElBQUksQ0FBQyxDQUFDLHVCQUF1QixFQUFFaUMsWUFBWTlCLE1BQU0sQ0FBQyxpQ0FBaUMsRUFBRUkseUJBQXlCLGNBQWMsQ0FBQztZQUNySTBCLGNBQWNBLFlBQVk3QixTQUFTLENBQUMsR0FBR0csNEJBQ3JDLENBQUMscUdBQXFHLEVBQUVBLHlCQUF5QixjQUFjLENBQUM7UUFDcEo7UUFFQSxPQUFPMEI7SUFFVCxFQUFFLE9BQU9qRCxPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQyw0Q0FBNENBO1FBQzFELE9BQU87SUFDVDtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcc3JjXFxsaWJcXGdlbWluaVxcZ2VtaW5pQ2xpZW50LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEdvb2dsZUdlbmVyYXRpdmVBSSwgSGFybUNhdGVnb3J5LCBIYXJtQmxvY2tUaHJlc2hvbGQgfSBmcm9tICdAZ29vZ2xlL2dlbmVyYXRpdmUtYWknO1xuXG4vLyBDb25maWd1cmFjacOzbiBkZSBsYSBBUEkgZGUgR2VtaW5pXG5jb25zdCBBUElfS0VZID0gcHJvY2Vzcy5lbnYuR0VNSU5JX0FQSV9LRVkgfHwgJyc7XG5jb25zdCBNT0RFTF9OQU1FID0gJ2dlbWluaS0yLjAtZmxhc2gtdGhpbmtpbmctZXhwLTAxLTIxJztcblxuLy8gVmVyaWZpY2FyIHF1ZSBsYSBBUEkga2V5IGVzdMOpIGNvbmZpZ3VyYWRhXG5pZiAoIUFQSV9LRVkpIHtcbiAgY29uc29sZS5lcnJvcignR0VNSU5JX0FQSV9LRVkgbm8gZXN0w6EgY29uZmlndXJhZGEgZW4gbGFzIHZhcmlhYmxlcyBkZSBlbnRvcm5vJyk7XG59XG5cbi8vIEluaWNpYWxpemFyIGVsIGNsaWVudGUgZGUgR2VtaW5pXG5leHBvcnQgY29uc3QgZ2VuQUkgPSBuZXcgR29vZ2xlR2VuZXJhdGl2ZUFJKEFQSV9LRVkpO1xuZXhwb3J0IGNvbnN0IG1vZGVsID0gZ2VuQUkuZ2V0R2VuZXJhdGl2ZU1vZGVsKHtcbiAgbW9kZWw6IE1PREVMX05BTUUsXG4gIHNhZmV0eVNldHRpbmdzOiBbXG4gICAge1xuICAgICAgY2F0ZWdvcnk6IEhhcm1DYXRlZ29yeS5IQVJNX0NBVEVHT1JZX0hBUkFTU01FTlQsXG4gICAgICB0aHJlc2hvbGQ6IEhhcm1CbG9ja1RocmVzaG9sZC5CTE9DS19NRURJVU1fQU5EX0FCT1ZFLFxuICAgIH0sXG4gICAge1xuICAgICAgY2F0ZWdvcnk6IEhhcm1DYXRlZ29yeS5IQVJNX0NBVEVHT1JZX0hBVEVfU1BFRUNILFxuICAgICAgdGhyZXNob2xkOiBIYXJtQmxvY2tUaHJlc2hvbGQuQkxPQ0tfTUVESVVNX0FORF9BQk9WRSxcbiAgICB9LFxuICAgIHtcbiAgICAgIGNhdGVnb3J5OiBIYXJtQ2F0ZWdvcnkuSEFSTV9DQVRFR09SWV9TRVhVQUxMWV9FWFBMSUNJVCxcbiAgICAgIHRocmVzaG9sZDogSGFybUJsb2NrVGhyZXNob2xkLkJMT0NLX01FRElVTV9BTkRfQUJPVkUsXG4gICAgfSxcbiAgICB7XG4gICAgICBjYXRlZ29yeTogSGFybUNhdGVnb3J5LkhBUk1fQ0FURUdPUllfREFOR0VST1VTX0NPTlRFTlQsXG4gICAgICB0aHJlc2hvbGQ6IEhhcm1CbG9ja1RocmVzaG9sZC5CTE9DS19NRURJVU1fQU5EX0FCT1ZFLFxuICAgIH0sXG4gIF0sXG59KTtcblxuLy8gWWEgbm8gdXNhbW9zIGluc3RydWNjaW9uZXMgYmFzZSBnbG9iYWxlcywgY2FkYSBzZXJ2aWNpbyB1c2Egc3UgcHJvcGlvIHByb21wdCBwZXJzb25hbGl6YWRvXG5cbi8qKlxuICogVHJ1bmNhIGVsIGNvbnRlbmlkbyBkZSB1biBkb2N1bWVudG8gc2kgZXMgZGVtYXNpYWRvIGxhcmdvXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0cnVuY2FyQ29udGVuaWRvKGNvbnRlbmlkbzogc3RyaW5nIHwgdW5kZWZpbmVkIHwgbnVsbCwgbWF4TGVuZ3RoOiBudW1iZXIgPSAyNTAwMCk6IHN0cmluZyB7XG4gIC8vIFZlcmlmaWNhciBxdWUgZWwgY29udGVuaWRvIHNlYSB1bmEgY2FkZW5hIHbDoWxpZGFcbiAgaWYgKGNvbnRlbmlkbyA9PT0gdW5kZWZpbmVkIHx8IGNvbnRlbmlkbyA9PT0gbnVsbCkge1xuICAgIGNvbnNvbGUud2FybignU2UgaW50ZW50w7MgdHJ1bmNhciB1biBjb250ZW5pZG8gdW5kZWZpbmVkIG8gbnVsbCcpO1xuICAgIHJldHVybiAnJztcbiAgfVxuXG4gIC8vIEFzZWd1cmFyc2UgZGUgcXVlIGVsIGNvbnRlbmlkbyBzZWEgdW5hIGNhZGVuYVxuICBjb25zdCBjb250ZW5pZG9TdHIgPSBTdHJpbmcoY29udGVuaWRvKTtcblxuICBpZiAoY29udGVuaWRvU3RyLmxlbmd0aCA8PSBtYXhMZW5ndGgpIHtcbiAgICByZXR1cm4gY29udGVuaWRvU3RyO1xuICB9XG5cbiAgcmV0dXJuIGNvbnRlbmlkb1N0ci5zdWJzdHJpbmcoMCwgbWF4TGVuZ3RoKSArXG4gICAgYFxcblxcbltDT05URU5JRE8gVFJVTkNBRE86IEVsIGRvY3VtZW50byBvcmlnaW5hbCBlcyBtw6FzIGxhcmdvLiBFc3RhIGVzIHVuYSB2ZXJzacOzbiByZWR1Y2lkYSBwYXJhIHByb2Nlc2FtaWVudG8uXWA7XG59XG5cbi8vIC0tLSBOdWV2YSBsw7NnaWNhIGRlIENodW5raW5nIC0tLVxuXG5jb25zdCBDSFVOS19TSVpFID0gNTAwMDsgLy8gQ2FyYWN0ZXJlc1xuY29uc3QgQ0hVTktfT1ZFUkxBUCA9IDIwMDsgLy8gQ2FyYWN0ZXJlc1xuY29uc3QgTUFYX1RPVEFMX0NPTlRFWFRfTEVOR1RIID0gNTAwMDA7IC8vIENhcmFjdGVyZXNcblxuaW50ZXJmYWNlIERvY3VtZW50Q2h1bmsge1xuICBvcmlnaW5hbERvY3VtZW50VGl0bGU6IHN0cmluZztcbiAgY2h1bmtJbmRleDogbnVtYmVyO1xuICB0ZXh0OiBzdHJpbmc7XG59XG5cbmZ1bmN0aW9uIGNyZWF0ZVRleHRDaHVua3MoXG4gIGRvY3VtZW50VGl0bGU6IHN0cmluZyxcbiAgY29udGVudDogc3RyaW5nIHwgdW5kZWZpbmVkIHwgbnVsbFxuKTogRG9jdW1lbnRDaHVua1tdIHtcbiAgaWYgKCFjb250ZW50KSB7XG4gICAgcmV0dXJuIFtdO1xuICB9XG4gIGNvbnN0IGNvbnRlbnRTdHIgPSBTdHJpbmcoY29udGVudCk7XG4gIGNvbnN0IGNodW5rczogRG9jdW1lbnRDaHVua1tdID0gW107XG4gIGxldCBjaHVua0luZGV4ID0gMDtcbiAgbGV0IGN1cnJlbnRJbmRleCA9IDA7XG5cbiAgd2hpbGUgKGN1cnJlbnRJbmRleCA8IGNvbnRlbnRTdHIubGVuZ3RoKSB7XG4gICAgY29uc3QgZW5kSW5kZXggPSBNYXRoLm1pbihjdXJyZW50SW5kZXggKyBDSFVOS19TSVpFLCBjb250ZW50U3RyLmxlbmd0aCk7XG4gICAgY29uc3QgdGV4dCA9IGNvbnRlbnRTdHIuc3Vic3RyaW5nKGN1cnJlbnRJbmRleCwgZW5kSW5kZXgpO1xuICAgIGNodW5rcy5wdXNoKHtcbiAgICAgIG9yaWdpbmFsRG9jdW1lbnRUaXRsZTogZG9jdW1lbnRUaXRsZSxcbiAgICAgIGNodW5rSW5kZXg6IGNodW5rSW5kZXggKyAxLCAvLyBFbXBlemFyIMOtbmRpY2UgZW4gMSBwYXJhIGVsIHVzdWFyaW9cbiAgICAgIHRleHQsXG4gICAgfSk7XG5cbiAgICBjaHVua0luZGV4Kys7XG4gICAgaWYgKGVuZEluZGV4ID09PSBjb250ZW50U3RyLmxlbmd0aCkge1xuICAgICAgYnJlYWs7IFxuICAgIH1cbiAgICBjdXJyZW50SW5kZXggKz0gQ0hVTktfU0laRSAtIENIVU5LX09WRVJMQVA7XG4gICAgLy8gQXNlZ3VyYXIgcXVlIG5vIGhheWEgdW4gYnVjbGUgaW5maW5pdG8gc2kgQ0hVTktfT1ZFUkxBUCA+PSBDSFVOS19TSVpFXG4gICAgaWYgKGN1cnJlbnRJbmRleCA+PSBlbmRJbmRleCAmJiBlbmRJbmRleCA8IGNvbnRlbnRTdHIubGVuZ3RoKSB7IFxuICAgICAgICBjdXJyZW50SW5kZXggPSBlbmRJbmRleDsgLy8gQXZhbnphciBhbCBtZW5vcyBoYXN0YSBlbCBmaW5hbCBkZWwgY2h1bmsgYWN0dWFsXG4gICAgfVxuICB9XG4gIHJldHVybiBjaHVua3M7XG59XG5cbi8qKlxuICogUHJlcGFyYSBsb3MgZG9jdW1lbnRvcyBwYXJhIGVudmlhcmxvcyBhbCBtb2RlbG8sIGRpdmlkacOpbmRvbG9zIGVuIGNodW5rcy5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHByZXBhcmFyRG9jdW1lbnRvcyhkb2N1bWVudG9zOiB7IHRpdHVsbzogc3RyaW5nOyBjb250ZW5pZG86IHN0cmluZzsgY2F0ZWdvcmlhPzogc3RyaW5nOyBudW1lcm9fdGVtYT86IG51bWJlciB9W10pOiBzdHJpbmcge1xuICBpZiAoIWRvY3VtZW50b3MgfHwgIUFycmF5LmlzQXJyYXkoZG9jdW1lbnRvcykgfHwgZG9jdW1lbnRvcy5sZW5ndGggPT09IDApIHtcbiAgICBjb25zb2xlLndhcm4oJ05vIHNlIHByb3BvcmNpb25hcm9uIGRvY3VtZW50b3MgdsOhbGlkb3MgcGFyYSBwcmVwYXJhckRvY3VtZW50b3MnKTtcbiAgICByZXR1cm4gJyc7XG4gIH1cblxuICB0cnkge1xuICAgIGNvbnN0IGFsbENodW5rczogRG9jdW1lbnRDaHVua1tdID0gW107XG5cbiAgICBmb3IgKGNvbnN0IGRvYyBvZiBkb2N1bWVudG9zKSB7XG4gICAgICBpZiAoIWRvYyB8fCB0eXBlb2YgZG9jICE9PSAnb2JqZWN0JyB8fCAhZG9jLnRpdHVsbyB8fCBkb2MuY29udGVuaWRvID09PSB1bmRlZmluZWQgfHwgZG9jLmNvbnRlbmlkbyA9PT0gbnVsbCkge1xuICAgICAgICBjb25zb2xlLndhcm4oJ0RvY3VtZW50byBpbnbDoWxpZG8sIHNpbiB0w610dWxvIG8gY29udGVuaWRvIGVuIHByZXBhcmFyRG9jdW1lbnRvczonLCBkb2MpO1xuICAgICAgICBjb250aW51ZTsgXG4gICAgICB9XG4gICAgICBcbiAgICAgIGNvbnN0IGZ1bGxPcmlnaW5hbFRpdGxlID0gYCR7ZG9jLmNhdGVnb3JpYSA/IGBbJHtkb2MuY2F0ZWdvcmlhfV0gYCA6ICcnfSR7ZG9jLm51bWVyb190ZW1hID8gYFRlbWEgJHtkb2MubnVtZXJvX3RlbWF9OiBgIDogJyd9JHtkb2MudGl0dWxvfWA7XG4gICAgICBjb25zdCBkb2N1bWVudENodW5rcyA9IGNyZWF0ZVRleHRDaHVua3MoZnVsbE9yaWdpbmFsVGl0bGUudHJpbSgpLCBkb2MuY29udGVuaWRvKTtcbiAgICAgIGFsbENodW5rcy5wdXNoKC4uLmRvY3VtZW50Q2h1bmtzKTtcbiAgICB9XG5cbiAgICBpZiAoYWxsQ2h1bmtzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgY29uc29sZS53YXJuKCdObyBzZSBnZW5lcmFyb24gY2h1bmtzIGEgcGFydGlyIGRlIGxvcyBkb2N1bWVudG9zIHByb3BvcmNpb25hZG9zLicpO1xuICAgICAgcmV0dXJuICcnO1xuICAgIH1cblxuICAgIGxldCBmdWxsQ29udGV4dCA9IGFsbENodW5rcy5tYXAoY2h1bmsgPT4ge1xuICAgICAgcmV0dXJuIGBcbj09PSBJTklDSU8gQ0hVTks6ICR7Y2h1bmsub3JpZ2luYWxEb2N1bWVudFRpdGxlfSAtIFBhcnRlICR7Y2h1bmsuY2h1bmtJbmRleH0gPT09XG4ke2NodW5rLnRleHR9XG49PT0gRklOIENIVU5LOiAke2NodW5rLm9yaWdpbmFsRG9jdW1lbnRUaXRsZX0gLSBQYXJ0ZSAke2NodW5rLmNodW5rSW5kZXh9ID09PVxuYDtcbiAgICB9KS5qb2luKCdcXG5cXG4nKTtcblxuICAgIGlmIChmdWxsQ29udGV4dC5sZW5ndGggPiBNQVhfVE9UQUxfQ09OVEVYVF9MRU5HVEgpIHtcbiAgICAgIGNvbnNvbGUud2FybihgRWwgY29udGV4dG8gY29tYmluYWRvICgke2Z1bGxDb250ZXh0Lmxlbmd0aH0gY2FyYWN0ZXJlcykgZXhjZWRlIGVsIG3DoXhpbW8gZGUgJHtNQVhfVE9UQUxfQ09OVEVYVF9MRU5HVEh9LiBTZSB0cnVuY2Fyw6EuYCk7XG4gICAgICBmdWxsQ29udGV4dCA9IGZ1bGxDb250ZXh0LnN1YnN0cmluZygwLCBNQVhfVE9UQUxfQ09OVEVYVF9MRU5HVEgpICtcbiAgICAgICAgYFxcblxcbltDT05URVhUTyBHRU5FUkFMIFRSVU5DQURPOiBFbCBjb250ZW5pZG8gY29tYmluYWRvIGRlIHRvZG9zIGxvcyBjaHVua3MgZXhjZWRpw7MgZWwgbMOtbWl0ZSBtw6F4aW1vICgke01BWF9UT1RBTF9DT05URVhUX0xFTkdUSH0gY2FyYWN0ZXJlcykuXWA7XG4gICAgfVxuICAgIFxuICAgIHJldHVybiBmdWxsQ29udGV4dDtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIHByZXBhcmFyIGRvY3VtZW50b3MgY29uIGNodW5rczonLCBlcnJvcik7XG4gICAgcmV0dXJuICcnO1xuICB9XG59XG4iXSwibmFtZXMiOlsiR29vZ2xlR2VuZXJhdGl2ZUFJIiwiSGFybUNhdGVnb3J5IiwiSGFybUJsb2NrVGhyZXNob2xkIiwiQVBJX0tFWSIsInByb2Nlc3MiLCJlbnYiLCJHRU1JTklfQVBJX0tFWSIsIk1PREVMX05BTUUiLCJjb25zb2xlIiwiZXJyb3IiLCJnZW5BSSIsIm1vZGVsIiwiZ2V0R2VuZXJhdGl2ZU1vZGVsIiwic2FmZXR5U2V0dGluZ3MiLCJjYXRlZ29yeSIsIkhBUk1fQ0FURUdPUllfSEFSQVNTTUVOVCIsInRocmVzaG9sZCIsIkJMT0NLX01FRElVTV9BTkRfQUJPVkUiLCJIQVJNX0NBVEVHT1JZX0hBVEVfU1BFRUNIIiwiSEFSTV9DQVRFR09SWV9TRVhVQUxMWV9FWFBMSUNJVCIsIkhBUk1fQ0FURUdPUllfREFOR0VST1VTX0NPTlRFTlQiLCJ0cnVuY2FyQ29udGVuaWRvIiwiY29udGVuaWRvIiwibWF4TGVuZ3RoIiwidW5kZWZpbmVkIiwid2FybiIsImNvbnRlbmlkb1N0ciIsIlN0cmluZyIsImxlbmd0aCIsInN1YnN0cmluZyIsIkNIVU5LX1NJWkUiLCJDSFVOS19PVkVSTEFQIiwiTUFYX1RPVEFMX0NPTlRFWFRfTEVOR1RIIiwiY3JlYXRlVGV4dENodW5rcyIsImRvY3VtZW50VGl0bGUiLCJjb250ZW50IiwiY29udGVudFN0ciIsImNodW5rcyIsImNodW5rSW5kZXgiLCJjdXJyZW50SW5kZXgiLCJlbmRJbmRleCIsIk1hdGgiLCJtaW4iLCJ0ZXh0IiwicHVzaCIsIm9yaWdpbmFsRG9jdW1lbnRUaXRsZSIsInByZXBhcmFyRG9jdW1lbnRvcyIsImRvY3VtZW50b3MiLCJBcnJheSIsImlzQXJyYXkiLCJhbGxDaHVua3MiLCJkb2MiLCJ0aXR1bG8iLCJmdWxsT3JpZ2luYWxUaXRsZSIsImNhdGVnb3JpYSIsIm51bWVyb190ZW1hIiwiZG9jdW1lbnRDaHVua3MiLCJ0cmltIiwiZnVsbENvbnRleHQiLCJtYXAiLCJjaHVuayIsImpvaW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/geminiClient.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/index.ts":
/*!*********************************!*\
  !*** ./src/lib/gemini/index.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   genAI: () => (/* reexport safe */ _geminiClient__WEBPACK_IMPORTED_MODULE_0__.genAI),\n/* harmony export */   generarFlashcards: () => (/* binding */ generarFlashcards),\n/* harmony export */   generarMapaMental: () => (/* binding */ generarMapaMental),\n/* harmony export */   generarTest: () => (/* binding */ generarTest),\n/* harmony export */   model: () => (/* reexport safe */ _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model),\n/* harmony export */   obtenerRespuestaIA: () => (/* reexport safe */ _questionService__WEBPACK_IMPORTED_MODULE_1__.obtenerRespuestaIA),\n/* harmony export */   prepararDocumentos: () => (/* reexport safe */ _geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos),\n/* harmony export */   truncarContenido: () => (/* reexport safe */ _geminiClient__WEBPACK_IMPORTED_MODULE_0__.truncarContenido)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _questionService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./questionService */ \"(rsc)/./src/lib/gemini/questionService.ts\");\n/* harmony import */ var _flashcardGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./flashcardGenerator */ \"(rsc)/./src/lib/gemini/flashcardGenerator.ts\");\n/* harmony import */ var _testGenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./testGenerator */ \"(rsc)/./src/lib/gemini/testGenerator.ts\");\n/* harmony import */ var _mindMapGenerator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./mindMapGenerator */ \"(rsc)/./src/lib/gemini/mindMapGenerator.ts\");\n// Exportar todo desde los archivos individuales\n\n\n\n\n\n// Función adaptadora para compatibilidad con la interfaz anterior de flashcards\nasync function generarFlashcards(peticion, contextos) {\n    // Convertir los contextos al formato esperado por la función original\n    const documentos = contextos.map((contenido, index)=>({\n            titulo: `Documento ${index + 1}`,\n            contenido\n        }));\n    // Llamar a la función original con los documentos formateados y la petición como instrucción\n    return await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./flashcardGenerator */ \"(rsc)/./src/lib/gemini/flashcardGenerator.ts\")).then((module)=>module.generarFlashcards(documentos, 10, peticion));\n}\n// Función adaptadora para compatibilidad con la interfaz anterior de mapas mentales\nasync function generarMapaMental(peticion, contextos) {\n    // Convertir los contextos al formato esperado por la función original\n    const documentos = contextos.map((contenido, index)=>({\n            titulo: `Documento ${index + 1}`,\n            contenido\n        }));\n    // Llamar a la función original con los documentos formateados y la petición como instrucción\n    return await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./mindMapGenerator */ \"(rsc)/./src/lib/gemini/mindMapGenerator.ts\")).then((module)=>module.generarMapaMental(documentos, peticion));\n}\n// Función adaptadora para compatibilidad con la interfaz anterior de tests\nasync function generarTest(peticion, contextos) {\n    // Convertir los contextos al formato esperado por la función original\n    const documentos = contextos.map((contenido, index)=>({\n            titulo: `Documento ${index + 1}`,\n            contenido\n        }));\n    // Llamar a la función original con los documentos formateados y la petición como instrucción\n    const result = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./testGenerator */ \"(rsc)/./src/lib/gemini/testGenerator.ts\")).then((module)=>module.generarTest(documentos, 10, peticion));\n    // Convertir el formato de la respuesta al formato esperado por el componente\n    return result.map((item)=>({\n            pregunta: item.pregunta,\n            opciones: {\n                a: item.opcion_a,\n                b: item.opcion_b,\n                c: item.opcion_c,\n                d: item.opcion_d\n            },\n            respuesta_correcta: item.respuesta_correcta\n        }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/mindMapGenerator.ts":
/*!********************************************!*\
  !*** ./src/lib/gemini/mindMapGenerator.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generarMapaMental: () => (/* binding */ generarMapaMental)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _config_prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config/prompts */ \"(rsc)/./src/config/prompts.ts\");\n\n\n/**\n * Genera un mapa mental a partir de los documentos\n */ async function generarMapaMental(documentos, instrucciones) {\n    try {\n        // Preparar el contenido de los documentos\n        const contenidoDocumentos = (0,_geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos)(documentos);\n        if (!contenidoDocumentos) {\n            throw new Error(\"No se han proporcionado documentos para generar el mapa mental.\");\n        }\n        // Construir el prompt final\n        let finalPrompt = _config_prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPT_MAPAS_MENTALES.replace('{documentos}', contenidoDocumentos);\n        finalPrompt = finalPrompt.replace('{instrucciones}', instrucciones || 'Crea un mapa mental que organice los conceptos principales del contenido.');\n        // Generar el mapa mental\n        const result = await _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model.generateContent(finalPrompt);\n        const response = result.response.text();\n        // Extraer el HTML completo de la respuesta (el prompt genera HTML completo)\n        // El mapa mental se genera como HTML con D3.js, no como JSON\n        // Verificar que la respuesta contiene HTML válido\n        if (!response.includes('<!DOCTYPE html>') && !response.includes('<html')) {\n            console.error('Respuesta de Gemini para mapa mental:', response);\n            throw new Error(\"La respuesta no contiene HTML válido para el mapa mental.\");\n        }\n        // Retornar el HTML completo como string\n        return response;\n    } catch (error) {\n        console.error('Error al generar mapa mental:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/mindMapGenerator.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/questionService.ts":
/*!*******************************************!*\
  !*** ./src/lib/gemini/questionService.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   obtenerRespuestaIA: () => (/* binding */ obtenerRespuestaIA)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _config_prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../config/prompts */ \"(rsc)/./src/config/prompts.ts\");\n\n\n/**\n * Obtiene una respuesta de la IA a una pregunta sobre los documentos\n */ async function obtenerRespuestaIA(pregunta, documentos) {\n    try {\n        // Verificar que la pregunta sea válida\n        if (!pregunta || typeof pregunta !== 'string' || pregunta.trim() === '') {\n            console.warn('Se recibió una pregunta vacía o inválida');\n            return \"Por favor, proporciona una pregunta válida.\";\n        }\n        // Verificar que los documentos sean válidos\n        if (!documentos || !Array.isArray(documentos) || documentos.length === 0) {\n            console.warn('No se proporcionaron documentos válidos para obtenerRespuestaIA');\n            return \"No se han proporcionado documentos para responder a esta pregunta.\";\n        }\n        // Preparar el contenido de los documentos\n        const contenidoDocumentos = (0,_geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos)(documentos);\n        if (!contenidoDocumentos) {\n            console.warn('No se pudo preparar el contenido de los documentos');\n            return \"No se han podido procesar los documentos proporcionados. Por favor, verifica que los documentos contengan información válida.\";\n        }\n        // Construir el prompt para la IA usando el prompt personalizado\n        // Reemplazar las variables en el prompt\n        const prompt = _config_prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPT_PREGUNTAS.replace('{documentos}', contenidoDocumentos).replace('{pregunta}', pregunta);\n        // Generar la respuesta\n        const result = await _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model.generateContent(prompt);\n        const response = result.response;\n        return response.text();\n    } catch (error) {\n        console.error('Error al obtener respuesta de la IA:', error);\n        // Proporcionar un mensaje de error más descriptivo si es posible\n        if (error instanceof Error) {\n            return `Lo siento, ha ocurrido un error al procesar tu pregunta: ${error.message}. Por favor, inténtalo de nuevo más tarde.`;\n        }\n        return \"Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, inténtalo de nuevo más tarde.\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/questionService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/testGenerator.ts":
/*!*****************************************!*\
  !*** ./src/lib/gemini/testGenerator.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generarTest: () => (/* binding */ generarTest)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _config_prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../config/prompts */ \"(rsc)/./src/config/prompts.ts\");\n\n\n/**\n * Genera un test con preguntas de opción múltiple a partir de los documentos\n */ async function generarTest(documentos, cantidad = 10, instrucciones) {\n    try {\n        // Preparar el contenido de los documentos\n        const contenidoDocumentos = (0,_geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos)(documentos);\n        if (!contenidoDocumentos) {\n            throw new Error(\"No se han proporcionado documentos para generar el test.\");\n        }\n        // Construir el prompt para la IA usando el prompt personalizado\n        // Reemplazar las variables en el prompt\n        let prompt = _config_prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPT_TESTS.replace('{documentos}', contenidoDocumentos).replace('{cantidad}', cantidad.toString());\n        // Añadir instrucciones adicionales si se proporcionan\n        if (instrucciones) {\n            prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\\n- ${instrucciones}`);\n        } else {\n            prompt = prompt.replace('{instrucciones}', '');\n        }\n        // Generar el test\n        const result = await _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model.generateContent(prompt);\n        const response = result.response.text();\n        // Extraer el JSON de la respuesta\n        const jsonMatch = response.match(/\\[\\s*\\{[\\s\\S]*\\}\\s*\\]/);\n        if (!jsonMatch) {\n            throw new Error(\"No se pudo extraer el formato JSON de la respuesta.\");\n        }\n        const testJson = jsonMatch[0];\n        const preguntas = JSON.parse(testJson);\n        // Validar el formato\n        if (!Array.isArray(preguntas) || preguntas.length === 0) {\n            throw new Error(\"El formato de las preguntas generadas no es válido.\");\n        }\n        // Validar que cada pregunta tiene el formato correcto\n        preguntas.forEach((pregunta, index)=>{\n            if (!pregunta.pregunta || !pregunta.opcion_a || !pregunta.opcion_b || !pregunta.opcion_c || !pregunta.opcion_d || !pregunta.respuesta_correcta) {\n                throw new Error(`La pregunta ${index + 1} no tiene el formato correcto.`);\n            }\n            // Asegurarse de que la respuesta correcta es una de las opciones válidas\n            if (![\n                'a',\n                'b',\n                'c',\n                'd'\n            ].includes(pregunta.respuesta_correcta)) {\n                throw new Error(`La respuesta correcta de la pregunta ${index + 1} no es válida.`);\n            }\n        });\n        return preguntas;\n    } catch (error) {\n        console.error('Error al generar test:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/testGenerator.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerSupabaseClient: () => (/* binding */ createServerSupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// Cliente para el servidor (componentes del servidor, API routes)\nasync function createServerSupabaseClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\", {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/zodSchemas.ts":
/*!*******************************!*\
  !*** ./src/lib/zodSchemas.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiGeminiInputSchema: () => (/* binding */ ApiGeminiInputSchema),\n/* harmony export */   DocumentoSchema: () => (/* binding */ DocumentoSchema),\n/* harmony export */   GenerarFlashcardsSchema: () => (/* binding */ GenerarFlashcardsSchema),\n/* harmony export */   GenerarMapaMentalSchema: () => (/* binding */ GenerarMapaMentalSchema),\n/* harmony export */   GenerarTestSchema: () => (/* binding */ GenerarTestSchema),\n/* harmony export */   PreguntaSchema: () => (/* binding */ PreguntaSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n// Directorio para esquemas Zod reutilizables\n\nconst DocumentoSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    titulo: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(200),\n    contenido: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1),\n    categoria: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional().nullable(),\n    numero_tema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.z.number().int().positive(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.z[\"null\"](),\n        zod__WEBPACK_IMPORTED_MODULE_0__.z.undefined()\n    ]).optional(),\n    creado_en: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    actualizado_en: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    user_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    tipo_original: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional()\n});\nconst PreguntaSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    pregunta: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(500),\n    documentos: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(DocumentoSchema).min(1)\n});\nconst GenerarTestSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    action: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal('generarTest'),\n    peticion: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(500),\n    contextos: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1))\n});\nconst GenerarFlashcardsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    action: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal('generarFlashcards'),\n    peticion: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(500),\n    contextos: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1))\n});\nconst GenerarMapaMentalSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    action: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal('generarMapaMental'),\n    peticion: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(500),\n    contextos: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1))\n});\nconst ApiGeminiInputSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n    PreguntaSchema,\n    GenerarTestSchema,\n    GenerarFlashcardsSchema,\n    GenerarMapaMentalSchema\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3pvZFNjaGVtYXMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBLDZDQUE2QztBQUNyQjtBQUVqQixNQUFNQyxrQkFBa0JELHlDQUFRLENBQUM7SUFDdENHLElBQUlILHlDQUFRLEdBQUdLLFFBQVE7SUFDdkJDLFFBQVFOLHlDQUFRLEdBQUdPLEdBQUcsQ0FBQyxHQUFHQyxHQUFHLENBQUM7SUFDOUJDLFdBQVdULHlDQUFRLEdBQUdPLEdBQUcsQ0FBQztJQUMxQkcsV0FBV1YseUNBQVEsR0FBR0ssUUFBUSxHQUFHTSxRQUFRO0lBQ3pDQyxhQUFhWix3Q0FBTyxDQUFDO1FBQUNBLHlDQUFRLEdBQUdlLEdBQUcsR0FBR0MsUUFBUTtRQUFJaEIseUNBQVE7UUFBSUEsMENBQU07UUFBSUEsNENBQVc7S0FBRyxFQUFFSyxRQUFRO0lBQ2pHYyxXQUFXbkIseUNBQVEsR0FBR0ssUUFBUTtJQUM5QmUsZ0JBQWdCcEIseUNBQVEsR0FBR0ssUUFBUTtJQUNuQ2dCLFNBQVNyQix5Q0FBUSxHQUFHSyxRQUFRO0lBQzVCaUIsZUFBZXRCLHlDQUFRLEdBQUdLLFFBQVE7QUFDcEMsR0FBRztBQUVJLE1BQU1rQixpQkFBaUJ2Qix5Q0FBUSxDQUFDO0lBQ3JDd0IsVUFBVXhCLHlDQUFRLEdBQUdPLEdBQUcsQ0FBQyxHQUFHQyxHQUFHLENBQUM7SUFDaENpQixZQUFZekIsd0NBQU8sQ0FBQ0MsaUJBQWlCTSxHQUFHLENBQUM7QUFDM0MsR0FBRztBQUVJLE1BQU1vQixvQkFBb0IzQix5Q0FBUSxDQUFDO0lBQ3hDNEIsUUFBUTVCLDBDQUFTLENBQUM7SUFDbEI4QixVQUFVOUIseUNBQVEsR0FBR08sR0FBRyxDQUFDLEdBQUdDLEdBQUcsQ0FBQztJQUNoQ3VCLFdBQVcvQix3Q0FBTyxDQUFDQSx5Q0FBUSxHQUFHTyxHQUFHLENBQUM7QUFDcEMsR0FBRztBQUVJLE1BQU15QiwwQkFBMEJoQyx5Q0FBUSxDQUFDO0lBQzlDNEIsUUFBUTVCLDBDQUFTLENBQUM7SUFDbEI4QixVQUFVOUIseUNBQVEsR0FBR08sR0FBRyxDQUFDLEdBQUdDLEdBQUcsQ0FBQztJQUNoQ3VCLFdBQVcvQix3Q0FBTyxDQUFDQSx5Q0FBUSxHQUFHTyxHQUFHLENBQUM7QUFDcEMsR0FBRztBQUVJLE1BQU0wQiwwQkFBMEJqQyx5Q0FBUSxDQUFDO0lBQzlDNEIsUUFBUTVCLDBDQUFTLENBQUM7SUFDbEI4QixVQUFVOUIseUNBQVEsR0FBR08sR0FBRyxDQUFDLEdBQUdDLEdBQUcsQ0FBQztJQUNoQ3VCLFdBQVcvQix3Q0FBTyxDQUFDQSx5Q0FBUSxHQUFHTyxHQUFHLENBQUM7QUFDcEMsR0FBRztBQUVJLE1BQU0yQix1QkFBdUJsQyx3Q0FBTyxDQUFDO0lBQzFDdUI7SUFDQUk7SUFDQUs7SUFDQUM7Q0FDRCxFQUFFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcc3JjXFxsaWJcXHpvZFNjaGVtYXMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRGlyZWN0b3JpbyBwYXJhIGVzcXVlbWFzIFpvZCByZXV0aWxpemFibGVzXG5pbXBvcnQgeyB6IH0gZnJvbSAnem9kJztcblxuZXhwb3J0IGNvbnN0IERvY3VtZW50b1NjaGVtYSA9IHoub2JqZWN0KHtcbiAgaWQ6IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgdGl0dWxvOiB6LnN0cmluZygpLm1pbigxKS5tYXgoMjAwKSxcbiAgY29udGVuaWRvOiB6LnN0cmluZygpLm1pbigxKSxcbiAgY2F0ZWdvcmlhOiB6LnN0cmluZygpLm9wdGlvbmFsKCkubnVsbGFibGUoKSxcbiAgbnVtZXJvX3RlbWE6IHoudW5pb24oW3oubnVtYmVyKCkuaW50KCkucG9zaXRpdmUoKSwgei5zdHJpbmcoKSwgei5udWxsKCksIHoudW5kZWZpbmVkKCldKS5vcHRpb25hbCgpLFxuICBjcmVhZG9fZW46IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgYWN0dWFsaXphZG9fZW46IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgdXNlcl9pZDogei5zdHJpbmcoKS5vcHRpb25hbCgpLFxuICB0aXBvX29yaWdpbmFsOiB6LnN0cmluZygpLm9wdGlvbmFsKCksXG59KTtcblxuZXhwb3J0IGNvbnN0IFByZWd1bnRhU2NoZW1hID0gei5vYmplY3Qoe1xuICBwcmVndW50YTogei5zdHJpbmcoKS5taW4oMSkubWF4KDUwMCksXG4gIGRvY3VtZW50b3M6IHouYXJyYXkoRG9jdW1lbnRvU2NoZW1hKS5taW4oMSksXG59KTtcblxuZXhwb3J0IGNvbnN0IEdlbmVyYXJUZXN0U2NoZW1hID0gei5vYmplY3Qoe1xuICBhY3Rpb246IHoubGl0ZXJhbCgnZ2VuZXJhclRlc3QnKSxcbiAgcGV0aWNpb246IHouc3RyaW5nKCkubWluKDEpLm1heCg1MDApLFxuICBjb250ZXh0b3M6IHouYXJyYXkoei5zdHJpbmcoKS5taW4oMSkpLFxufSk7XG5cbmV4cG9ydCBjb25zdCBHZW5lcmFyRmxhc2hjYXJkc1NjaGVtYSA9IHoub2JqZWN0KHtcbiAgYWN0aW9uOiB6LmxpdGVyYWwoJ2dlbmVyYXJGbGFzaGNhcmRzJyksXG4gIHBldGljaW9uOiB6LnN0cmluZygpLm1pbigxKS5tYXgoNTAwKSxcbiAgY29udGV4dG9zOiB6LmFycmF5KHouc3RyaW5nKCkubWluKDEpKSxcbn0pO1xuXG5leHBvcnQgY29uc3QgR2VuZXJhck1hcGFNZW50YWxTY2hlbWEgPSB6Lm9iamVjdCh7XG4gIGFjdGlvbjogei5saXRlcmFsKCdnZW5lcmFyTWFwYU1lbnRhbCcpLFxuICBwZXRpY2lvbjogei5zdHJpbmcoKS5taW4oMSkubWF4KDUwMCksXG4gIGNvbnRleHRvczogei5hcnJheSh6LnN0cmluZygpLm1pbigxKSksXG59KTtcblxuZXhwb3J0IGNvbnN0IEFwaUdlbWluaUlucHV0U2NoZW1hID0gei51bmlvbihbXG4gIFByZWd1bnRhU2NoZW1hLFxuICBHZW5lcmFyVGVzdFNjaGVtYSxcbiAgR2VuZXJhckZsYXNoY2FyZHNTY2hlbWEsXG4gIEdlbmVyYXJNYXBhTWVudGFsU2NoZW1hLFxuXSk7XG4iXSwibmFtZXMiOlsieiIsIkRvY3VtZW50b1NjaGVtYSIsIm9iamVjdCIsImlkIiwic3RyaW5nIiwib3B0aW9uYWwiLCJ0aXR1bG8iLCJtaW4iLCJtYXgiLCJjb250ZW5pZG8iLCJjYXRlZ29yaWEiLCJudWxsYWJsZSIsIm51bWVyb190ZW1hIiwidW5pb24iLCJudW1iZXIiLCJpbnQiLCJwb3NpdGl2ZSIsIm51bGwiLCJ1bmRlZmluZWQiLCJjcmVhZG9fZW4iLCJhY3R1YWxpemFkb19lbiIsInVzZXJfaWQiLCJ0aXBvX29yaWdpbmFsIiwiUHJlZ3VudGFTY2hlbWEiLCJwcmVndW50YSIsImRvY3VtZW50b3MiLCJhcnJheSIsIkdlbmVyYXJUZXN0U2NoZW1hIiwiYWN0aW9uIiwibGl0ZXJhbCIsInBldGljaW9uIiwiY29udGV4dG9zIiwiR2VuZXJhckZsYXNoY2FyZHNTY2hlbWEiLCJHZW5lcmFyTWFwYU1lbnRhbFNjaGVtYSIsIkFwaUdlbWluaUlucHV0U2NoZW1hIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/zodSchemas.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/zod","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/@google"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgemini%2Froute&page=%2Fapi%2Fgemini%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgemini%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();