import { NextResponse, type NextRequest } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const contentType = request.headers.get('content-type');
    if (!contentType || !contentType.includes('multipart/form-data')) {
      return NextResponse.json(
        { error: "El cuerpo de la petición debe ser 'multipart/form-data'." },
        { status: 415 } // Unsupported Media Type
      );
    }

    const formData = await request.formData();
    const file = formData.get('file') as File | null;

    // Obtener datos adicionales del formulario
    const titulo = formData.get('titulo') as string || '';
    const categoria = formData.get('categoria') as string || undefined;
    const numeroTemaStr = formData.get('numero_tema') as string;
    const numeroTema = numeroTemaStr ? parseInt(numeroTemaStr) : undefined;

    if (!file) {
      return NextResponse.json(
        { error: 'No se proporcionó ningún archivo.' },
        { status: 400 }
      );
    }

    let extractedText: string;
    let originalFileType: string;
    const fileBuffer = await file.arrayBuffer();

    if (file.type === 'application/pdf') {
      try {
        // Import pdf-lib
        const { PDFDocument } = await import('pdf-lib');

        // Load the PDF with pdf-lib
        const pdfDoc = await PDFDocument.load(fileBuffer);
        const numPages = pdfDoc.getPageCount();

        // Define configurable crop percentages for top and bottom
        const CROP_MARGIN_TOP_PERCENT = 0.08; // Example: 8% from the top
        const CROP_MARGIN_BOTTOM_PERCENT = 0.08; // Example: 8% from the bottom

        for (let i = 0; i < numPages; i++) {
          const page = pdfDoc.getPage(i);
          const { width: originalWidth, height: originalHeight } = page.getSize();

          const cropAmountBottom = originalHeight * CROP_MARGIN_BOTTOM_PERCENT;
          const cropAmountTop = originalHeight * CROP_MARGIN_TOP_PERCENT;

          let newY = cropAmountBottom;
          let newHeight = originalHeight - cropAmountBottom - cropAmountTop;

          // Ensure newHeight is not negative
          if (newHeight < 0) {
            console.warn(`Page ${i + 1}: Calculated newHeight (${newHeight}) is negative. Margins (${CROP_MARGIN_TOP_PERCENT*100}%, ${CROP_MARGIN_BOTTOM_PERCENT*100}%) might be too large for page height ${originalHeight}. Resetting to prevent error, page will not be cropped effectively.`);
            newY = 0; // Reset y to prevent invalid box
            newHeight = originalHeight; // Reset height
          }

          // Set the crop box [x, y, width, height]
          // Origin (0,0) is bottom-left
          page.setCropBox(
            0, // x: typically the left edge of the page
            newY, // y: the new bottom edge after cropping
            originalWidth, // width: typically the full width of the page
            newHeight // height: the new height of the page content area
          );
        }

        // Save the modified PDF to a new buffer
        const modifiedPdfBuffer = await pdfDoc.save();

        // Pass the modified buffer to pdf-parse
        const pdf = await (await import('pdf-parse')).default(Buffer.from(modifiedPdfBuffer));
        extractedText = pdf.text;
        originalFileType = 'pdf';
      } catch (parseError) {
        console.error('Error al procesar PDF:', parseError);
        const errorMessage = parseError instanceof Error ? parseError.message : 'Error desconocido al procesar PDF';
        return NextResponse.json(
          { error: 'Error al procesar el archivo PDF.', details: errorMessage },
          { status: 422 } // Unprocessable Entity
        );
      }
    } else if (file.type === 'text/plain') {
      extractedText = Buffer.from(fileBuffer).toString('utf-8');
      originalFileType = 'txt';
    } else {
      return NextResponse.json(
        { error: `Tipo de archivo no soportado: ${file.type}. Solo se permiten .txt y .pdf.` },
        { status: 415 } // Unsupported Media Type
      );
    }

    if (!extractedText || extractedText.trim() === '') {
      return NextResponse.json(
        { error: 'El contenido extraído del archivo está vacío.' },
        { status: 422 } // Unprocessable Entity
      );
    }

    // Importar guardarDocumentoServer para usar en el servidor
    const { guardarDocumentoServer } = await import('@/lib/supabase/documentosService.server');

    const documentId = await guardarDocumentoServer({
      titulo: titulo || file.name, // Usar el título del formulario o el nombre del archivo como fallback
      contenido: extractedText,
      categoria: categoria, // Usar la categoría del formulario
      numero_tema: numeroTema, // Usar el número de tema del formulario
      tipo_original: originalFileType,
    });

    if (documentId) {
      return NextResponse.json(
        { message: 'Documento procesado y guardado con éxito.', documentId },
        { status: 201 }
      );
    } else {
      return NextResponse.json(
        { error: 'Error al guardar el documento en la base de datos.' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error en el endpoint de subida:', error);
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
    return NextResponse.json(
      { error: 'Error interno del servidor.', details: errorMessage },
      { status: 500 }
    );
  }
}
