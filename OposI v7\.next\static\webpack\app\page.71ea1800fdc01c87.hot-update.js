"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/QuestionForm.tsx":
/*!*****************************************!*\
  !*** ./src/components/QuestionForm.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuestionForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _ConversationHistory__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ConversationHistory */ \"(app-pages-browser)/./src/components/ConversationHistory.tsx\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _lib_formSchemas__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/formSchemas */ \"(app-pages-browser)/./src/lib/formSchemas.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction QuestionForm(param) {\n    let { documentosSeleccionados } = param;\n    _s();\n    const [mensajes, setMensajes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [conversacionActualId, setConversacionActualId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mostrarHistorial, setMostrarHistorial] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [guardandoConversacion, setGuardandoConversacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cargandoConversacionActiva, setCargandoConversacionActiva] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { register, handleSubmit: handleSubmitForm, formState: { errors }, reset, setValue// <-- Añadido para sincronizar documentos\n     } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(_lib_formSchemas__WEBPACK_IMPORTED_MODULE_5__.preguntaFormSchema),\n        defaultValues: {\n            pregunta: '',\n            documentos: documentosSeleccionados\n        }\n    });\n    // Sincronizar documentos seleccionados con el formulario, asegurando tipos correctos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionForm.useEffect\": ()=>{\n            const documentosValidados = documentosSeleccionados.map({\n                \"QuestionForm.useEffect.documentosValidados\": (doc)=>({\n                        ...doc,\n                        numero_tema: doc.numero_tema !== undefined && doc.numero_tema !== null ? Number(doc.numero_tema) : undefined\n                    })\n            }[\"QuestionForm.useEffect.documentosValidados\"]);\n            setValue('documentos', documentosValidados);\n        }\n    }[\"QuestionForm.useEffect\"], [\n        documentosSeleccionados,\n        setValue\n    ]);\n    // Efecto para cargar la conversación activa al iniciar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionForm.useEffect\": ()=>{\n            const cargarConversacionActiva = {\n                \"QuestionForm.useEffect.cargarConversacionActiva\": async ()=>{\n                    setCargandoConversacionActiva(true);\n                    try {\n                        const conversacionActiva = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerConversacionActiva)();\n                        if (conversacionActiva) {\n                            console.log('Conversación activa encontrada:', conversacionActiva.id);\n                            setConversacionActualId(conversacionActiva.id);\n                            await cargarConversacion(conversacionActiva.id);\n                        } else {\n                            console.log('No hay conversación activa - esto es normal para usuarios nuevos');\n                            setMensajes([]);\n                            setConversacionActualId(null);\n                        }\n                    } catch (error) {\n                        console.warn('No se pudo cargar la conversación activa (esto es normal para usuarios nuevos):', error);\n                        // No mostrar error al usuario, simplemente inicializar sin conversación\n                        setMensajes([]);\n                        setConversacionActualId(null);\n                    } finally{\n                        setCargandoConversacionActiva(false);\n                    }\n                }\n            }[\"QuestionForm.useEffect.cargarConversacionActiva\"];\n            cargarConversacionActiva();\n        }\n    }[\"QuestionForm.useEffect\"], []);\n    // Efecto para hacer scroll al último mensaje cuando se añade uno nuevo\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionForm.useEffect\": ()=>{\n            if (chatContainerRef.current) {\n                chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n            }\n        }\n    }[\"QuestionForm.useEffect\"], [\n        mensajes\n    ]);\n    // Función para cargar una conversación desde Supabase\n    const cargarConversacion = async (conversacionId)=>{\n        try {\n            setIsLoading(true);\n            // Activar la conversación seleccionada\n            await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.activarConversacion)(conversacionId);\n            // Obtener los mensajes de la conversación\n            const mensajesDB = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerMensajesPorConversacionId)(conversacionId);\n            // Convertir los mensajes de la base de datos al formato local\n            const mensajesFormateados = mensajesDB.map((msg)=>({\n                    id: msg.id,\n                    tipo: msg.tipo,\n                    contenido: msg.contenido,\n                    timestamp: new Date(msg.timestamp)\n                }));\n            // Actualizar el estado\n            setMensajes(mensajesFormateados);\n            setConversacionActualId(conversacionId);\n            setError('');\n            console.log(\"Conversaci\\xf3n \".concat(conversacionId, \" cargada y activada\"));\n        } catch (error) {\n            console.error('Error al cargar la conversación:', error);\n            setError('No se pudo cargar la conversación');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Función para guardar un mensaje en Supabase\n    const guardarMensajeEnDB = async (mensaje)=>{\n        try {\n            setGuardandoConversacion(true);\n            // Si no hay una conversación actual, crear una nueva\n            if (!conversacionActualId) {\n                // Solo crear una nueva conversación si es el primer mensaje del usuario\n                if (mensaje.tipo === 'usuario') {\n                    // Crear un título basado en la primera pregunta\n                    const titulo = \"Conversaci\\xf3n: \".concat(mensaje.contenido.substring(0, 50)).concat(mensaje.contenido.length > 50 ? '...' : '');\n                    // Crear una nueva conversación y marcarla como activa\n                    const nuevoId = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.crearConversacion)(titulo, true);\n                    if (!nuevoId) {\n                        throw new Error('No se pudo crear la conversación');\n                    }\n                    console.log(\"Nueva conversaci\\xf3n creada y activada: \".concat(nuevoId));\n                    // Guardar el ID de la conversación para futuros mensajes\n                    setConversacionActualId(nuevoId);\n                    // Guardar el mensaje en la nueva conversación\n                    await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.guardarMensaje)({\n                        conversacion_id: nuevoId,\n                        tipo: mensaje.tipo,\n                        contenido: mensaje.contenido\n                    });\n                } else {\n                    // Si es un mensaje de la IA pero no hay conversación actual,\n                    // algo salió mal. Intentar recuperar creando una nueva conversación.\n                    console.warn('No hay conversación actual para guardar el mensaje de la IA. Creando una nueva.');\n                    const titulo = 'Nueva conversación';\n                    const nuevoId = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.crearConversacion)(titulo, true);\n                    if (!nuevoId) {\n                        throw new Error('No se pudo crear la conversación');\n                    }\n                    console.log(\"Nueva conversaci\\xf3n de recuperaci\\xf3n creada: \".concat(nuevoId));\n                    setConversacionActualId(nuevoId);\n                    // Guardar el mensaje en la nueva conversación\n                    await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.guardarMensaje)({\n                        conversacion_id: nuevoId,\n                        tipo: mensaje.tipo,\n                        contenido: mensaje.contenido\n                    });\n                }\n            } else {\n                // Verificar que la conversación actual sigue siendo la activa\n                const conversacionActiva = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerConversacionActiva)();\n                if (!conversacionActiva || conversacionActiva.id !== conversacionActualId) {\n                    // Si la conversación actual no es la activa, activarla\n                    console.log(\"Reactivando conversaci\\xf3n: \".concat(conversacionActualId));\n                    await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.activarConversacion)(conversacionActualId);\n                }\n                // Guardar el mensaje en la conversación existente\n                await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.guardarMensaje)({\n                    conversacion_id: conversacionActualId,\n                    tipo: mensaje.tipo,\n                    contenido: mensaje.contenido\n                });\n            }\n        } catch (error) {\n            console.error('Error al guardar el mensaje:', error);\n        // No mostramos error al usuario para no interrumpir la experiencia\n        } finally{\n            setGuardandoConversacion(false);\n        }\n    };\n    // Función para iniciar una nueva conversación\n    const iniciarNuevaConversacion = async ()=>{\n        try {\n            setIsLoading(true);\n            // Desactivar todas las conversaciones en la base de datos\n            await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.desactivarTodasLasConversaciones)();\n            // Limpiar los mensajes actuales\n            setMensajes([]);\n            // Establecer el ID de conversación a null para que se cree una nueva en el próximo mensaje\n            setConversacionActualId(null);\n            setError('');\n            console.log('Nueva conversación iniciada. El próximo mensaje creará una nueva conversación en la base de datos.');\n        } catch (error) {\n            console.error('Error al iniciar nueva conversación:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Cambia handleSubmit para usar React Hook Form\n    const onSubmit = async (data)=>{\n        console.log('🚀 Formulario enviado con datos:', data);\n        console.log('📄 Documentos seleccionados:', documentosSeleccionados);\n        setIsLoading(true);\n        setError('');\n        // Añadir la pregunta del usuario al historial\n        const preguntaUsuario = {\n            tipo: 'usuario',\n            contenido: data.pregunta,\n            timestamp: new Date()\n        };\n        setMensajes((prevMensajes)=>[\n                ...prevMensajes,\n                preguntaUsuario\n            ]);\n        setIsLoading(true);\n        setError('');\n        // Limpiar el campo de pregunta después de enviarla\n        reset({\n            pregunta: '',\n            documentos: documentosSeleccionados\n        });\n        try {\n            // Guardar la pregunta del usuario en Supabase\n            await guardarMensajeEnDB(preguntaUsuario);\n            // Pasar los documentos completos a la función obtenerRespuestaIA\n            // No solo el contenido, sino también el título, categoría y número de tema\n            // Obtener respuesta de la IA\n            const response = await fetch('/api/gemini', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    pregunta: preguntaUsuario.contenido,\n                    documentos: data.documentos\n                })\n            });\n            const respuestaIA = await response.json();\n            let respuestaTexto = '';\n            if (respuestaIA.result) {\n                respuestaTexto = typeof respuestaIA.result === 'string' ? respuestaIA.result : JSON.stringify(respuestaIA.result);\n            } else if (respuestaIA.error) {\n                respuestaTexto = typeof respuestaIA.error === 'string' ? respuestaIA.error : JSON.stringify(respuestaIA.error);\n            } else {\n                respuestaTexto = 'Error desconocido al obtener respuesta de la IA.';\n            }\n            // Añadir la respuesta de la IA al historial\n            const mensajeIA = {\n                tipo: 'ia',\n                contenido: respuestaTexto,\n                timestamp: new Date()\n            };\n            setMensajes((prevMensajes)=>[\n                    ...prevMensajes,\n                    mensajeIA\n                ]);\n            // Guardar la respuesta de la IA en Supabase\n            await guardarMensajeEnDB(mensajeIA);\n            // Si es la primera pregunta, actualizar el título de la conversación con un título más descriptivo\n            if (mensajes.length === 0 && conversacionActualId) {\n                const tituloMejorado = \"Conversaci\\xf3n: \".concat(data.pregunta.substring(0, 50)).concat(data.pregunta.length > 50 ? '...' : '');\n                await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.actualizarConversacion)(conversacionActualId, tituloMejorado);\n            }\n        } catch (error) {\n            console.error('Error al obtener respuesta:', error);\n            // Determinar el tipo de error y mostrar un mensaje más específico\n            let mensajeError = 'Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, inténtalo de nuevo.';\n            if (error instanceof Error) {\n                if (error.message.includes('API key')) {\n                    mensajeError = 'Error de configuración: La clave de API de Gemini no está configurada correctamente.';\n                } else if (error.message.includes('network') || error.message.includes('fetch')) {\n                    mensajeError = 'Error de conexión: No se pudo conectar con el servicio de IA. Verifica tu conexión a internet.';\n                } else if (error.message.includes('quota') || error.message.includes('limit')) {\n                    mensajeError = 'Se ha alcanzado el límite de uso del servicio de IA. Inténtalo más tarde.';\n                } else {\n                    mensajeError = \"Error: \".concat(error.message);\n                }\n            }\n            setError(mensajeError);\n            // Añadir mensaje de error como respuesta de la IA\n            const mensajeErrorIA = {\n                tipo: 'ia',\n                contenido: mensajeError,\n                timestamp: new Date()\n            };\n            setMensajes((prevMensajes)=>[\n                    ...prevMensajes,\n                    mensajeErrorIA\n                ]);\n            // Intentar guardar el mensaje de error en Supabase (sin fallar si no se puede)\n            try {\n                await guardarMensajeEnDB(mensajeErrorIA);\n            } catch (dbError) {\n                console.error('Error al guardar mensaje de error en DB:', dbError);\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Formatear la fecha para mostrarla en el chat\n    const formatearFecha = (fecha)=>{\n        return fecha.toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-6 flex flex-col h-[600px]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>setMostrarHistorial(!mostrarHistorial),\n                        className: \"bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded inline-flex items-center\",\n                        children: mostrarHistorial ? 'Ocultar historial' : 'Ver historial de conversaciones'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: iniciarNuevaConversacion,\n                        className: \"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded inline-flex items-center\",\n                        disabled: isLoading,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"h-5 w-5 mr-2\",\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 11\n                            }, this),\n                            \"Nueva conversaci\\xf3n\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                lineNumber: 348,\n                columnNumber: 7\n            }, this),\n            mostrarHistorial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConversationHistory__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onSelectConversation: cargarConversacion,\n                conversacionActualId: conversacionActualId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                lineNumber: 372,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatContainerRef,\n                className: \"flex-grow overflow-y-auto mb-4 p-4 border rounded-lg bg-gray-50\",\n                style: {\n                    height: 'calc(100% - 180px)'\n                },\n                children: mensajes.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full text-gray-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Selecciona documentos y haz una pregunta para comenzar la conversaci\\xf3n.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                    lineNumber: 385,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        mensajes.map((mensaje, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex \".concat(mensaje.tipo === 'usuario' ? 'justify-end' : 'justify-start'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-[80%] p-3 rounded-lg \".concat(mensaje.tipo === 'usuario' ? 'bg-blue-500 text-white rounded-br-none' : 'bg-white border border-gray-300 rounded-bl-none'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"whitespace-pre-wrap\",\n                                            children: mensaje.contenido\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs mt-1 text-right \".concat(mensaje.tipo === 'usuario' ? 'text-blue-100' : 'text-gray-500'),\n                                            children: formatearFecha(mensaje.timestamp)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 17\n                                }, this)\n                            }, mensaje.id || index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 15\n                            }, this)),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-3 rounded-lg border border-gray-300 rounded-bl-none\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-bounce h-2 w-2 bg-gray-500 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-bounce h-2 w-2 bg-gray-500 rounded-full\",\n                                            style: {\n                                                animationDelay: '0.2s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-bounce h-2 w-2 bg-gray-500 rounded-full\",\n                                            style: {\n                                                animationDelay: '0.4s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 15\n                        }, this),\n                        guardandoConversacion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 text-center py-1\",\n                            children: \"Guardando conversaci\\xf3n...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                            lineNumber: 427,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                    lineNumber: 389,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                lineNumber: 379,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmitForm(onSubmit),\n                className: \"mt-auto\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-500 text-sm mb-2\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 438,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-grow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"pregunta\",\n                                        className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                        rows: 2,\n                                        ...register('pregunta'),\n                                        placeholder: \"Escribe tu pregunta sobre los documentos seleccionados...\",\n                                        disabled: isLoading,\n                                        onKeyDown: (e)=>{\n                                            if (e.key === 'Enter' && !e.shiftKey) {\n                                                e.preventDefault();\n                                                handleSubmitForm(onSubmit)(); // Ejecutar la función devuelta\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.pregunta && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-500 text-xs mt-1\",\n                                        children: errors.pregunta.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: \"Presiona Enter para enviar, Shift+Enter para nueva l\\xednea\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-full h-10 w-10 flex items-center justify-center focus:outline-none focus:shadow-outline disabled:opacity-50 disabled:cursor-not-allowed\",\n                                disabled: isLoading || documentosSeleccionados.length === 0,\n                                title: documentosSeleccionados.length === 0 ? 'Selecciona al menos un documento para hacer una pregunta' : 'Enviar pregunta',\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin h-5 w-5 text-white\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 008-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-5 w-5 text-white\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                lineNumber: 436,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n        lineNumber: 346,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionForm, \"tfR+tk0F7qhXsixIRdOvGhuwjCA=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm\n    ];\n});\n_c = QuestionForm;\nvar _c;\n$RefreshReg$(_c, \"QuestionForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/QuestionForm.tsx\n"));

/***/ })

});