"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@floating-ui";
exports.ids = ["vendor-chunks/@floating-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@floating-ui/core/dist/floating-ui.core.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@floating-ui/core/dist/floating-ui.core.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrow: () => (/* binding */ arrow),\n/* harmony export */   autoPlacement: () => (/* binding */ autoPlacement),\n/* harmony export */   computePosition: () => (/* binding */ computePosition),\n/* harmony export */   detectOverflow: () => (/* binding */ detectOverflow),\n/* harmony export */   flip: () => (/* binding */ flip),\n/* harmony export */   hide: () => (/* binding */ hide),\n/* harmony export */   inline: () => (/* binding */ inline),\n/* harmony export */   limitShift: () => (/* binding */ limitShift),\n/* harmony export */   offset: () => (/* binding */ offset),\n/* harmony export */   rectToClientRect: () => (/* reexport safe */ _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect),\n/* harmony export */   shift: () => (/* binding */ shift),\n/* harmony export */   size: () => (/* binding */ size)\n/* harmony export */ });\n/* harmony import */ var _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @floating-ui/utils */ \"(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs\");\nconst _excluded = [\"crossAxis\", \"alignment\", \"allowedPlacements\", \"autoAlignment\"],\n  _excluded2 = [\"mainAxis\", \"crossAxis\", \"fallbackPlacements\", \"fallbackStrategy\", \"fallbackAxisSideDirection\", \"flipAlignment\"],\n  _excluded3 = [\"strategy\"],\n  _excluded4 = [\"mainAxis\", \"crossAxis\", \"limiter\"],\n  _excluded5 = [\"apply\"];\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement);\n  const alignmentAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentAxis)(placement);\n  const alignLength = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAxisLength)(alignmentAxis);\n  const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch ((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = _objectSpread(_objectSpread({}, middlewareData), {}, {\n      [name]: _objectSpread(_objectSpread({}, middlewareData[name]), data)\n    });\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n  const paddingObject = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getPaddingObject)(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getPaddingObject)(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentAxis)(placement);\n    const length = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAxisLength)(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.clamp)(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: _objectSpread({\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset\n      }, shouldAddOffset && {\n        alignmentOffset\n      }),\n      reset: shouldAddOffset\n    };\n  }\n});\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) === alignment), ...allowedPlacements.filter(placement => (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) !== alignment)] : allowedPlacements.filter(placement => (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) === alignment || (autoAlignment ? (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAlignmentPlacement)(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const _evaluate = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state),\n        {\n          crossAxis = false,\n          alignment,\n          allowedPlacements = _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.placements,\n          autoAlignment = true\n        } = _evaluate,\n        detectOverflowOptions = _objectWithoutProperties(_evaluate, _excluded);\n      const placements$1 = alignment !== undefined || allowedPlacements === _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentSides)(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const _evaluate2 = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state),\n        {\n          mainAxis: checkMainAxis = true,\n          crossAxis: checkCrossAxis = true,\n          fallbackPlacements: specifiedFallbackPlacements,\n          fallbackStrategy = 'bestFit',\n          fallbackAxisSideDirection = 'none',\n          flipAlignment = true\n        } = _evaluate2,\n        detectOverflowOptions = _objectWithoutProperties(_evaluate2, _excluded2);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n      const initialSideAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(initialPlacement);\n      const isBasePlacement = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositePlacement)(initialPlacement)] : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getExpandedPlacements)(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAxisPlacements)(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentSides)(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          var _overflowsData$;\n          const ignoreCrossAxisOverflow = checkCrossAxis === 'alignment' ? initialSideAxis !== (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(nextPlacement) : false;\n          const hasInitialMainAxisOverflow = ((_overflowsData$ = overflowsData[0]) == null ? void 0 : _overflowsData$.overflows[0]) > 0;\n          if (!ignoreCrossAxisOverflow || hasInitialMainAxisOverflow) {\n            // Try next placement and re-run the lifecycle.\n            return {\n              data: {\n                index: nextIndex,\n                overflows: overflowsData\n              },\n              reset: {\n                placement: nextPlacement\n              }\n            };\n          }\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const _evaluate3 = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state),\n        {\n          strategy = 'referenceHidden'\n        } = _evaluate3,\n        detectOverflowOptions = _objectWithoutProperties(_evaluate3, _excluded3);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, _objectSpread(_objectSpread({}, detectOverflowOptions), {}, {\n              elementContext: 'reference'\n            }));\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, _objectSpread(_objectSpread({}, detectOverflowOptions), {}, {\n              altBoundary: true\n            }));\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\nfunction getBoundingRect(rects) {\n  const minX = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(...rects.map(rect => rect.left));\n  const minY = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(...rects.map(rect => rect.top));\n  const maxX = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(...rects.map(rect => rect.right));\n  const maxY = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(getBoundingRect(nativeClientRects));\n      const paddingObject = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getPaddingObject)(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if ((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement) === 'left';\n          const maxRight = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(...clientRects.map(rect => rect.right));\n          const minLeft = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n  const alignment = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement);\n  const isVertical = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement) === 'y';\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: _objectSpread(_objectSpread({}, diffCoords), {}, {\n          placement\n        })\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const _evaluate4 = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state),\n        {\n          mainAxis: checkMainAxis = true,\n          crossAxis: checkCrossAxis = false,\n          limiter = {\n            fn: _ref => {\n              let {\n                x,\n                y\n              } = _ref;\n              return {\n                x,\n                y\n              };\n            }\n          }\n        } = _evaluate4,\n        detectOverflowOptions = _objectWithoutProperties(_evaluate4, _excluded4);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement));\n      const mainAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAxis)(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.clamp)(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.clamp)(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn(_objectSpread(_objectSpread({}, state), {}, {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      }));\n      return _objectSpread(_objectSpread({}, limitedCoords), {}, {\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      });\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement);\n      const mainAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAxis)(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : _objectSpread({\n        mainAxis: 0,\n        crossAxis: 0\n      }, rawOffset);\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const _evaluate5 = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state),\n        {\n          apply = () => {}\n        } = _evaluate5,\n        detectOverflowOptions = _objectWithoutProperties(_evaluate5, _excluded5);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n      const alignment = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement);\n      const isYAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.left, 0);\n        const xMax = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.right, 0);\n        const yMin = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.top, 0);\n        const yMax = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.top, overflow.bottom));\n        }\n      }\n      await apply(_objectSpread(_objectSpread({}, state), {}, {\n        availableWidth,\n        availableHeight\n      }));\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGZsb2F0aW5nLXVpL2NvcmUvZGlzdC9mbG9hdGluZy11aS5jb3JlLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXNVO0FBQ2hSO0FBRXRELFNBQVNtQiwwQkFBMEJBLENBQUNDLElBQUksRUFBRUMsU0FBUyxFQUFFQyxHQUFHLEVBQUU7RUFDeEQsSUFBSTtJQUNGQyxTQUFTO0lBQ1RDO0VBQ0YsQ0FBQyxHQUFHSixJQUFJO0VBQ1IsTUFBTUssUUFBUSxHQUFHekIsK0RBQVcsQ0FBQ3FCLFNBQVMsQ0FBQztFQUN2QyxNQUFNSyxhQUFhLEdBQUd6QixvRUFBZ0IsQ0FBQ29CLFNBQVMsQ0FBQztFQUNqRCxNQUFNTSxXQUFXLEdBQUd6QixpRUFBYSxDQUFDd0IsYUFBYSxDQUFDO0VBQ2hELE1BQU1FLElBQUksR0FBR3pCLDJEQUFPLENBQUNrQixTQUFTLENBQUM7RUFDL0IsTUFBTVEsVUFBVSxHQUFHSixRQUFRLEtBQUssR0FBRztFQUNuQyxNQUFNSyxPQUFPLEdBQUdQLFNBQVMsQ0FBQ1EsQ0FBQyxHQUFHUixTQUFTLENBQUNTLEtBQUssR0FBRyxDQUFDLEdBQUdSLFFBQVEsQ0FBQ1EsS0FBSyxHQUFHLENBQUM7RUFDdEUsTUFBTUMsT0FBTyxHQUFHVixTQUFTLENBQUNXLENBQUMsR0FBR1gsU0FBUyxDQUFDWSxNQUFNLEdBQUcsQ0FBQyxHQUFHWCxRQUFRLENBQUNXLE1BQU0sR0FBRyxDQUFDO0VBQ3hFLE1BQU1DLFdBQVcsR0FBR2IsU0FBUyxDQUFDSSxXQUFXLENBQUMsR0FBRyxDQUFDLEdBQUdILFFBQVEsQ0FBQ0csV0FBVyxDQUFDLEdBQUcsQ0FBQztFQUMxRSxJQUFJVSxNQUFNO0VBQ1YsUUFBUVQsSUFBSTtJQUNWLEtBQUssS0FBSztNQUNSUyxNQUFNLEdBQUc7UUFDUE4sQ0FBQyxFQUFFRCxPQUFPO1FBQ1ZJLENBQUMsRUFBRVgsU0FBUyxDQUFDVyxDQUFDLEdBQUdWLFFBQVEsQ0FBQ1c7TUFDNUIsQ0FBQztNQUNEO0lBQ0YsS0FBSyxRQUFRO01BQ1hFLE1BQU0sR0FBRztRQUNQTixDQUFDLEVBQUVELE9BQU87UUFDVkksQ0FBQyxFQUFFWCxTQUFTLENBQUNXLENBQUMsR0FBR1gsU0FBUyxDQUFDWTtNQUM3QixDQUFDO01BQ0Q7SUFDRixLQUFLLE9BQU87TUFDVkUsTUFBTSxHQUFHO1FBQ1BOLENBQUMsRUFBRVIsU0FBUyxDQUFDUSxDQUFDLEdBQUdSLFNBQVMsQ0FBQ1MsS0FBSztRQUNoQ0UsQ0FBQyxFQUFFRDtNQUNMLENBQUM7TUFDRDtJQUNGLEtBQUssTUFBTTtNQUNUSSxNQUFNLEdBQUc7UUFDUE4sQ0FBQyxFQUFFUixTQUFTLENBQUNRLENBQUMsR0FBR1AsUUFBUSxDQUFDUSxLQUFLO1FBQy9CRSxDQUFDLEVBQUVEO01BQ0wsQ0FBQztNQUNEO0lBQ0Y7TUFDRUksTUFBTSxHQUFHO1FBQ1BOLENBQUMsRUFBRVIsU0FBUyxDQUFDUSxDQUFDO1FBQ2RHLENBQUMsRUFBRVgsU0FBUyxDQUFDVztNQUNmLENBQUM7RUFDTDtFQUNBLFFBQVE5QixnRUFBWSxDQUFDaUIsU0FBUyxDQUFDO0lBQzdCLEtBQUssT0FBTztNQUNWZ0IsTUFBTSxDQUFDWCxhQUFhLENBQUMsSUFBSVUsV0FBVyxJQUFJZCxHQUFHLElBQUlPLFVBQVUsR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUM7TUFDbkU7SUFDRixLQUFLLEtBQUs7TUFDUlEsTUFBTSxDQUFDWCxhQUFhLENBQUMsSUFBSVUsV0FBVyxJQUFJZCxHQUFHLElBQUlPLFVBQVUsR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUM7TUFDbkU7RUFDSjtFQUNBLE9BQU9RLE1BQU07QUFDZjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU1DLGVBQWUsR0FBRyxNQUFBQSxDQUFPZixTQUFTLEVBQUVDLFFBQVEsRUFBRWUsTUFBTSxLQUFLO0VBQzdELE1BQU07SUFDSmxCLFNBQVMsR0FBRyxRQUFRO0lBQ3BCbUIsUUFBUSxHQUFHLFVBQVU7SUFDckJDLFVBQVUsR0FBRyxFQUFFO0lBQ2ZDO0VBQ0YsQ0FBQyxHQUFHSCxNQUFNO0VBQ1YsTUFBTUksZUFBZSxHQUFHRixVQUFVLENBQUNHLE1BQU0sQ0FBQ0MsT0FBTyxDQUFDO0VBQ2xELE1BQU12QixHQUFHLEdBQUcsT0FBT29CLFFBQVEsQ0FBQ0ksS0FBSyxJQUFJLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR0osUUFBUSxDQUFDSSxLQUFLLENBQUN0QixRQUFRLENBQUMsQ0FBQztFQUM5RSxJQUFJdUIsS0FBSyxHQUFHLE1BQU1MLFFBQVEsQ0FBQ00sZUFBZSxDQUFDO0lBQ3pDekIsU0FBUztJQUNUQyxRQUFRO0lBQ1JnQjtFQUNGLENBQUMsQ0FBQztFQUNGLElBQUk7SUFDRlQsQ0FBQztJQUNERztFQUNGLENBQUMsR0FBR2YsMEJBQTBCLENBQUM0QixLQUFLLEVBQUUxQixTQUFTLEVBQUVDLEdBQUcsQ0FBQztFQUNyRCxJQUFJMkIsaUJBQWlCLEdBQUc1QixTQUFTO0VBQ2pDLElBQUk2QixjQUFjLEdBQUcsQ0FBQyxDQUFDO0VBQ3ZCLElBQUlDLFVBQVUsR0FBRyxDQUFDO0VBQ2xCLEtBQUssSUFBSUMsQ0FBQyxHQUFHLENBQUMsRUFBRUEsQ0FBQyxHQUFHVCxlQUFlLENBQUNVLE1BQU0sRUFBRUQsQ0FBQyxFQUFFLEVBQUU7SUFDL0MsTUFBTTtNQUNKRSxJQUFJO01BQ0pDO0lBQ0YsQ0FBQyxHQUFHWixlQUFlLENBQUNTLENBQUMsQ0FBQztJQUN0QixNQUFNO01BQ0pyQixDQUFDLEVBQUV5QixLQUFLO01BQ1J0QixDQUFDLEVBQUV1QixLQUFLO01BQ1JDLElBQUk7TUFDSkM7SUFDRixDQUFDLEdBQUcsTUFBTUosRUFBRSxDQUFDO01BQ1h4QixDQUFDO01BQ0RHLENBQUM7TUFDRDBCLGdCQUFnQixFQUFFdkMsU0FBUztNQUMzQkEsU0FBUyxFQUFFNEIsaUJBQWlCO01BQzVCVCxRQUFRO01BQ1JVLGNBQWM7TUFDZEgsS0FBSztNQUNMTCxRQUFRO01BQ1JtQixRQUFRLEVBQUU7UUFDUnRDLFNBQVM7UUFDVEM7TUFDRjtJQUNGLENBQUMsQ0FBQztJQUNGTyxDQUFDLEdBQUd5QixLQUFLLElBQUksSUFBSSxHQUFHQSxLQUFLLEdBQUd6QixDQUFDO0lBQzdCRyxDQUFDLEdBQUd1QixLQUFLLElBQUksSUFBSSxHQUFHQSxLQUFLLEdBQUd2QixDQUFDO0lBQzdCZ0IsY0FBYyxHQUFBWSxhQUFBLENBQUFBLGFBQUEsS0FDVFosY0FBYztNQUNqQixDQUFDSSxJQUFJLEdBQUFRLGFBQUEsQ0FBQUEsYUFBQSxLQUNBWixjQUFjLENBQUNJLElBQUksQ0FBQyxHQUNwQkksSUFBSTtJQUNSLEVBQ0Y7SUFDRCxJQUFJQyxLQUFLLElBQUlSLFVBQVUsSUFBSSxFQUFFLEVBQUU7TUFDN0JBLFVBQVUsRUFBRTtNQUNaLElBQUksT0FBT1EsS0FBSyxLQUFLLFFBQVEsRUFBRTtRQUM3QixJQUFJQSxLQUFLLENBQUN0QyxTQUFTLEVBQUU7VUFDbkI0QixpQkFBaUIsR0FBR1UsS0FBSyxDQUFDdEMsU0FBUztRQUNyQztRQUNBLElBQUlzQyxLQUFLLENBQUNaLEtBQUssRUFBRTtVQUNmQSxLQUFLLEdBQUdZLEtBQUssQ0FBQ1osS0FBSyxLQUFLLElBQUksR0FBRyxNQUFNTCxRQUFRLENBQUNNLGVBQWUsQ0FBQztZQUM1RHpCLFNBQVM7WUFDVEMsUUFBUTtZQUNSZ0I7VUFDRixDQUFDLENBQUMsR0FBR21CLEtBQUssQ0FBQ1osS0FBSztRQUNsQjtRQUNBLENBQUM7VUFDQ2hCLENBQUM7VUFDREc7UUFDRixDQUFDLEdBQUdmLDBCQUEwQixDQUFDNEIsS0FBSyxFQUFFRSxpQkFBaUIsRUFBRTNCLEdBQUcsQ0FBQztNQUMvRDtNQUNBOEIsQ0FBQyxHQUFHLENBQUMsQ0FBQztJQUNSO0VBQ0Y7RUFDQSxPQUFPO0lBQ0xyQixDQUFDO0lBQ0RHLENBQUM7SUFDRGIsU0FBUyxFQUFFNEIsaUJBQWlCO0lBQzVCVCxRQUFRO0lBQ1JVO0VBQ0YsQ0FBQztBQUNILENBQUM7O0FBRUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWVhLGNBQWNBLENBQUNDLEtBQUssRUFBRUMsT0FBTyxFQUFFO0VBQzVDLElBQUlDLHFCQUFxQjtFQUN6QixJQUFJRCxPQUFPLEtBQUssS0FBSyxDQUFDLEVBQUU7SUFDdEJBLE9BQU8sR0FBRyxDQUFDLENBQUM7RUFDZDtFQUNBLE1BQU07SUFDSmxDLENBQUM7SUFDREcsQ0FBQztJQUNEUSxRQUFRO0lBQ1JLLEtBQUs7SUFDTGMsUUFBUTtJQUNSckI7RUFDRixDQUFDLEdBQUd3QixLQUFLO0VBQ1QsTUFBTTtJQUNKRyxRQUFRLEdBQUcsbUJBQW1CO0lBQzlCQyxZQUFZLEdBQUcsVUFBVTtJQUN6QkMsY0FBYyxHQUFHLFVBQVU7SUFDM0JDLFdBQVcsR0FBRyxLQUFLO0lBQ25CQyxPQUFPLEdBQUc7RUFDWixDQUFDLEdBQUdsRSw0REFBUSxDQUFDNEQsT0FBTyxFQUFFRCxLQUFLLENBQUM7RUFDNUIsTUFBTVEsYUFBYSxHQUFHbEUsb0VBQWdCLENBQUNpRSxPQUFPLENBQUM7RUFDL0MsTUFBTUUsVUFBVSxHQUFHSixjQUFjLEtBQUssVUFBVSxHQUFHLFdBQVcsR0FBRyxVQUFVO0VBQzNFLE1BQU1LLE9BQU8sR0FBR2IsUUFBUSxDQUFDUyxXQUFXLEdBQUdHLFVBQVUsR0FBR0osY0FBYyxDQUFDO0VBQ25FLE1BQU1NLGtCQUFrQixHQUFHcEUsb0VBQWdCLENBQUMsTUFBTW1DLFFBQVEsQ0FBQ2tDLGVBQWUsQ0FBQztJQUN6RUYsT0FBTyxFQUFFLENBQUMsQ0FBQ1IscUJBQXFCLEdBQUcsT0FBT3hCLFFBQVEsQ0FBQ21DLFNBQVMsSUFBSSxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUduQyxRQUFRLENBQUNtQyxTQUFTLENBQUNILE9BQU8sQ0FBQyxDQUFDLEtBQUssSUFBSSxHQUFHUixxQkFBcUIsR0FBRyxJQUFJLElBQUlRLE9BQU8sR0FBR0EsT0FBTyxDQUFDSSxjQUFjLEtBQUssT0FBT3BDLFFBQVEsQ0FBQ3FDLGtCQUFrQixJQUFJLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR3JDLFFBQVEsQ0FBQ3FDLGtCQUFrQixDQUFDbEIsUUFBUSxDQUFDckMsUUFBUSxDQUFDLENBQUMsQ0FBQztJQUNuUzJDLFFBQVE7SUFDUkMsWUFBWTtJQUNaNUI7RUFDRixDQUFDLENBQUMsQ0FBQztFQUNILE1BQU13QyxJQUFJLEdBQUdYLGNBQWMsS0FBSyxVQUFVLEdBQUc7SUFDM0N0QyxDQUFDO0lBQ0RHLENBQUM7SUFDREYsS0FBSyxFQUFFZSxLQUFLLENBQUN2QixRQUFRLENBQUNRLEtBQUs7SUFDM0JHLE1BQU0sRUFBRVksS0FBSyxDQUFDdkIsUUFBUSxDQUFDVztFQUN6QixDQUFDLEdBQUdZLEtBQUssQ0FBQ3hCLFNBQVM7RUFDbkIsTUFBTTBELFlBQVksR0FBRyxPQUFPdkMsUUFBUSxDQUFDd0MsZUFBZSxJQUFJLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR3hDLFFBQVEsQ0FBQ3dDLGVBQWUsQ0FBQ3JCLFFBQVEsQ0FBQ3JDLFFBQVEsQ0FBQyxDQUFDO0VBQ3BILE1BQU0yRCxXQUFXLEdBQUcsQ0FBQyxPQUFPekMsUUFBUSxDQUFDbUMsU0FBUyxJQUFJLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR25DLFFBQVEsQ0FBQ21DLFNBQVMsQ0FBQ0ksWUFBWSxDQUFDLENBQUMsSUFBSSxDQUFDLE9BQU92QyxRQUFRLENBQUMwQyxRQUFRLElBQUksSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHMUMsUUFBUSxDQUFDMEMsUUFBUSxDQUFDSCxZQUFZLENBQUMsQ0FBQyxLQUFLO0lBQ3ZMbEQsQ0FBQyxFQUFFLENBQUM7SUFDSkcsQ0FBQyxFQUFFO0VBQ0wsQ0FBQyxHQUFHO0lBQ0ZILENBQUMsRUFBRSxDQUFDO0lBQ0pHLENBQUMsRUFBRTtFQUNMLENBQUM7RUFDRCxNQUFNbUQsaUJBQWlCLEdBQUc5RSxvRUFBZ0IsQ0FBQ21DLFFBQVEsQ0FBQzRDLHFEQUFxRCxHQUFHLE1BQU01QyxRQUFRLENBQUM0QyxxREFBcUQsQ0FBQztJQUMvS3pCLFFBQVE7SUFDUm1CLElBQUk7SUFDSkMsWUFBWTtJQUNaekM7RUFDRixDQUFDLENBQUMsR0FBR3dDLElBQUksQ0FBQztFQUNWLE9BQU87SUFDTE8sR0FBRyxFQUFFLENBQUNaLGtCQUFrQixDQUFDWSxHQUFHLEdBQUdGLGlCQUFpQixDQUFDRSxHQUFHLEdBQUdmLGFBQWEsQ0FBQ2UsR0FBRyxJQUFJSixXQUFXLENBQUNqRCxDQUFDO0lBQ3pGc0QsTUFBTSxFQUFFLENBQUNILGlCQUFpQixDQUFDRyxNQUFNLEdBQUdiLGtCQUFrQixDQUFDYSxNQUFNLEdBQUdoQixhQUFhLENBQUNnQixNQUFNLElBQUlMLFdBQVcsQ0FBQ2pELENBQUM7SUFDckd1RCxJQUFJLEVBQUUsQ0FBQ2Qsa0JBQWtCLENBQUNjLElBQUksR0FBR0osaUJBQWlCLENBQUNJLElBQUksR0FBR2pCLGFBQWEsQ0FBQ2lCLElBQUksSUFBSU4sV0FBVyxDQUFDcEQsQ0FBQztJQUM3RjJELEtBQUssRUFBRSxDQUFDTCxpQkFBaUIsQ0FBQ0ssS0FBSyxHQUFHZixrQkFBa0IsQ0FBQ2UsS0FBSyxHQUFHbEIsYUFBYSxDQUFDa0IsS0FBSyxJQUFJUCxXQUFXLENBQUNwRDtFQUNsRyxDQUFDO0FBQ0g7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU00RCxLQUFLLEdBQUcxQixPQUFPLEtBQUs7RUFDeEJYLElBQUksRUFBRSxPQUFPO0VBQ2JXLE9BQU87RUFDUCxNQUFNVixFQUFFQSxDQUFDUyxLQUFLLEVBQUU7SUFDZCxNQUFNO01BQ0pqQyxDQUFDO01BQ0RHLENBQUM7TUFDRGIsU0FBUztNQUNUMEIsS0FBSztNQUNMTCxRQUFRO01BQ1JtQixRQUFRO01BQ1JYO0lBQ0YsQ0FBQyxHQUFHYyxLQUFLO0lBQ1Q7SUFDQSxNQUFNO01BQ0pVLE9BQU87TUFDUEgsT0FBTyxHQUFHO0lBQ1osQ0FBQyxHQUFHbEUsNERBQVEsQ0FBQzRELE9BQU8sRUFBRUQsS0FBSyxDQUFDLElBQUksQ0FBQyxDQUFDO0lBQ2xDLElBQUlVLE9BQU8sSUFBSSxJQUFJLEVBQUU7TUFDbkIsT0FBTyxDQUFDLENBQUM7SUFDWDtJQUNBLE1BQU1GLGFBQWEsR0FBR2xFLG9FQUFnQixDQUFDaUUsT0FBTyxDQUFDO0lBQy9DLE1BQU1sQyxNQUFNLEdBQUc7TUFDYk4sQ0FBQztNQUNERztJQUNGLENBQUM7SUFDRCxNQUFNMEQsSUFBSSxHQUFHM0Ysb0VBQWdCLENBQUNvQixTQUFTLENBQUM7SUFDeEMsTUFBTWdDLE1BQU0sR0FBR25ELGlFQUFhLENBQUMwRixJQUFJLENBQUM7SUFDbEMsTUFBTUMsZUFBZSxHQUFHLE1BQU1uRCxRQUFRLENBQUNvRCxhQUFhLENBQUNwQixPQUFPLENBQUM7SUFDN0QsTUFBTXFCLE9BQU8sR0FBR0gsSUFBSSxLQUFLLEdBQUc7SUFDNUIsTUFBTUksT0FBTyxHQUFHRCxPQUFPLEdBQUcsS0FBSyxHQUFHLE1BQU07SUFDeEMsTUFBTUUsT0FBTyxHQUFHRixPQUFPLEdBQUcsUUFBUSxHQUFHLE9BQU87SUFDNUMsTUFBTUcsVUFBVSxHQUFHSCxPQUFPLEdBQUcsY0FBYyxHQUFHLGFBQWE7SUFDM0QsTUFBTUksT0FBTyxHQUFHcEQsS0FBSyxDQUFDeEIsU0FBUyxDQUFDOEIsTUFBTSxDQUFDLEdBQUdOLEtBQUssQ0FBQ3hCLFNBQVMsQ0FBQ3FFLElBQUksQ0FBQyxHQUFHdkQsTUFBTSxDQUFDdUQsSUFBSSxDQUFDLEdBQUc3QyxLQUFLLENBQUN2QixRQUFRLENBQUM2QixNQUFNLENBQUM7SUFDdkcsTUFBTStDLFNBQVMsR0FBRy9ELE1BQU0sQ0FBQ3VELElBQUksQ0FBQyxHQUFHN0MsS0FBSyxDQUFDeEIsU0FBUyxDQUFDcUUsSUFBSSxDQUFDO0lBQ3RELE1BQU1TLGlCQUFpQixHQUFHLE9BQU8zRCxRQUFRLENBQUN3QyxlQUFlLElBQUksSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHeEMsUUFBUSxDQUFDd0MsZUFBZSxDQUFDUixPQUFPLENBQUMsQ0FBQztJQUMvRyxJQUFJNEIsVUFBVSxHQUFHRCxpQkFBaUIsR0FBR0EsaUJBQWlCLENBQUNILFVBQVUsQ0FBQyxHQUFHLENBQUM7O0lBRXRFO0lBQ0EsSUFBSSxDQUFDSSxVQUFVLElBQUksRUFBRSxPQUFPNUQsUUFBUSxDQUFDbUMsU0FBUyxJQUFJLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR25DLFFBQVEsQ0FBQ21DLFNBQVMsQ0FBQ3dCLGlCQUFpQixDQUFDLENBQUMsQ0FBQyxFQUFFO01BQ3pHQyxVQUFVLEdBQUd6QyxRQUFRLENBQUNyQyxRQUFRLENBQUMwRSxVQUFVLENBQUMsSUFBSW5ELEtBQUssQ0FBQ3ZCLFFBQVEsQ0FBQzZCLE1BQU0sQ0FBQztJQUN0RTtJQUNBLE1BQU1rRCxpQkFBaUIsR0FBR0osT0FBTyxHQUFHLENBQUMsR0FBR0MsU0FBUyxHQUFHLENBQUM7O0lBRXJEO0lBQ0E7SUFDQSxNQUFNSSxzQkFBc0IsR0FBR0YsVUFBVSxHQUFHLENBQUMsR0FBR1QsZUFBZSxDQUFDeEMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUM7SUFDL0UsTUFBTW9ELFVBQVUsR0FBR2pHLHVEQUFHLENBQUNnRSxhQUFhLENBQUN3QixPQUFPLENBQUMsRUFBRVEsc0JBQXNCLENBQUM7SUFDdEUsTUFBTUUsVUFBVSxHQUFHbEcsdURBQUcsQ0FBQ2dFLGFBQWEsQ0FBQ3lCLE9BQU8sQ0FBQyxFQUFFTyxzQkFBc0IsQ0FBQzs7SUFFdEU7SUFDQTtJQUNBLE1BQU1HLEtBQUssR0FBR0YsVUFBVTtJQUN4QixNQUFNeEYsR0FBRyxHQUFHcUYsVUFBVSxHQUFHVCxlQUFlLENBQUN4QyxNQUFNLENBQUMsR0FBR3FELFVBQVU7SUFDN0QsTUFBTUUsTUFBTSxHQUFHTixVQUFVLEdBQUcsQ0FBQyxHQUFHVCxlQUFlLENBQUN4QyxNQUFNLENBQUMsR0FBRyxDQUFDLEdBQUdrRCxpQkFBaUI7SUFDL0UsTUFBTU0sTUFBTSxHQUFHcEcseURBQUssQ0FBQ2tHLEtBQUssRUFBRUMsTUFBTSxFQUFFM0YsR0FBRyxDQUFDOztJQUV4QztJQUNBO0lBQ0E7SUFDQTtJQUNBLE1BQU02RixlQUFlLEdBQUcsQ0FBQzVELGNBQWMsQ0FBQ3lDLEtBQUssSUFBSXZGLGdFQUFZLENBQUNpQixTQUFTLENBQUMsSUFBSSxJQUFJLElBQUl1RixNQUFNLEtBQUtDLE1BQU0sSUFBSTlELEtBQUssQ0FBQ3hCLFNBQVMsQ0FBQzhCLE1BQU0sQ0FBQyxHQUFHLENBQUMsSUFBSXVELE1BQU0sR0FBR0QsS0FBSyxHQUFHRixVQUFVLEdBQUdDLFVBQVUsQ0FBQyxHQUFHYixlQUFlLENBQUN4QyxNQUFNLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQztJQUNuTixNQUFNMEQsZUFBZSxHQUFHRCxlQUFlLEdBQUdGLE1BQU0sR0FBR0QsS0FBSyxHQUFHQyxNQUFNLEdBQUdELEtBQUssR0FBR0MsTUFBTSxHQUFHM0YsR0FBRyxHQUFHLENBQUM7SUFDNUYsT0FBTztNQUNMLENBQUMyRSxJQUFJLEdBQUd2RCxNQUFNLENBQUN1RCxJQUFJLENBQUMsR0FBR21CLGVBQWU7TUFDdENyRCxJQUFJLEVBQUFJLGFBQUE7UUFDRixDQUFDOEIsSUFBSSxHQUFHaUIsTUFBTTtRQUNkRyxZQUFZLEVBQUVKLE1BQU0sR0FBR0MsTUFBTSxHQUFHRTtNQUFlLEdBQzNDRCxlQUFlLElBQUk7UUFDckJDO01BQ0YsQ0FBQyxDQUNGO01BQ0RwRCxLQUFLLEVBQUVtRDtJQUNULENBQUM7RUFDSDtBQUNGLENBQUMsQ0FBQztBQUVGLFNBQVNHLGdCQUFnQkEsQ0FBQ0MsU0FBUyxFQUFFQyxhQUFhLEVBQUVDLGlCQUFpQixFQUFFO0VBQ3JFLE1BQU1DLGtDQUFrQyxHQUFHSCxTQUFTLEdBQUcsQ0FBQyxHQUFHRSxpQkFBaUIsQ0FBQ3hFLE1BQU0sQ0FBQ3ZCLFNBQVMsSUFBSWpCLGdFQUFZLENBQUNpQixTQUFTLENBQUMsS0FBSzZGLFNBQVMsQ0FBQyxFQUFFLEdBQUdFLGlCQUFpQixDQUFDeEUsTUFBTSxDQUFDdkIsU0FBUyxJQUFJakIsZ0VBQVksQ0FBQ2lCLFNBQVMsQ0FBQyxLQUFLNkYsU0FBUyxDQUFDLENBQUMsR0FBR0UsaUJBQWlCLENBQUN4RSxNQUFNLENBQUN2QixTQUFTLElBQUlsQiwyREFBTyxDQUFDa0IsU0FBUyxDQUFDLEtBQUtBLFNBQVMsQ0FBQztFQUNuUyxPQUFPZ0csa0NBQWtDLENBQUN6RSxNQUFNLENBQUN2QixTQUFTLElBQUk7SUFDNUQsSUFBSTZGLFNBQVMsRUFBRTtNQUNiLE9BQU85RyxnRUFBWSxDQUFDaUIsU0FBUyxDQUFDLEtBQUs2RixTQUFTLEtBQUtDLGFBQWEsR0FBR3ZHLGlGQUE2QixDQUFDUyxTQUFTLENBQUMsS0FBS0EsU0FBUyxHQUFHLEtBQUssQ0FBQztJQUNsSTtJQUNBLE9BQU8sSUFBSTtFQUNiLENBQUMsQ0FBQztBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTWlHLGFBQWEsR0FBRyxTQUFBQSxDQUFVckQsT0FBTyxFQUFFO0VBQ3ZDLElBQUlBLE9BQU8sS0FBSyxLQUFLLENBQUMsRUFBRTtJQUN0QkEsT0FBTyxHQUFHLENBQUMsQ0FBQztFQUNkO0VBQ0EsT0FBTztJQUNMWCxJQUFJLEVBQUUsZUFBZTtJQUNyQlcsT0FBTztJQUNQLE1BQU1WLEVBQUVBLENBQUNTLEtBQUssRUFBRTtNQUNkLElBQUl1RCxxQkFBcUIsRUFBRUMsc0JBQXNCLEVBQUVDLHFCQUFxQjtNQUN4RSxNQUFNO1FBQ0oxRSxLQUFLO1FBQ0xHLGNBQWM7UUFDZDdCLFNBQVM7UUFDVHFCLFFBQVE7UUFDUm1CO01BQ0YsQ0FBQyxHQUFHRyxLQUFLO01BQ1QsTUFBQTBELFNBQUEsR0FNSXJILDREQUFRLENBQUM0RCxPQUFPLEVBQUVELEtBQUssQ0FBQztRQU50QjtVQUNKMkQsU0FBUyxHQUFHLEtBQUs7VUFDakJULFNBQVM7VUFDVEUsaUJBQWlCLEdBQUcxRywwREFBVTtVQUM5QnlHLGFBQWEsR0FBRztRQUVsQixDQUFDLEdBQUFPLFNBQUE7UUFESUUscUJBQXFCLEdBQUFDLHdCQUFBLENBQUFILFNBQUEsRUFBQUksU0FBQTtNQUUxQixNQUFNQyxZQUFZLEdBQUdiLFNBQVMsS0FBS2MsU0FBUyxJQUFJWixpQkFBaUIsS0FBSzFHLDBEQUFVLEdBQUd1RyxnQkFBZ0IsQ0FBQ0MsU0FBUyxJQUFJLElBQUksRUFBRUMsYUFBYSxFQUFFQyxpQkFBaUIsQ0FBQyxHQUFHQSxpQkFBaUI7TUFDNUssTUFBTWEsUUFBUSxHQUFHLE1BQU1sRSxjQUFjLENBQUNDLEtBQUssRUFBRTRELHFCQUFxQixDQUFDO01BQ25FLE1BQU1NLFlBQVksR0FBRyxDQUFDLENBQUNYLHFCQUFxQixHQUFHckUsY0FBYyxDQUFDb0UsYUFBYSxLQUFLLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR0MscUJBQXFCLENBQUNZLEtBQUssS0FBSyxDQUFDO01BQ2pJLE1BQU1DLGdCQUFnQixHQUFHTCxZQUFZLENBQUNHLFlBQVksQ0FBQztNQUNuRCxJQUFJRSxnQkFBZ0IsSUFBSSxJQUFJLEVBQUU7UUFDNUIsT0FBTyxDQUFDLENBQUM7TUFDWDtNQUNBLE1BQU1DLGNBQWMsR0FBRzFILHFFQUFpQixDQUFDeUgsZ0JBQWdCLEVBQUVyRixLQUFLLEVBQUUsT0FBT0wsUUFBUSxDQUFDSSxLQUFLLElBQUksSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHSixRQUFRLENBQUNJLEtBQUssQ0FBQ2UsUUFBUSxDQUFDckMsUUFBUSxDQUFDLENBQUMsQ0FBQzs7TUFFOUk7TUFDQSxJQUFJSCxTQUFTLEtBQUsrRyxnQkFBZ0IsRUFBRTtRQUNsQyxPQUFPO1VBQ0x6RSxLQUFLLEVBQUU7WUFDTHRDLFNBQVMsRUFBRTBHLFlBQVksQ0FBQyxDQUFDO1VBQzNCO1FBQ0YsQ0FBQztNQUNIO01BQ0EsTUFBTU8sZ0JBQWdCLEdBQUcsQ0FBQ0wsUUFBUSxDQUFDOUgsMkRBQU8sQ0FBQ2lJLGdCQUFnQixDQUFDLENBQUMsRUFBRUgsUUFBUSxDQUFDSSxjQUFjLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRUosUUFBUSxDQUFDSSxjQUFjLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztNQUN4SCxNQUFNRSxZQUFZLEdBQUcsQ0FBQyxJQUFJLENBQUMsQ0FBQ2Ysc0JBQXNCLEdBQUd0RSxjQUFjLENBQUNvRSxhQUFhLEtBQUssSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHRSxzQkFBc0IsQ0FBQ2dCLFNBQVMsS0FBSyxFQUFFLENBQUMsRUFBRTtRQUM5SW5ILFNBQVMsRUFBRStHLGdCQUFnQjtRQUMzQkksU0FBUyxFQUFFRjtNQUNiLENBQUMsQ0FBQztNQUNGLE1BQU1HLGFBQWEsR0FBR1YsWUFBWSxDQUFDRyxZQUFZLEdBQUcsQ0FBQyxDQUFDOztNQUVwRDtNQUNBLElBQUlPLGFBQWEsRUFBRTtRQUNqQixPQUFPO1VBQ0wvRSxJQUFJLEVBQUU7WUFDSnlFLEtBQUssRUFBRUQsWUFBWSxHQUFHLENBQUM7WUFDdkJNLFNBQVMsRUFBRUQ7VUFDYixDQUFDO1VBQ0Q1RSxLQUFLLEVBQUU7WUFDTHRDLFNBQVMsRUFBRW9IO1VBQ2I7UUFDRixDQUFDO01BQ0g7TUFDQSxNQUFNQywyQkFBMkIsR0FBR0gsWUFBWSxDQUFDSSxHQUFHLENBQUNDLENBQUMsSUFBSTtRQUN4RCxNQUFNMUIsU0FBUyxHQUFHOUcsZ0VBQVksQ0FBQ3dJLENBQUMsQ0FBQ3ZILFNBQVMsQ0FBQztRQUMzQyxPQUFPLENBQUN1SCxDQUFDLENBQUN2SCxTQUFTLEVBQUU2RixTQUFTLElBQUlTLFNBQVM7UUFDM0M7UUFDQWlCLENBQUMsQ0FBQ0osU0FBUyxDQUFDSyxLQUFLLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDQyxNQUFNLENBQUMsQ0FBQ0MsR0FBRyxFQUFFQyxDQUFDLEtBQUtELEdBQUcsR0FBR0MsQ0FBQyxFQUFFLENBQUMsQ0FBQztRQUN0RDtRQUNBSixDQUFDLENBQUNKLFNBQVMsQ0FBQyxDQUFDLENBQUMsRUFBRUksQ0FBQyxDQUFDSixTQUFTLENBQUM7TUFDOUIsQ0FBQyxDQUFDLENBQUNTLElBQUksQ0FBQyxDQUFDQyxDQUFDLEVBQUVDLENBQUMsS0FBS0QsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7TUFDOUIsTUFBTUMsMkJBQTJCLEdBQUdWLDJCQUEyQixDQUFDOUYsTUFBTSxDQUFDZ0csQ0FBQyxJQUFJQSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUNDLEtBQUssQ0FBQyxDQUFDO01BQ3hGO01BQ0E7TUFDQXpJLGdFQUFZLENBQUN3SSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUNTLEtBQUssQ0FBQ0wsQ0FBQyxJQUFJQSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUM7TUFDL0MsTUFBTU0sY0FBYyxHQUFHLENBQUMsQ0FBQzdCLHFCQUFxQixHQUFHMkIsMkJBQTJCLENBQUMsQ0FBQyxDQUFDLEtBQUssSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHM0IscUJBQXFCLENBQUMsQ0FBQyxDQUFDLEtBQUtpQiwyQkFBMkIsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7TUFDbEssSUFBSVksY0FBYyxLQUFLakksU0FBUyxFQUFFO1FBQ2hDLE9BQU87VUFDTHFDLElBQUksRUFBRTtZQUNKeUUsS0FBSyxFQUFFRCxZQUFZLEdBQUcsQ0FBQztZQUN2Qk0sU0FBUyxFQUFFRDtVQUNiLENBQUM7VUFDRDVFLEtBQUssRUFBRTtZQUNMdEMsU0FBUyxFQUFFaUk7VUFDYjtRQUNGLENBQUM7TUFDSDtNQUNBLE9BQU8sQ0FBQyxDQUFDO0lBQ1g7RUFDRixDQUFDO0FBQ0gsQ0FBQzs7QUFFRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNQyxJQUFJLEdBQUcsU0FBQUEsQ0FBVXRGLE9BQU8sRUFBRTtFQUM5QixJQUFJQSxPQUFPLEtBQUssS0FBSyxDQUFDLEVBQUU7SUFDdEJBLE9BQU8sR0FBRyxDQUFDLENBQUM7RUFDZDtFQUNBLE9BQU87SUFDTFgsSUFBSSxFQUFFLE1BQU07SUFDWlcsT0FBTztJQUNQLE1BQU1WLEVBQUVBLENBQUNTLEtBQUssRUFBRTtNQUNkLElBQUl3RixxQkFBcUIsRUFBRUMsb0JBQW9CO01BQy9DLE1BQU07UUFDSnBJLFNBQVM7UUFDVDZCLGNBQWM7UUFDZEgsS0FBSztRQUNMYSxnQkFBZ0I7UUFDaEJsQixRQUFRO1FBQ1JtQjtNQUNGLENBQUMsR0FBR0csS0FBSztNQUNULE1BQUEwRixVQUFBLEdBUUlySiw0REFBUSxDQUFDNEQsT0FBTyxFQUFFRCxLQUFLLENBQUM7UUFSdEI7VUFDSjJGLFFBQVEsRUFBRUMsYUFBYSxHQUFHLElBQUk7VUFDOUJqQyxTQUFTLEVBQUVrQyxjQUFjLEdBQUcsSUFBSTtVQUNoQ0Msa0JBQWtCLEVBQUVDLDJCQUEyQjtVQUMvQ0MsZ0JBQWdCLEdBQUcsU0FBUztVQUM1QkMseUJBQXlCLEdBQUcsTUFBTTtVQUNsQ0MsYUFBYSxHQUFHO1FBRWxCLENBQUMsR0FBQVIsVUFBQTtRQURJOUIscUJBQXFCLEdBQUFDLHdCQUFBLENBQUE2QixVQUFBLEVBQUFTLFVBQUE7O01BRzFCO01BQ0E7TUFDQTtNQUNBO01BQ0EsSUFBSSxDQUFDWCxxQkFBcUIsR0FBR3RHLGNBQWMsQ0FBQ3lDLEtBQUssS0FBSyxJQUFJLElBQUk2RCxxQkFBcUIsQ0FBQ3pDLGVBQWUsRUFBRTtRQUNuRyxPQUFPLENBQUMsQ0FBQztNQUNYO01BQ0EsTUFBTW5GLElBQUksR0FBR3pCLDJEQUFPLENBQUNrQixTQUFTLENBQUM7TUFDL0IsTUFBTStJLGVBQWUsR0FBR3BLLCtEQUFXLENBQUM0RCxnQkFBZ0IsQ0FBQztNQUNyRCxNQUFNeUcsZUFBZSxHQUFHbEssMkRBQU8sQ0FBQ3lELGdCQUFnQixDQUFDLEtBQUtBLGdCQUFnQjtNQUN0RSxNQUFNdEMsR0FBRyxHQUFHLE9BQU9vQixRQUFRLENBQUNJLEtBQUssSUFBSSxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUdKLFFBQVEsQ0FBQ0ksS0FBSyxDQUFDZSxRQUFRLENBQUNyQyxRQUFRLENBQUMsQ0FBQztNQUN2RixNQUFNc0ksa0JBQWtCLEdBQUdDLDJCQUEyQixLQUFLTSxlQUFlLElBQUksQ0FBQ0gsYUFBYSxHQUFHLENBQUNySix3RUFBb0IsQ0FBQytDLGdCQUFnQixDQUFDLENBQUMsR0FBRzlDLHlFQUFxQixDQUFDOEMsZ0JBQWdCLENBQUMsQ0FBQztNQUNsTCxNQUFNMEcsNEJBQTRCLEdBQUdMLHlCQUF5QixLQUFLLE1BQU07TUFDekUsSUFBSSxDQUFDRiwyQkFBMkIsSUFBSU8sNEJBQTRCLEVBQUU7UUFDaEVSLGtCQUFrQixDQUFDUyxJQUFJLENBQUMsR0FBR3hKLDZFQUF5QixDQUFDNkMsZ0JBQWdCLEVBQUVzRyxhQUFhLEVBQUVELHlCQUF5QixFQUFFM0ksR0FBRyxDQUFDLENBQUM7TUFDeEg7TUFDQSxNQUFNWixVQUFVLEdBQUcsQ0FBQ2tELGdCQUFnQixFQUFFLEdBQUdrRyxrQkFBa0IsQ0FBQztNQUM1RCxNQUFNN0IsUUFBUSxHQUFHLE1BQU1sRSxjQUFjLENBQUNDLEtBQUssRUFBRTRELHFCQUFxQixDQUFDO01BQ25FLE1BQU1ZLFNBQVMsR0FBRyxFQUFFO01BQ3BCLElBQUlnQyxhQUFhLEdBQUcsQ0FBQyxDQUFDZixvQkFBb0IsR0FBR3ZHLGNBQWMsQ0FBQ3FHLElBQUksS0FBSyxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUdFLG9CQUFvQixDQUFDakIsU0FBUyxLQUFLLEVBQUU7TUFDMUgsSUFBSW9CLGFBQWEsRUFBRTtRQUNqQnBCLFNBQVMsQ0FBQytCLElBQUksQ0FBQ3RDLFFBQVEsQ0FBQ3JHLElBQUksQ0FBQyxDQUFDO01BQ2hDO01BQ0EsSUFBSWlJLGNBQWMsRUFBRTtRQUNsQixNQUFNN0ksS0FBSyxHQUFHTCxxRUFBaUIsQ0FBQ1UsU0FBUyxFQUFFMEIsS0FBSyxFQUFFekIsR0FBRyxDQUFDO1FBQ3REa0gsU0FBUyxDQUFDK0IsSUFBSSxDQUFDdEMsUUFBUSxDQUFDakgsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUVpSCxRQUFRLENBQUNqSCxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztNQUN4RDtNQUNBd0osYUFBYSxHQUFHLENBQUMsR0FBR0EsYUFBYSxFQUFFO1FBQ2pDbkosU0FBUztRQUNUbUg7TUFDRixDQUFDLENBQUM7O01BRUY7TUFDQSxJQUFJLENBQUNBLFNBQVMsQ0FBQ2EsS0FBSyxDQUFDekgsSUFBSSxJQUFJQSxJQUFJLElBQUksQ0FBQyxDQUFDLEVBQUU7UUFDdkMsSUFBSTZJLHFCQUFxQixFQUFFQyxxQkFBcUI7UUFDaEQsTUFBTUMsU0FBUyxHQUFHLENBQUMsQ0FBQyxDQUFDRixxQkFBcUIsR0FBR3ZILGNBQWMsQ0FBQ3FHLElBQUksS0FBSyxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUdrQixxQkFBcUIsQ0FBQ3RDLEtBQUssS0FBSyxDQUFDLElBQUksQ0FBQztRQUMzSCxNQUFNTSxhQUFhLEdBQUcvSCxVQUFVLENBQUNpSyxTQUFTLENBQUM7UUFDM0MsSUFBSWxDLGFBQWEsRUFBRTtVQUNqQixJQUFJbUMsZUFBZTtVQUNuQixNQUFNQyx1QkFBdUIsR0FBR2hCLGNBQWMsS0FBSyxXQUFXLEdBQUdPLGVBQWUsS0FBS3BLLCtEQUFXLENBQUN5SSxhQUFhLENBQUMsR0FBRyxLQUFLO1VBQ3ZILE1BQU1xQywwQkFBMEIsR0FBRyxDQUFDLENBQUNGLGVBQWUsR0FBR0osYUFBYSxDQUFDLENBQUMsQ0FBQyxLQUFLLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR0ksZUFBZSxDQUFDcEMsU0FBUyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUM7VUFDN0gsSUFBSSxDQUFDcUMsdUJBQXVCLElBQUlDLDBCQUEwQixFQUFFO1lBQzFEO1lBQ0EsT0FBTztjQUNMcEgsSUFBSSxFQUFFO2dCQUNKeUUsS0FBSyxFQUFFd0MsU0FBUztnQkFDaEJuQyxTQUFTLEVBQUVnQztjQUNiLENBQUM7Y0FDRDdHLEtBQUssRUFBRTtnQkFDTHRDLFNBQVMsRUFBRW9IO2NBQ2I7WUFDRixDQUFDO1VBQ0g7UUFDRjs7UUFFQTtRQUNBO1FBQ0EsSUFBSWEsY0FBYyxHQUFHLENBQUNvQixxQkFBcUIsR0FBR0YsYUFBYSxDQUFDNUgsTUFBTSxDQUFDZ0csQ0FBQyxJQUFJQSxDQUFDLENBQUNKLFNBQVMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQ1MsSUFBSSxDQUFDLENBQUNDLENBQUMsRUFBRUMsQ0FBQyxLQUFLRCxDQUFDLENBQUNWLFNBQVMsQ0FBQyxDQUFDLENBQUMsR0FBR1csQ0FBQyxDQUFDWCxTQUFTLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSyxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUdrQyxxQkFBcUIsQ0FBQ3JKLFNBQVM7O1FBRW5NO1FBQ0EsSUFBSSxDQUFDaUksY0FBYyxFQUFFO1VBQ25CLFFBQVFVLGdCQUFnQjtZQUN0QixLQUFLLFNBQVM7Y0FDWjtnQkFDRSxJQUFJZSxzQkFBc0I7Z0JBQzFCLE1BQU0xSixTQUFTLEdBQUcsQ0FBQzBKLHNCQUFzQixHQUFHUCxhQUFhLENBQUM1SCxNQUFNLENBQUNnRyxDQUFDLElBQUk7a0JBQ3BFLElBQUkwQiw0QkFBNEIsRUFBRTtvQkFDaEMsTUFBTVUsZUFBZSxHQUFHaEwsK0RBQVcsQ0FBQzRJLENBQUMsQ0FBQ3ZILFNBQVMsQ0FBQztvQkFDaEQsT0FBTzJKLGVBQWUsS0FBS1osZUFBZTtvQkFDMUM7b0JBQ0E7b0JBQ0FZLGVBQWUsS0FBSyxHQUFHO2tCQUN6QjtrQkFDQSxPQUFPLElBQUk7Z0JBQ2IsQ0FBQyxDQUFDLENBQUNyQyxHQUFHLENBQUNDLENBQUMsSUFBSSxDQUFDQSxDQUFDLENBQUN2SCxTQUFTLEVBQUV1SCxDQUFDLENBQUNKLFNBQVMsQ0FBQzVGLE1BQU0sQ0FBQ3FGLFFBQVEsSUFBSUEsUUFBUSxHQUFHLENBQUMsQ0FBQyxDQUFDYSxNQUFNLENBQUMsQ0FBQ0MsR0FBRyxFQUFFZCxRQUFRLEtBQUtjLEdBQUcsR0FBR2QsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQ2dCLElBQUksQ0FBQyxDQUFDQyxDQUFDLEVBQUVDLENBQUMsS0FBS0QsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSyxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUc0QixzQkFBc0IsQ0FBQyxDQUFDLENBQUM7Z0JBQ2xNLElBQUkxSixTQUFTLEVBQUU7a0JBQ2JpSSxjQUFjLEdBQUdqSSxTQUFTO2dCQUM1QjtnQkFDQTtjQUNGO1lBQ0YsS0FBSyxrQkFBa0I7Y0FDckJpSSxjQUFjLEdBQUcxRixnQkFBZ0I7Y0FDakM7VUFDSjtRQUNGO1FBQ0EsSUFBSXZDLFNBQVMsS0FBS2lJLGNBQWMsRUFBRTtVQUNoQyxPQUFPO1lBQ0wzRixLQUFLLEVBQUU7Y0FDTHRDLFNBQVMsRUFBRWlJO1lBQ2I7VUFDRixDQUFDO1FBQ0g7TUFDRjtNQUNBLE9BQU8sQ0FBQyxDQUFDO0lBQ1g7RUFDRixDQUFDO0FBQ0gsQ0FBQztBQUVELFNBQVMyQixjQUFjQSxDQUFDaEQsUUFBUSxFQUFFakQsSUFBSSxFQUFFO0VBQ3RDLE9BQU87SUFDTE8sR0FBRyxFQUFFMEMsUUFBUSxDQUFDMUMsR0FBRyxHQUFHUCxJQUFJLENBQUM3QyxNQUFNO0lBQy9CdUQsS0FBSyxFQUFFdUMsUUFBUSxDQUFDdkMsS0FBSyxHQUFHVixJQUFJLENBQUNoRCxLQUFLO0lBQ2xDd0QsTUFBTSxFQUFFeUMsUUFBUSxDQUFDekMsTUFBTSxHQUFHUixJQUFJLENBQUM3QyxNQUFNO0lBQ3JDc0QsSUFBSSxFQUFFd0MsUUFBUSxDQUFDeEMsSUFBSSxHQUFHVCxJQUFJLENBQUNoRDtFQUM3QixDQUFDO0FBQ0g7QUFDQSxTQUFTa0oscUJBQXFCQSxDQUFDakQsUUFBUSxFQUFFO0VBQ3ZDLE9BQU9qSCxxREFBSyxDQUFDbUssSUFBSSxDQUFDdkosSUFBSSxJQUFJcUcsUUFBUSxDQUFDckcsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU13SixJQUFJLEdBQUcsU0FBQUEsQ0FBVW5ILE9BQU8sRUFBRTtFQUM5QixJQUFJQSxPQUFPLEtBQUssS0FBSyxDQUFDLEVBQUU7SUFDdEJBLE9BQU8sR0FBRyxDQUFDLENBQUM7RUFDZDtFQUNBLE9BQU87SUFDTFgsSUFBSSxFQUFFLE1BQU07SUFDWlcsT0FBTztJQUNQLE1BQU1WLEVBQUVBLENBQUNTLEtBQUssRUFBRTtNQUNkLE1BQU07UUFDSmpCO01BQ0YsQ0FBQyxHQUFHaUIsS0FBSztNQUNULE1BQUFxSCxVQUFBLEdBR0loTCw0REFBUSxDQUFDNEQsT0FBTyxFQUFFRCxLQUFLLENBQUM7UUFIdEI7VUFDSnhCLFFBQVEsR0FBRztRQUViLENBQUMsR0FBQTZJLFVBQUE7UUFESXpELHFCQUFxQixHQUFBQyx3QkFBQSxDQUFBd0QsVUFBQSxFQUFBQyxVQUFBO01BRTFCLFFBQVE5SSxRQUFRO1FBQ2QsS0FBSyxpQkFBaUI7VUFDcEI7WUFDRSxNQUFNeUYsUUFBUSxHQUFHLE1BQU1sRSxjQUFjLENBQUNDLEtBQUssRUFBQUYsYUFBQSxDQUFBQSxhQUFBLEtBQ3RDOEQscUJBQXFCO2NBQ3hCdkQsY0FBYyxFQUFFO1lBQVcsRUFDNUIsQ0FBQztZQUNGLE1BQU1rSCxPQUFPLEdBQUdOLGNBQWMsQ0FBQ2hELFFBQVEsRUFBRWxGLEtBQUssQ0FBQ3hCLFNBQVMsQ0FBQztZQUN6RCxPQUFPO2NBQ0xtQyxJQUFJLEVBQUU7Z0JBQ0o4SCxzQkFBc0IsRUFBRUQsT0FBTztnQkFDL0JFLGVBQWUsRUFBRVAscUJBQXFCLENBQUNLLE9BQU87Y0FDaEQ7WUFDRixDQUFDO1VBQ0g7UUFDRixLQUFLLFNBQVM7VUFDWjtZQUNFLE1BQU10RCxRQUFRLEdBQUcsTUFBTWxFLGNBQWMsQ0FBQ0MsS0FBSyxFQUFBRixhQUFBLENBQUFBLGFBQUEsS0FDdEM4RCxxQkFBcUI7Y0FDeEJ0RCxXQUFXLEVBQUU7WUFBSSxFQUNsQixDQUFDO1lBQ0YsTUFBTWlILE9BQU8sR0FBR04sY0FBYyxDQUFDaEQsUUFBUSxFQUFFbEYsS0FBSyxDQUFDdkIsUUFBUSxDQUFDO1lBQ3hELE9BQU87Y0FDTGtDLElBQUksRUFBRTtnQkFDSmdJLGNBQWMsRUFBRUgsT0FBTztnQkFDdkJJLE9BQU8sRUFBRVQscUJBQXFCLENBQUNLLE9BQU87Y0FDeEM7WUFDRixDQUFDO1VBQ0g7UUFDRjtVQUNFO1lBQ0UsT0FBTyxDQUFDLENBQUM7VUFDWDtNQUNKO0lBQ0Y7RUFDRixDQUFDO0FBQ0gsQ0FBQztBQUVELFNBQVNLLGVBQWVBLENBQUM3SSxLQUFLLEVBQUU7RUFDOUIsTUFBTThJLElBQUksR0FBR3JMLHVEQUFHLENBQUMsR0FBR3VDLEtBQUssQ0FBQzRGLEdBQUcsQ0FBQzNELElBQUksSUFBSUEsSUFBSSxDQUFDUyxJQUFJLENBQUMsQ0FBQztFQUNqRCxNQUFNcUcsSUFBSSxHQUFHdEwsdURBQUcsQ0FBQyxHQUFHdUMsS0FBSyxDQUFDNEYsR0FBRyxDQUFDM0QsSUFBSSxJQUFJQSxJQUFJLENBQUNPLEdBQUcsQ0FBQyxDQUFDO0VBQ2hELE1BQU13RyxJQUFJLEdBQUc5Syx1REFBRyxDQUFDLEdBQUc4QixLQUFLLENBQUM0RixHQUFHLENBQUMzRCxJQUFJLElBQUlBLElBQUksQ0FBQ1UsS0FBSyxDQUFDLENBQUM7RUFDbEQsTUFBTXNHLElBQUksR0FBRy9LLHVEQUFHLENBQUMsR0FBRzhCLEtBQUssQ0FBQzRGLEdBQUcsQ0FBQzNELElBQUksSUFBSUEsSUFBSSxDQUFDUSxNQUFNLENBQUMsQ0FBQztFQUNuRCxPQUFPO0lBQ0x6RCxDQUFDLEVBQUU4SixJQUFJO0lBQ1AzSixDQUFDLEVBQUU0SixJQUFJO0lBQ1A5SixLQUFLLEVBQUUrSixJQUFJLEdBQUdGLElBQUk7SUFDbEIxSixNQUFNLEVBQUU2SixJQUFJLEdBQUdGO0VBQ2pCLENBQUM7QUFDSDtBQUNBLFNBQVNHLGNBQWNBLENBQUNsSixLQUFLLEVBQUU7RUFDN0IsTUFBTW1KLFdBQVcsR0FBR25KLEtBQUssQ0FBQzhGLEtBQUssQ0FBQyxDQUFDLENBQUNJLElBQUksQ0FBQyxDQUFDQyxDQUFDLEVBQUVDLENBQUMsS0FBS0QsQ0FBQyxDQUFDaEgsQ0FBQyxHQUFHaUgsQ0FBQyxDQUFDakgsQ0FBQyxDQUFDO0VBQzNELE1BQU1pSyxNQUFNLEdBQUcsRUFBRTtFQUNqQixJQUFJQyxRQUFRLEdBQUcsSUFBSTtFQUNuQixLQUFLLElBQUloSixDQUFDLEdBQUcsQ0FBQyxFQUFFQSxDQUFDLEdBQUc4SSxXQUFXLENBQUM3SSxNQUFNLEVBQUVELENBQUMsRUFBRSxFQUFFO0lBQzNDLE1BQU00QixJQUFJLEdBQUdrSCxXQUFXLENBQUM5SSxDQUFDLENBQUM7SUFDM0IsSUFBSSxDQUFDZ0osUUFBUSxJQUFJcEgsSUFBSSxDQUFDOUMsQ0FBQyxHQUFHa0ssUUFBUSxDQUFDbEssQ0FBQyxHQUFHa0ssUUFBUSxDQUFDakssTUFBTSxHQUFHLENBQUMsRUFBRTtNQUMxRGdLLE1BQU0sQ0FBQzVCLElBQUksQ0FBQyxDQUFDdkYsSUFBSSxDQUFDLENBQUM7SUFDckIsQ0FBQyxNQUFNO01BQ0xtSCxNQUFNLENBQUNBLE1BQU0sQ0FBQzlJLE1BQU0sR0FBRyxDQUFDLENBQUMsQ0FBQ2tILElBQUksQ0FBQ3ZGLElBQUksQ0FBQztJQUN0QztJQUNBb0gsUUFBUSxHQUFHcEgsSUFBSTtFQUNqQjtFQUNBLE9BQU9tSCxNQUFNLENBQUN4RCxHQUFHLENBQUMzRCxJQUFJLElBQUl6RSxvRUFBZ0IsQ0FBQ3FMLGVBQWUsQ0FBQzVHLElBQUksQ0FBQyxDQUFDLENBQUM7QUFDcEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTXFILE1BQU0sR0FBRyxTQUFBQSxDQUFVcEksT0FBTyxFQUFFO0VBQ2hDLElBQUlBLE9BQU8sS0FBSyxLQUFLLENBQUMsRUFBRTtJQUN0QkEsT0FBTyxHQUFHLENBQUMsQ0FBQztFQUNkO0VBQ0EsT0FBTztJQUNMWCxJQUFJLEVBQUUsUUFBUTtJQUNkVyxPQUFPO0lBQ1AsTUFBTVYsRUFBRUEsQ0FBQ1MsS0FBSyxFQUFFO01BQ2QsTUFBTTtRQUNKM0MsU0FBUztRQUNUd0MsUUFBUTtRQUNSZCxLQUFLO1FBQ0xMLFFBQVE7UUFDUkY7TUFDRixDQUFDLEdBQUd3QixLQUFLO01BQ1Q7TUFDQTtNQUNBO01BQ0EsTUFBTTtRQUNKTyxPQUFPLEdBQUcsQ0FBQztRQUNYeEMsQ0FBQztRQUNERztNQUNGLENBQUMsR0FBRzdCLDREQUFRLENBQUM0RCxPQUFPLEVBQUVELEtBQUssQ0FBQztNQUM1QixNQUFNc0ksaUJBQWlCLEdBQUdDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDLENBQUMsT0FBTzlKLFFBQVEsQ0FBQytKLGNBQWMsSUFBSSxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUcvSixRQUFRLENBQUMrSixjQUFjLENBQUM1SSxRQUFRLENBQUN0QyxTQUFTLENBQUMsQ0FBQyxLQUFLLEVBQUUsQ0FBQztNQUM1SSxNQUFNbUwsV0FBVyxHQUFHVCxjQUFjLENBQUNLLGlCQUFpQixDQUFDO01BQ3JELE1BQU1LLFFBQVEsR0FBR3BNLG9FQUFnQixDQUFDcUwsZUFBZSxDQUFDVSxpQkFBaUIsQ0FBQyxDQUFDO01BQ3JFLE1BQU05SCxhQUFhLEdBQUdsRSxvRUFBZ0IsQ0FBQ2lFLE9BQU8sQ0FBQztNQUMvQyxTQUFTcUkscUJBQXFCQSxDQUFBLEVBQUc7UUFDL0I7UUFDQSxJQUFJRixXQUFXLENBQUNySixNQUFNLEtBQUssQ0FBQyxJQUFJcUosV0FBVyxDQUFDLENBQUMsQ0FBQyxDQUFDakgsSUFBSSxHQUFHaUgsV0FBVyxDQUFDLENBQUMsQ0FBQyxDQUFDaEgsS0FBSyxJQUFJM0QsQ0FBQyxJQUFJLElBQUksSUFBSUcsQ0FBQyxJQUFJLElBQUksRUFBRTtVQUNwRztVQUNBLE9BQU93SyxXQUFXLENBQUNHLElBQUksQ0FBQzdILElBQUksSUFBSWpELENBQUMsR0FBR2lELElBQUksQ0FBQ1MsSUFBSSxHQUFHakIsYUFBYSxDQUFDaUIsSUFBSSxJQUFJMUQsQ0FBQyxHQUFHaUQsSUFBSSxDQUFDVSxLQUFLLEdBQUdsQixhQUFhLENBQUNrQixLQUFLLElBQUl4RCxDQUFDLEdBQUc4QyxJQUFJLENBQUNPLEdBQUcsR0FBR2YsYUFBYSxDQUFDZSxHQUFHLElBQUlyRCxDQUFDLEdBQUc4QyxJQUFJLENBQUNRLE1BQU0sR0FBR2hCLGFBQWEsQ0FBQ2dCLE1BQU0sQ0FBQyxJQUFJbUgsUUFBUTtRQUN2TTs7UUFFQTtRQUNBLElBQUlELFdBQVcsQ0FBQ3JKLE1BQU0sSUFBSSxDQUFDLEVBQUU7VUFDM0IsSUFBSXJELCtEQUFXLENBQUNxQixTQUFTLENBQUMsS0FBSyxHQUFHLEVBQUU7WUFDbEMsTUFBTXlMLFNBQVMsR0FBR0osV0FBVyxDQUFDLENBQUMsQ0FBQztZQUNoQyxNQUFNSyxRQUFRLEdBQUdMLFdBQVcsQ0FBQ0EsV0FBVyxDQUFDckosTUFBTSxHQUFHLENBQUMsQ0FBQztZQUNwRCxNQUFNMkosS0FBSyxHQUFHN00sMkRBQU8sQ0FBQ2tCLFNBQVMsQ0FBQyxLQUFLLEtBQUs7WUFDMUMsTUFBTWtFLEdBQUcsR0FBR3VILFNBQVMsQ0FBQ3ZILEdBQUc7WUFDekIsTUFBTUMsTUFBTSxHQUFHdUgsUUFBUSxDQUFDdkgsTUFBTTtZQUM5QixNQUFNQyxJQUFJLEdBQUd1SCxLQUFLLEdBQUdGLFNBQVMsQ0FBQ3JILElBQUksR0FBR3NILFFBQVEsQ0FBQ3RILElBQUk7WUFDbkQsTUFBTUMsS0FBSyxHQUFHc0gsS0FBSyxHQUFHRixTQUFTLENBQUNwSCxLQUFLLEdBQUdxSCxRQUFRLENBQUNySCxLQUFLO1lBQ3RELE1BQU0xRCxLQUFLLEdBQUcwRCxLQUFLLEdBQUdELElBQUk7WUFDMUIsTUFBTXRELE1BQU0sR0FBR3FELE1BQU0sR0FBR0QsR0FBRztZQUMzQixPQUFPO2NBQ0xBLEdBQUc7Y0FDSEMsTUFBTTtjQUNOQyxJQUFJO2NBQ0pDLEtBQUs7Y0FDTDFELEtBQUs7Y0FDTEcsTUFBTTtjQUNOSixDQUFDLEVBQUUwRCxJQUFJO2NBQ1B2RCxDQUFDLEVBQUVxRDtZQUNMLENBQUM7VUFDSDtVQUNBLE1BQU0wSCxVQUFVLEdBQUc5TSwyREFBTyxDQUFDa0IsU0FBUyxDQUFDLEtBQUssTUFBTTtVQUNoRCxNQUFNNkwsUUFBUSxHQUFHak0sdURBQUcsQ0FBQyxHQUFHeUwsV0FBVyxDQUFDL0QsR0FBRyxDQUFDM0QsSUFBSSxJQUFJQSxJQUFJLENBQUNVLEtBQUssQ0FBQyxDQUFDO1VBQzVELE1BQU15SCxPQUFPLEdBQUczTSx1REFBRyxDQUFDLEdBQUdrTSxXQUFXLENBQUMvRCxHQUFHLENBQUMzRCxJQUFJLElBQUlBLElBQUksQ0FBQ1MsSUFBSSxDQUFDLENBQUM7VUFDMUQsTUFBTTJILFlBQVksR0FBR1YsV0FBVyxDQUFDOUosTUFBTSxDQUFDb0MsSUFBSSxJQUFJaUksVUFBVSxHQUFHakksSUFBSSxDQUFDUyxJQUFJLEtBQUswSCxPQUFPLEdBQUduSSxJQUFJLENBQUNVLEtBQUssS0FBS3dILFFBQVEsQ0FBQztVQUM3RyxNQUFNM0gsR0FBRyxHQUFHNkgsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDN0gsR0FBRztVQUMvQixNQUFNQyxNQUFNLEdBQUc0SCxZQUFZLENBQUNBLFlBQVksQ0FBQy9KLE1BQU0sR0FBRyxDQUFDLENBQUMsQ0FBQ21DLE1BQU07VUFDM0QsTUFBTUMsSUFBSSxHQUFHMEgsT0FBTztVQUNwQixNQUFNekgsS0FBSyxHQUFHd0gsUUFBUTtVQUN0QixNQUFNbEwsS0FBSyxHQUFHMEQsS0FBSyxHQUFHRCxJQUFJO1VBQzFCLE1BQU10RCxNQUFNLEdBQUdxRCxNQUFNLEdBQUdELEdBQUc7VUFDM0IsT0FBTztZQUNMQSxHQUFHO1lBQ0hDLE1BQU07WUFDTkMsSUFBSTtZQUNKQyxLQUFLO1lBQ0wxRCxLQUFLO1lBQ0xHLE1BQU07WUFDTkosQ0FBQyxFQUFFMEQsSUFBSTtZQUNQdkQsQ0FBQyxFQUFFcUQ7VUFDTCxDQUFDO1FBQ0g7UUFDQSxPQUFPb0gsUUFBUTtNQUNqQjtNQUNBLE1BQU1VLFVBQVUsR0FBRyxNQUFNM0ssUUFBUSxDQUFDTSxlQUFlLENBQUM7UUFDaER6QixTQUFTLEVBQUU7VUFDVHFMO1FBQ0YsQ0FBQztRQUNEcEwsUUFBUSxFQUFFcUMsUUFBUSxDQUFDckMsUUFBUTtRQUMzQmdCO01BQ0YsQ0FBQyxDQUFDO01BQ0YsSUFBSU8sS0FBSyxDQUFDeEIsU0FBUyxDQUFDUSxDQUFDLEtBQUtzTCxVQUFVLENBQUM5TCxTQUFTLENBQUNRLENBQUMsSUFBSWdCLEtBQUssQ0FBQ3hCLFNBQVMsQ0FBQ1csQ0FBQyxLQUFLbUwsVUFBVSxDQUFDOUwsU0FBUyxDQUFDVyxDQUFDLElBQUlhLEtBQUssQ0FBQ3hCLFNBQVMsQ0FBQ1MsS0FBSyxLQUFLcUwsVUFBVSxDQUFDOUwsU0FBUyxDQUFDUyxLQUFLLElBQUllLEtBQUssQ0FBQ3hCLFNBQVMsQ0FBQ1ksTUFBTSxLQUFLa0wsVUFBVSxDQUFDOUwsU0FBUyxDQUFDWSxNQUFNLEVBQUU7UUFDbE4sT0FBTztVQUNMd0IsS0FBSyxFQUFFO1lBQ0xaLEtBQUssRUFBRXNLO1VBQ1Q7UUFDRixDQUFDO01BQ0g7TUFDQSxPQUFPLENBQUMsQ0FBQztJQUNYO0VBQ0YsQ0FBQztBQUNILENBQUM7O0FBRUQ7QUFDQTs7QUFFQSxlQUFlQyxvQkFBb0JBLENBQUN0SixLQUFLLEVBQUVDLE9BQU8sRUFBRTtFQUNsRCxNQUFNO0lBQ0o1QyxTQUFTO0lBQ1RxQixRQUFRO0lBQ1JtQjtFQUNGLENBQUMsR0FBR0csS0FBSztFQUNULE1BQU0xQyxHQUFHLEdBQUcsT0FBT29CLFFBQVEsQ0FBQ0ksS0FBSyxJQUFJLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR0osUUFBUSxDQUFDSSxLQUFLLENBQUNlLFFBQVEsQ0FBQ3JDLFFBQVEsQ0FBQyxDQUFDO0VBQ3ZGLE1BQU1JLElBQUksR0FBR3pCLDJEQUFPLENBQUNrQixTQUFTLENBQUM7RUFDL0IsTUFBTTZGLFNBQVMsR0FBRzlHLGdFQUFZLENBQUNpQixTQUFTLENBQUM7RUFDekMsTUFBTVEsVUFBVSxHQUFHN0IsK0RBQVcsQ0FBQ3FCLFNBQVMsQ0FBQyxLQUFLLEdBQUc7RUFDakQsTUFBTWtNLGFBQWEsR0FBRyxDQUFDLE1BQU0sRUFBRSxLQUFLLENBQUMsQ0FBQ0MsUUFBUSxDQUFDNUwsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLEdBQUcsQ0FBQztFQUM3RCxNQUFNNkwsY0FBYyxHQUFHbk0sR0FBRyxJQUFJTyxVQUFVLEdBQUcsQ0FBQyxDQUFDLEdBQUcsQ0FBQztFQUNqRCxNQUFNNkwsUUFBUSxHQUFHck4sNERBQVEsQ0FBQzRELE9BQU8sRUFBRUQsS0FBSyxDQUFDOztFQUV6QztFQUNBLElBQUk7SUFDRjJGLFFBQVE7SUFDUmhDLFNBQVM7SUFDVGpHO0VBQ0YsQ0FBQyxHQUFHLE9BQU9nTSxRQUFRLEtBQUssUUFBUSxHQUFHO0lBQ2pDL0QsUUFBUSxFQUFFK0QsUUFBUTtJQUNsQi9GLFNBQVMsRUFBRSxDQUFDO0lBQ1pqRyxhQUFhLEVBQUU7RUFDakIsQ0FBQyxHQUFHO0lBQ0ZpSSxRQUFRLEVBQUUrRCxRQUFRLENBQUMvRCxRQUFRLElBQUksQ0FBQztJQUNoQ2hDLFNBQVMsRUFBRStGLFFBQVEsQ0FBQy9GLFNBQVMsSUFBSSxDQUFDO0lBQ2xDakcsYUFBYSxFQUFFZ00sUUFBUSxDQUFDaE07RUFDMUIsQ0FBQztFQUNELElBQUl3RixTQUFTLElBQUksT0FBT3hGLGFBQWEsS0FBSyxRQUFRLEVBQUU7SUFDbERpRyxTQUFTLEdBQUdULFNBQVMsS0FBSyxLQUFLLEdBQUd4RixhQUFhLEdBQUcsQ0FBQyxDQUFDLEdBQUdBLGFBQWE7RUFDdEU7RUFDQSxPQUFPRyxVQUFVLEdBQUc7SUFDbEJFLENBQUMsRUFBRTRGLFNBQVMsR0FBRzhGLGNBQWM7SUFDN0J2TCxDQUFDLEVBQUV5SCxRQUFRLEdBQUc0RDtFQUNoQixDQUFDLEdBQUc7SUFDRnhMLENBQUMsRUFBRTRILFFBQVEsR0FBRzRELGFBQWE7SUFDM0JyTCxDQUFDLEVBQUV5RixTQUFTLEdBQUc4RjtFQUNqQixDQUFDO0FBQ0g7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNNUcsTUFBTSxHQUFHLFNBQUFBLENBQVU1QyxPQUFPLEVBQUU7RUFDaEMsSUFBSUEsT0FBTyxLQUFLLEtBQUssQ0FBQyxFQUFFO0lBQ3RCQSxPQUFPLEdBQUcsQ0FBQztFQUNiO0VBQ0EsT0FBTztJQUNMWCxJQUFJLEVBQUUsUUFBUTtJQUNkVyxPQUFPO0lBQ1AsTUFBTVYsRUFBRUEsQ0FBQ1MsS0FBSyxFQUFFO01BQ2QsSUFBSTJKLHFCQUFxQixFQUFFbkUscUJBQXFCO01BQ2hELE1BQU07UUFDSnpILENBQUM7UUFDREcsQ0FBQztRQUNEYixTQUFTO1FBQ1Q2QjtNQUNGLENBQUMsR0FBR2MsS0FBSztNQUNULE1BQU00SixVQUFVLEdBQUcsTUFBTU4sb0JBQW9CLENBQUN0SixLQUFLLEVBQUVDLE9BQU8sQ0FBQzs7TUFFN0Q7TUFDQTtNQUNBLElBQUk1QyxTQUFTLE1BQU0sQ0FBQ3NNLHFCQUFxQixHQUFHekssY0FBYyxDQUFDMkQsTUFBTSxLQUFLLElBQUksR0FBRyxLQUFLLENBQUMsR0FBRzhHLHFCQUFxQixDQUFDdE0sU0FBUyxDQUFDLElBQUksQ0FBQ21JLHFCQUFxQixHQUFHdEcsY0FBYyxDQUFDeUMsS0FBSyxLQUFLLElBQUksSUFBSTZELHFCQUFxQixDQUFDekMsZUFBZSxFQUFFO1FBQ3pOLE9BQU8sQ0FBQyxDQUFDO01BQ1g7TUFDQSxPQUFPO1FBQ0xoRixDQUFDLEVBQUVBLENBQUMsR0FBRzZMLFVBQVUsQ0FBQzdMLENBQUM7UUFDbkJHLENBQUMsRUFBRUEsQ0FBQyxHQUFHMEwsVUFBVSxDQUFDMUwsQ0FBQztRQUNuQndCLElBQUksRUFBQUksYUFBQSxDQUFBQSxhQUFBLEtBQ0M4SixVQUFVO1VBQ2J2TTtRQUFTO01BRWIsQ0FBQztJQUNIO0VBQ0YsQ0FBQztBQUNILENBQUM7O0FBRUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU13TSxLQUFLLEdBQUcsU0FBQUEsQ0FBVTVKLE9BQU8sRUFBRTtFQUMvQixJQUFJQSxPQUFPLEtBQUssS0FBSyxDQUFDLEVBQUU7SUFDdEJBLE9BQU8sR0FBRyxDQUFDLENBQUM7RUFDZDtFQUNBLE9BQU87SUFDTFgsSUFBSSxFQUFFLE9BQU87SUFDYlcsT0FBTztJQUNQLE1BQU1WLEVBQUVBLENBQUNTLEtBQUssRUFBRTtNQUNkLE1BQU07UUFDSmpDLENBQUM7UUFDREcsQ0FBQztRQUNEYjtNQUNGLENBQUMsR0FBRzJDLEtBQUs7TUFDVCxNQUFBOEosVUFBQSxHQWdCSXpOLDREQUFRLENBQUM0RCxPQUFPLEVBQUVELEtBQUssQ0FBQztRQWhCdEI7VUFDSjJGLFFBQVEsRUFBRUMsYUFBYSxHQUFHLElBQUk7VUFDOUJqQyxTQUFTLEVBQUVrQyxjQUFjLEdBQUcsS0FBSztVQUNqQ2tFLE9BQU8sR0FBRztZQUNSeEssRUFBRSxFQUFFbkMsSUFBSSxJQUFJO2NBQ1YsSUFBSTtnQkFDRlcsQ0FBQztnQkFDREc7Y0FDRixDQUFDLEdBQUdkLElBQUk7Y0FDUixPQUFPO2dCQUNMVyxDQUFDO2dCQUNERztjQUNGLENBQUM7WUFDSDtVQUNGO1FBRUYsQ0FBQyxHQUFBNEwsVUFBQTtRQURJbEcscUJBQXFCLEdBQUFDLHdCQUFBLENBQUFpRyxVQUFBLEVBQUFFLFVBQUE7TUFFMUIsTUFBTTNMLE1BQU0sR0FBRztRQUNiTixDQUFDO1FBQ0RHO01BQ0YsQ0FBQztNQUNELE1BQU0rRixRQUFRLEdBQUcsTUFBTWxFLGNBQWMsQ0FBQ0MsS0FBSyxFQUFFNEQscUJBQXFCLENBQUM7TUFDbkUsTUFBTUQsU0FBUyxHQUFHM0gsK0RBQVcsQ0FBQ0csMkRBQU8sQ0FBQ2tCLFNBQVMsQ0FBQyxDQUFDO01BQ2pELE1BQU1zSSxRQUFRLEdBQUd6SSxtRUFBZSxDQUFDeUcsU0FBUyxDQUFDO01BQzNDLElBQUlzRyxhQUFhLEdBQUc1TCxNQUFNLENBQUNzSCxRQUFRLENBQUM7TUFDcEMsSUFBSXVFLGNBQWMsR0FBRzdMLE1BQU0sQ0FBQ3NGLFNBQVMsQ0FBQztNQUN0QyxJQUFJaUMsYUFBYSxFQUFFO1FBQ2pCLE1BQU11RSxPQUFPLEdBQUd4RSxRQUFRLEtBQUssR0FBRyxHQUFHLEtBQUssR0FBRyxNQUFNO1FBQ2pELE1BQU15RSxPQUFPLEdBQUd6RSxRQUFRLEtBQUssR0FBRyxHQUFHLFFBQVEsR0FBRyxPQUFPO1FBQ3JELE1BQU1uSixHQUFHLEdBQUd5TixhQUFhLEdBQUdoRyxRQUFRLENBQUNrRyxPQUFPLENBQUM7UUFDN0MsTUFBTWxOLEdBQUcsR0FBR2dOLGFBQWEsR0FBR2hHLFFBQVEsQ0FBQ21HLE9BQU8sQ0FBQztRQUM3Q0gsYUFBYSxHQUFHeE4seURBQUssQ0FBQ0QsR0FBRyxFQUFFeU4sYUFBYSxFQUFFaE4sR0FBRyxDQUFDO01BQ2hEO01BQ0EsSUFBSTRJLGNBQWMsRUFBRTtRQUNsQixNQUFNc0UsT0FBTyxHQUFHeEcsU0FBUyxLQUFLLEdBQUcsR0FBRyxLQUFLLEdBQUcsTUFBTTtRQUNsRCxNQUFNeUcsT0FBTyxHQUFHekcsU0FBUyxLQUFLLEdBQUcsR0FBRyxRQUFRLEdBQUcsT0FBTztRQUN0RCxNQUFNbkgsR0FBRyxHQUFHME4sY0FBYyxHQUFHakcsUUFBUSxDQUFDa0csT0FBTyxDQUFDO1FBQzlDLE1BQU1sTixHQUFHLEdBQUdpTixjQUFjLEdBQUdqRyxRQUFRLENBQUNtRyxPQUFPLENBQUM7UUFDOUNGLGNBQWMsR0FBR3pOLHlEQUFLLENBQUNELEdBQUcsRUFBRTBOLGNBQWMsRUFBRWpOLEdBQUcsQ0FBQztNQUNsRDtNQUNBLE1BQU1vTixhQUFhLEdBQUdOLE9BQU8sQ0FBQ3hLLEVBQUUsQ0FBQU8sYUFBQSxDQUFBQSxhQUFBLEtBQzNCRSxLQUFLO1FBQ1IsQ0FBQzJGLFFBQVEsR0FBR3NFLGFBQWE7UUFDekIsQ0FBQ3RHLFNBQVMsR0FBR3VHO01BQWMsRUFDNUIsQ0FBQztNQUNGLE9BQUFwSyxhQUFBLENBQUFBLGFBQUEsS0FDS3VLLGFBQWE7UUFDaEIzSyxJQUFJLEVBQUU7VUFDSjNCLENBQUMsRUFBRXNNLGFBQWEsQ0FBQ3RNLENBQUMsR0FBR0EsQ0FBQztVQUN0QkcsQ0FBQyxFQUFFbU0sYUFBYSxDQUFDbk0sQ0FBQyxHQUFHQSxDQUFDO1VBQ3RCb00sT0FBTyxFQUFFO1lBQ1AsQ0FBQzNFLFFBQVEsR0FBR0MsYUFBYTtZQUN6QixDQUFDakMsU0FBUyxHQUFHa0M7VUFDZjtRQUNGO01BQUM7SUFFTDtFQUNGLENBQUM7QUFDSCxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsTUFBTTBFLFVBQVUsR0FBRyxTQUFBQSxDQUFVdEssT0FBTyxFQUFFO0VBQ3BDLElBQUlBLE9BQU8sS0FBSyxLQUFLLENBQUMsRUFBRTtJQUN0QkEsT0FBTyxHQUFHLENBQUMsQ0FBQztFQUNkO0VBQ0EsT0FBTztJQUNMQSxPQUFPO0lBQ1BWLEVBQUVBLENBQUNTLEtBQUssRUFBRTtNQUNSLE1BQU07UUFDSmpDLENBQUM7UUFDREcsQ0FBQztRQUNEYixTQUFTO1FBQ1QwQixLQUFLO1FBQ0xHO01BQ0YsQ0FBQyxHQUFHYyxLQUFLO01BQ1QsTUFBTTtRQUNKNkMsTUFBTSxHQUFHLENBQUM7UUFDVjhDLFFBQVEsRUFBRUMsYUFBYSxHQUFHLElBQUk7UUFDOUJqQyxTQUFTLEVBQUVrQyxjQUFjLEdBQUc7TUFDOUIsQ0FBQyxHQUFHeEosNERBQVEsQ0FBQzRELE9BQU8sRUFBRUQsS0FBSyxDQUFDO01BQzVCLE1BQU0zQixNQUFNLEdBQUc7UUFDYk4sQ0FBQztRQUNERztNQUNGLENBQUM7TUFDRCxNQUFNeUYsU0FBUyxHQUFHM0gsK0RBQVcsQ0FBQ3FCLFNBQVMsQ0FBQztNQUN4QyxNQUFNc0ksUUFBUSxHQUFHekksbUVBQWUsQ0FBQ3lHLFNBQVMsQ0FBQztNQUMzQyxJQUFJc0csYUFBYSxHQUFHNUwsTUFBTSxDQUFDc0gsUUFBUSxDQUFDO01BQ3BDLElBQUl1RSxjQUFjLEdBQUc3TCxNQUFNLENBQUNzRixTQUFTLENBQUM7TUFDdEMsTUFBTTZHLFNBQVMsR0FBR25PLDREQUFRLENBQUN3RyxNQUFNLEVBQUU3QyxLQUFLLENBQUM7TUFDekMsTUFBTXlLLGNBQWMsR0FBRyxPQUFPRCxTQUFTLEtBQUssUUFBUSxHQUFHO1FBQ3JEN0UsUUFBUSxFQUFFNkUsU0FBUztRQUNuQjdHLFNBQVMsRUFBRTtNQUNiLENBQUMsR0FBQTdELGFBQUE7UUFDQzZGLFFBQVEsRUFBRSxDQUFDO1FBQ1hoQyxTQUFTLEVBQUU7TUFBQyxHQUNUNkcsU0FBUyxDQUNiO01BQ0QsSUFBSTVFLGFBQWEsRUFBRTtRQUNqQixNQUFNOEUsR0FBRyxHQUFHL0UsUUFBUSxLQUFLLEdBQUcsR0FBRyxRQUFRLEdBQUcsT0FBTztRQUNqRCxNQUFNZ0YsUUFBUSxHQUFHNUwsS0FBSyxDQUFDeEIsU0FBUyxDQUFDb0ksUUFBUSxDQUFDLEdBQUc1RyxLQUFLLENBQUN2QixRQUFRLENBQUNrTixHQUFHLENBQUMsR0FBR0QsY0FBYyxDQUFDOUUsUUFBUTtRQUMxRixNQUFNaUYsUUFBUSxHQUFHN0wsS0FBSyxDQUFDeEIsU0FBUyxDQUFDb0ksUUFBUSxDQUFDLEdBQUc1RyxLQUFLLENBQUN4QixTQUFTLENBQUNtTixHQUFHLENBQUMsR0FBR0QsY0FBYyxDQUFDOUUsUUFBUTtRQUMzRixJQUFJc0UsYUFBYSxHQUFHVSxRQUFRLEVBQUU7VUFDNUJWLGFBQWEsR0FBR1UsUUFBUTtRQUMxQixDQUFDLE1BQU0sSUFBSVYsYUFBYSxHQUFHVyxRQUFRLEVBQUU7VUFDbkNYLGFBQWEsR0FBR1csUUFBUTtRQUMxQjtNQUNGO01BQ0EsSUFBSS9FLGNBQWMsRUFBRTtRQUNsQixJQUFJOEQscUJBQXFCLEVBQUVrQixzQkFBc0I7UUFDakQsTUFBTUgsR0FBRyxHQUFHL0UsUUFBUSxLQUFLLEdBQUcsR0FBRyxPQUFPLEdBQUcsUUFBUTtRQUNqRCxNQUFNbUYsWUFBWSxHQUFHLENBQUMsS0FBSyxFQUFFLE1BQU0sQ0FBQyxDQUFDdEIsUUFBUSxDQUFDck4sMkRBQU8sQ0FBQ2tCLFNBQVMsQ0FBQyxDQUFDO1FBQ2pFLE1BQU1zTixRQUFRLEdBQUc1TCxLQUFLLENBQUN4QixTQUFTLENBQUNvRyxTQUFTLENBQUMsR0FBRzVFLEtBQUssQ0FBQ3ZCLFFBQVEsQ0FBQ2tOLEdBQUcsQ0FBQyxJQUFJSSxZQUFZLEdBQUcsQ0FBQyxDQUFDbkIscUJBQXFCLEdBQUd6SyxjQUFjLENBQUMyRCxNQUFNLEtBQUssSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHOEcscUJBQXFCLENBQUNoRyxTQUFTLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLElBQUltSCxZQUFZLEdBQUcsQ0FBQyxHQUFHTCxjQUFjLENBQUM5RyxTQUFTLENBQUM7UUFDblAsTUFBTWlILFFBQVEsR0FBRzdMLEtBQUssQ0FBQ3hCLFNBQVMsQ0FBQ29HLFNBQVMsQ0FBQyxHQUFHNUUsS0FBSyxDQUFDeEIsU0FBUyxDQUFDbU4sR0FBRyxDQUFDLElBQUlJLFlBQVksR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFDRCxzQkFBc0IsR0FBRzNMLGNBQWMsQ0FBQzJELE1BQU0sS0FBSyxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUdnSSxzQkFBc0IsQ0FBQ2xILFNBQVMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxJQUFJbUgsWUFBWSxHQUFHTCxjQUFjLENBQUM5RyxTQUFTLEdBQUcsQ0FBQyxDQUFDO1FBQ3RQLElBQUl1RyxjQUFjLEdBQUdTLFFBQVEsRUFBRTtVQUM3QlQsY0FBYyxHQUFHUyxRQUFRO1FBQzNCLENBQUMsTUFBTSxJQUFJVCxjQUFjLEdBQUdVLFFBQVEsRUFBRTtVQUNwQ1YsY0FBYyxHQUFHVSxRQUFRO1FBQzNCO01BQ0Y7TUFDQSxPQUFPO1FBQ0wsQ0FBQ2pGLFFBQVEsR0FBR3NFLGFBQWE7UUFDekIsQ0FBQ3RHLFNBQVMsR0FBR3VHO01BQ2YsQ0FBQztJQUNIO0VBQ0YsQ0FBQztBQUNILENBQUM7O0FBRUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTWEsSUFBSSxHQUFHLFNBQUFBLENBQVU5SyxPQUFPLEVBQUU7RUFDOUIsSUFBSUEsT0FBTyxLQUFLLEtBQUssQ0FBQyxFQUFFO0lBQ3RCQSxPQUFPLEdBQUcsQ0FBQyxDQUFDO0VBQ2Q7RUFDQSxPQUFPO0lBQ0xYLElBQUksRUFBRSxNQUFNO0lBQ1pXLE9BQU87SUFDUCxNQUFNVixFQUFFQSxDQUFDUyxLQUFLLEVBQUU7TUFDZCxJQUFJZ0wscUJBQXFCLEVBQUVDLHNCQUFzQjtNQUNqRCxNQUFNO1FBQ0o1TixTQUFTO1FBQ1QwQixLQUFLO1FBQ0xMLFFBQVE7UUFDUm1CO01BQ0YsQ0FBQyxHQUFHRyxLQUFLO01BQ1QsTUFBQWtMLFVBQUEsR0FHSTdPLDREQUFRLENBQUM0RCxPQUFPLEVBQUVELEtBQUssQ0FBQztRQUh0QjtVQUNKbUwsS0FBSyxHQUFHQSxDQUFBLEtBQU0sQ0FBQztRQUVqQixDQUFDLEdBQUFELFVBQUE7UUFESXRILHFCQUFxQixHQUFBQyx3QkFBQSxDQUFBcUgsVUFBQSxFQUFBRSxVQUFBO01BRTFCLE1BQU1uSCxRQUFRLEdBQUcsTUFBTWxFLGNBQWMsQ0FBQ0MsS0FBSyxFQUFFNEQscUJBQXFCLENBQUM7TUFDbkUsTUFBTWhHLElBQUksR0FBR3pCLDJEQUFPLENBQUNrQixTQUFTLENBQUM7TUFDL0IsTUFBTTZGLFNBQVMsR0FBRzlHLGdFQUFZLENBQUNpQixTQUFTLENBQUM7TUFDekMsTUFBTTBFLE9BQU8sR0FBRy9GLCtEQUFXLENBQUNxQixTQUFTLENBQUMsS0FBSyxHQUFHO01BQzlDLE1BQU07UUFDSlcsS0FBSztRQUNMRztNQUNGLENBQUMsR0FBR1ksS0FBSyxDQUFDdkIsUUFBUTtNQUNsQixJQUFJNk4sVUFBVTtNQUNkLElBQUlDLFNBQVM7TUFDYixJQUFJMU4sSUFBSSxLQUFLLEtBQUssSUFBSUEsSUFBSSxLQUFLLFFBQVEsRUFBRTtRQUN2Q3lOLFVBQVUsR0FBR3pOLElBQUk7UUFDakIwTixTQUFTLEdBQUdwSSxTQUFTLE1BQU0sQ0FBQyxPQUFPeEUsUUFBUSxDQUFDSSxLQUFLLElBQUksSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHSixRQUFRLENBQUNJLEtBQUssQ0FBQ2UsUUFBUSxDQUFDckMsUUFBUSxDQUFDLENBQUMsSUFBSSxPQUFPLEdBQUcsS0FBSyxDQUFDLEdBQUcsTUFBTSxHQUFHLE9BQU87TUFDaEosQ0FBQyxNQUFNO1FBQ0w4TixTQUFTLEdBQUcxTixJQUFJO1FBQ2hCeU4sVUFBVSxHQUFHbkksU0FBUyxLQUFLLEtBQUssR0FBRyxLQUFLLEdBQUcsUUFBUTtNQUNyRDtNQUNBLE1BQU1xSSxxQkFBcUIsR0FBR3BOLE1BQU0sR0FBRzhGLFFBQVEsQ0FBQzFDLEdBQUcsR0FBRzBDLFFBQVEsQ0FBQ3pDLE1BQU07TUFDckUsTUFBTWdLLG9CQUFvQixHQUFHeE4sS0FBSyxHQUFHaUcsUUFBUSxDQUFDeEMsSUFBSSxHQUFHd0MsUUFBUSxDQUFDdkMsS0FBSztNQUNuRSxNQUFNK0osdUJBQXVCLEdBQUdqUCx1REFBRyxDQUFDMkIsTUFBTSxHQUFHOEYsUUFBUSxDQUFDb0gsVUFBVSxDQUFDLEVBQUVFLHFCQUFxQixDQUFDO01BQ3pGLE1BQU1HLHNCQUFzQixHQUFHbFAsdURBQUcsQ0FBQ3dCLEtBQUssR0FBR2lHLFFBQVEsQ0FBQ3FILFNBQVMsQ0FBQyxFQUFFRSxvQkFBb0IsQ0FBQztNQUNyRixNQUFNRyxPQUFPLEdBQUcsQ0FBQzNMLEtBQUssQ0FBQ2QsY0FBYyxDQUFDMkssS0FBSztNQUMzQyxJQUFJK0IsZUFBZSxHQUFHSCx1QkFBdUI7TUFDN0MsSUFBSUksY0FBYyxHQUFHSCxzQkFBc0I7TUFDM0MsSUFBSSxDQUFDVixxQkFBcUIsR0FBR2hMLEtBQUssQ0FBQ2QsY0FBYyxDQUFDMkssS0FBSyxLQUFLLElBQUksSUFBSW1CLHFCQUFxQixDQUFDVixPQUFPLENBQUN2TSxDQUFDLEVBQUU7UUFDbkc4TixjQUFjLEdBQUdMLG9CQUFvQjtNQUN2QztNQUNBLElBQUksQ0FBQ1Asc0JBQXNCLEdBQUdqTCxLQUFLLENBQUNkLGNBQWMsQ0FBQzJLLEtBQUssS0FBSyxJQUFJLElBQUlvQixzQkFBc0IsQ0FBQ1gsT0FBTyxDQUFDcE0sQ0FBQyxFQUFFO1FBQ3JHME4sZUFBZSxHQUFHTCxxQkFBcUI7TUFDekM7TUFDQSxJQUFJSSxPQUFPLElBQUksQ0FBQ3pJLFNBQVMsRUFBRTtRQUN6QixNQUFNNEksSUFBSSxHQUFHN08sdURBQUcsQ0FBQ2dILFFBQVEsQ0FBQ3hDLElBQUksRUFBRSxDQUFDLENBQUM7UUFDbEMsTUFBTXNLLElBQUksR0FBRzlPLHVEQUFHLENBQUNnSCxRQUFRLENBQUN2QyxLQUFLLEVBQUUsQ0FBQyxDQUFDO1FBQ25DLE1BQU1zSyxJQUFJLEdBQUcvTyx1REFBRyxDQUFDZ0gsUUFBUSxDQUFDMUMsR0FBRyxFQUFFLENBQUMsQ0FBQztRQUNqQyxNQUFNMEssSUFBSSxHQUFHaFAsdURBQUcsQ0FBQ2dILFFBQVEsQ0FBQ3pDLE1BQU0sRUFBRSxDQUFDLENBQUM7UUFDcEMsSUFBSU8sT0FBTyxFQUFFO1VBQ1g4SixjQUFjLEdBQUc3TixLQUFLLEdBQUcsQ0FBQyxJQUFJOE4sSUFBSSxLQUFLLENBQUMsSUFBSUMsSUFBSSxLQUFLLENBQUMsR0FBR0QsSUFBSSxHQUFHQyxJQUFJLEdBQUc5Tyx1REFBRyxDQUFDZ0gsUUFBUSxDQUFDeEMsSUFBSSxFQUFFd0MsUUFBUSxDQUFDdkMsS0FBSyxDQUFDLENBQUM7UUFDNUcsQ0FBQyxNQUFNO1VBQ0xrSyxlQUFlLEdBQUd6TixNQUFNLEdBQUcsQ0FBQyxJQUFJNk4sSUFBSSxLQUFLLENBQUMsSUFBSUMsSUFBSSxLQUFLLENBQUMsR0FBR0QsSUFBSSxHQUFHQyxJQUFJLEdBQUdoUCx1REFBRyxDQUFDZ0gsUUFBUSxDQUFDMUMsR0FBRyxFQUFFMEMsUUFBUSxDQUFDekMsTUFBTSxDQUFDLENBQUM7UUFDOUc7TUFDRjtNQUNBLE1BQU0ySixLQUFLLENBQUFyTCxhQUFBLENBQUFBLGFBQUEsS0FDTkUsS0FBSztRQUNSNkwsY0FBYztRQUNkRDtNQUFlLEVBQ2hCLENBQUM7TUFDRixNQUFNTSxjQUFjLEdBQUcsTUFBTXhOLFFBQVEsQ0FBQ29ELGFBQWEsQ0FBQ2pDLFFBQVEsQ0FBQ3JDLFFBQVEsQ0FBQztNQUN0RSxJQUFJUSxLQUFLLEtBQUtrTyxjQUFjLENBQUNsTyxLQUFLLElBQUlHLE1BQU0sS0FBSytOLGNBQWMsQ0FBQy9OLE1BQU0sRUFBRTtRQUN0RSxPQUFPO1VBQ0x3QixLQUFLLEVBQUU7WUFDTFosS0FBSyxFQUFFO1VBQ1Q7UUFDRixDQUFDO01BQ0g7TUFDQSxPQUFPLENBQUMsQ0FBQztJQUNYO0VBQ0YsQ0FBQztBQUNILENBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXE9wb3NJIHY3XFxub2RlX21vZHVsZXNcXEBmbG9hdGluZy11aVxcY29yZVxcZGlzdFxcZmxvYXRpbmctdWkuY29yZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ2V0U2lkZUF4aXMsIGdldEFsaWdubWVudEF4aXMsIGdldEF4aXNMZW5ndGgsIGdldFNpZGUsIGdldEFsaWdubWVudCwgZXZhbHVhdGUsIGdldFBhZGRpbmdPYmplY3QsIHJlY3RUb0NsaWVudFJlY3QsIG1pbiwgY2xhbXAsIHBsYWNlbWVudHMsIGdldEFsaWdubWVudFNpZGVzLCBnZXRPcHBvc2l0ZUFsaWdubWVudFBsYWNlbWVudCwgZ2V0T3Bwb3NpdGVQbGFjZW1lbnQsIGdldEV4cGFuZGVkUGxhY2VtZW50cywgZ2V0T3Bwb3NpdGVBeGlzUGxhY2VtZW50cywgc2lkZXMsIG1heCwgZ2V0T3Bwb3NpdGVBeGlzIH0gZnJvbSAnQGZsb2F0aW5nLXVpL3V0aWxzJztcbmV4cG9ydCB7IHJlY3RUb0NsaWVudFJlY3QgfSBmcm9tICdAZmxvYXRpbmctdWkvdXRpbHMnO1xuXG5mdW5jdGlvbiBjb21wdXRlQ29vcmRzRnJvbVBsYWNlbWVudChfcmVmLCBwbGFjZW1lbnQsIHJ0bCkge1xuICBsZXQge1xuICAgIHJlZmVyZW5jZSxcbiAgICBmbG9hdGluZ1xuICB9ID0gX3JlZjtcbiAgY29uc3Qgc2lkZUF4aXMgPSBnZXRTaWRlQXhpcyhwbGFjZW1lbnQpO1xuICBjb25zdCBhbGlnbm1lbnRBeGlzID0gZ2V0QWxpZ25tZW50QXhpcyhwbGFjZW1lbnQpO1xuICBjb25zdCBhbGlnbkxlbmd0aCA9IGdldEF4aXNMZW5ndGgoYWxpZ25tZW50QXhpcyk7XG4gIGNvbnN0IHNpZGUgPSBnZXRTaWRlKHBsYWNlbWVudCk7XG4gIGNvbnN0IGlzVmVydGljYWwgPSBzaWRlQXhpcyA9PT0gJ3knO1xuICBjb25zdCBjb21tb25YID0gcmVmZXJlbmNlLnggKyByZWZlcmVuY2Uud2lkdGggLyAyIC0gZmxvYXRpbmcud2lkdGggLyAyO1xuICBjb25zdCBjb21tb25ZID0gcmVmZXJlbmNlLnkgKyByZWZlcmVuY2UuaGVpZ2h0IC8gMiAtIGZsb2F0aW5nLmhlaWdodCAvIDI7XG4gIGNvbnN0IGNvbW1vbkFsaWduID0gcmVmZXJlbmNlW2FsaWduTGVuZ3RoXSAvIDIgLSBmbG9hdGluZ1thbGlnbkxlbmd0aF0gLyAyO1xuICBsZXQgY29vcmRzO1xuICBzd2l0Y2ggKHNpZGUpIHtcbiAgICBjYXNlICd0b3AnOlxuICAgICAgY29vcmRzID0ge1xuICAgICAgICB4OiBjb21tb25YLFxuICAgICAgICB5OiByZWZlcmVuY2UueSAtIGZsb2F0aW5nLmhlaWdodFxuICAgICAgfTtcbiAgICAgIGJyZWFrO1xuICAgIGNhc2UgJ2JvdHRvbSc6XG4gICAgICBjb29yZHMgPSB7XG4gICAgICAgIHg6IGNvbW1vblgsXG4gICAgICAgIHk6IHJlZmVyZW5jZS55ICsgcmVmZXJlbmNlLmhlaWdodFxuICAgICAgfTtcbiAgICAgIGJyZWFrO1xuICAgIGNhc2UgJ3JpZ2h0JzpcbiAgICAgIGNvb3JkcyA9IHtcbiAgICAgICAgeDogcmVmZXJlbmNlLnggKyByZWZlcmVuY2Uud2lkdGgsXG4gICAgICAgIHk6IGNvbW1vbllcbiAgICAgIH07XG4gICAgICBicmVhaztcbiAgICBjYXNlICdsZWZ0JzpcbiAgICAgIGNvb3JkcyA9IHtcbiAgICAgICAgeDogcmVmZXJlbmNlLnggLSBmbG9hdGluZy53aWR0aCxcbiAgICAgICAgeTogY29tbW9uWVxuICAgICAgfTtcbiAgICAgIGJyZWFrO1xuICAgIGRlZmF1bHQ6XG4gICAgICBjb29yZHMgPSB7XG4gICAgICAgIHg6IHJlZmVyZW5jZS54LFxuICAgICAgICB5OiByZWZlcmVuY2UueVxuICAgICAgfTtcbiAgfVxuICBzd2l0Y2ggKGdldEFsaWdubWVudChwbGFjZW1lbnQpKSB7XG4gICAgY2FzZSAnc3RhcnQnOlxuICAgICAgY29vcmRzW2FsaWdubWVudEF4aXNdIC09IGNvbW1vbkFsaWduICogKHJ0bCAmJiBpc1ZlcnRpY2FsID8gLTEgOiAxKTtcbiAgICAgIGJyZWFrO1xuICAgIGNhc2UgJ2VuZCc6XG4gICAgICBjb29yZHNbYWxpZ25tZW50QXhpc10gKz0gY29tbW9uQWxpZ24gKiAocnRsICYmIGlzVmVydGljYWwgPyAtMSA6IDEpO1xuICAgICAgYnJlYWs7XG4gIH1cbiAgcmV0dXJuIGNvb3Jkcztcbn1cblxuLyoqXG4gKiBDb21wdXRlcyB0aGUgYHhgIGFuZCBgeWAgY29vcmRpbmF0ZXMgdGhhdCB3aWxsIHBsYWNlIHRoZSBmbG9hdGluZyBlbGVtZW50XG4gKiBuZXh0IHRvIGEgZ2l2ZW4gcmVmZXJlbmNlIGVsZW1lbnQuXG4gKlxuICogVGhpcyBleHBvcnQgZG9lcyBub3QgaGF2ZSBhbnkgYHBsYXRmb3JtYCBpbnRlcmZhY2UgbG9naWMuIFlvdSB3aWxsIG5lZWQgdG9cbiAqIHdyaXRlIG9uZSBmb3IgdGhlIHBsYXRmb3JtIHlvdSBhcmUgdXNpbmcgRmxvYXRpbmcgVUkgd2l0aC5cbiAqL1xuY29uc3QgY29tcHV0ZVBvc2l0aW9uID0gYXN5bmMgKHJlZmVyZW5jZSwgZmxvYXRpbmcsIGNvbmZpZykgPT4ge1xuICBjb25zdCB7XG4gICAgcGxhY2VtZW50ID0gJ2JvdHRvbScsXG4gICAgc3RyYXRlZ3kgPSAnYWJzb2x1dGUnLFxuICAgIG1pZGRsZXdhcmUgPSBbXSxcbiAgICBwbGF0Zm9ybVxuICB9ID0gY29uZmlnO1xuICBjb25zdCB2YWxpZE1pZGRsZXdhcmUgPSBtaWRkbGV3YXJlLmZpbHRlcihCb29sZWFuKTtcbiAgY29uc3QgcnRsID0gYXdhaXQgKHBsYXRmb3JtLmlzUlRMID09IG51bGwgPyB2b2lkIDAgOiBwbGF0Zm9ybS5pc1JUTChmbG9hdGluZykpO1xuICBsZXQgcmVjdHMgPSBhd2FpdCBwbGF0Zm9ybS5nZXRFbGVtZW50UmVjdHMoe1xuICAgIHJlZmVyZW5jZSxcbiAgICBmbG9hdGluZyxcbiAgICBzdHJhdGVneVxuICB9KTtcbiAgbGV0IHtcbiAgICB4LFxuICAgIHlcbiAgfSA9IGNvbXB1dGVDb29yZHNGcm9tUGxhY2VtZW50KHJlY3RzLCBwbGFjZW1lbnQsIHJ0bCk7XG4gIGxldCBzdGF0ZWZ1bFBsYWNlbWVudCA9IHBsYWNlbWVudDtcbiAgbGV0IG1pZGRsZXdhcmVEYXRhID0ge307XG4gIGxldCByZXNldENvdW50ID0gMDtcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCB2YWxpZE1pZGRsZXdhcmUubGVuZ3RoOyBpKyspIHtcbiAgICBjb25zdCB7XG4gICAgICBuYW1lLFxuICAgICAgZm5cbiAgICB9ID0gdmFsaWRNaWRkbGV3YXJlW2ldO1xuICAgIGNvbnN0IHtcbiAgICAgIHg6IG5leHRYLFxuICAgICAgeTogbmV4dFksXG4gICAgICBkYXRhLFxuICAgICAgcmVzZXRcbiAgICB9ID0gYXdhaXQgZm4oe1xuICAgICAgeCxcbiAgICAgIHksXG4gICAgICBpbml0aWFsUGxhY2VtZW50OiBwbGFjZW1lbnQsXG4gICAgICBwbGFjZW1lbnQ6IHN0YXRlZnVsUGxhY2VtZW50LFxuICAgICAgc3RyYXRlZ3ksXG4gICAgICBtaWRkbGV3YXJlRGF0YSxcbiAgICAgIHJlY3RzLFxuICAgICAgcGxhdGZvcm0sXG4gICAgICBlbGVtZW50czoge1xuICAgICAgICByZWZlcmVuY2UsXG4gICAgICAgIGZsb2F0aW5nXG4gICAgICB9XG4gICAgfSk7XG4gICAgeCA9IG5leHRYICE9IG51bGwgPyBuZXh0WCA6IHg7XG4gICAgeSA9IG5leHRZICE9IG51bGwgPyBuZXh0WSA6IHk7XG4gICAgbWlkZGxld2FyZURhdGEgPSB7XG4gICAgICAuLi5taWRkbGV3YXJlRGF0YSxcbiAgICAgIFtuYW1lXToge1xuICAgICAgICAuLi5taWRkbGV3YXJlRGF0YVtuYW1lXSxcbiAgICAgICAgLi4uZGF0YVxuICAgICAgfVxuICAgIH07XG4gICAgaWYgKHJlc2V0ICYmIHJlc2V0Q291bnQgPD0gNTApIHtcbiAgICAgIHJlc2V0Q291bnQrKztcbiAgICAgIGlmICh0eXBlb2YgcmVzZXQgPT09ICdvYmplY3QnKSB7XG4gICAgICAgIGlmIChyZXNldC5wbGFjZW1lbnQpIHtcbiAgICAgICAgICBzdGF0ZWZ1bFBsYWNlbWVudCA9IHJlc2V0LnBsYWNlbWVudDtcbiAgICAgICAgfVxuICAgICAgICBpZiAocmVzZXQucmVjdHMpIHtcbiAgICAgICAgICByZWN0cyA9IHJlc2V0LnJlY3RzID09PSB0cnVlID8gYXdhaXQgcGxhdGZvcm0uZ2V0RWxlbWVudFJlY3RzKHtcbiAgICAgICAgICAgIHJlZmVyZW5jZSxcbiAgICAgICAgICAgIGZsb2F0aW5nLFxuICAgICAgICAgICAgc3RyYXRlZ3lcbiAgICAgICAgICB9KSA6IHJlc2V0LnJlY3RzO1xuICAgICAgICB9XG4gICAgICAgICh7XG4gICAgICAgICAgeCxcbiAgICAgICAgICB5XG4gICAgICAgIH0gPSBjb21wdXRlQ29vcmRzRnJvbVBsYWNlbWVudChyZWN0cywgc3RhdGVmdWxQbGFjZW1lbnQsIHJ0bCkpO1xuICAgICAgfVxuICAgICAgaSA9IC0xO1xuICAgIH1cbiAgfVxuICByZXR1cm4ge1xuICAgIHgsXG4gICAgeSxcbiAgICBwbGFjZW1lbnQ6IHN0YXRlZnVsUGxhY2VtZW50LFxuICAgIHN0cmF0ZWd5LFxuICAgIG1pZGRsZXdhcmVEYXRhXG4gIH07XG59O1xuXG4vKipcbiAqIFJlc29sdmVzIHdpdGggYW4gb2JqZWN0IG9mIG92ZXJmbG93IHNpZGUgb2Zmc2V0cyB0aGF0IGRldGVybWluZSBob3cgbXVjaCB0aGVcbiAqIGVsZW1lbnQgaXMgb3ZlcmZsb3dpbmcgYSBnaXZlbiBjbGlwcGluZyBib3VuZGFyeSBvbiBlYWNoIHNpZGUuXG4gKiAtIHBvc2l0aXZlID0gb3ZlcmZsb3dpbmcgdGhlIGJvdW5kYXJ5IGJ5IHRoYXQgbnVtYmVyIG9mIHBpeGVsc1xuICogLSBuZWdhdGl2ZSA9IGhvdyBtYW55IHBpeGVscyBsZWZ0IGJlZm9yZSBpdCB3aWxsIG92ZXJmbG93XG4gKiAtIDAgPSBsaWVzIGZsdXNoIHdpdGggdGhlIGJvdW5kYXJ5XG4gKiBAc2VlIGh0dHBzOi8vZmxvYXRpbmctdWkuY29tL2RvY3MvZGV0ZWN0T3ZlcmZsb3dcbiAqL1xuYXN5bmMgZnVuY3Rpb24gZGV0ZWN0T3ZlcmZsb3coc3RhdGUsIG9wdGlvbnMpIHtcbiAgdmFyIF9hd2FpdCRwbGF0Zm9ybSRpc0VsZTtcbiAgaWYgKG9wdGlvbnMgPT09IHZvaWQgMCkge1xuICAgIG9wdGlvbnMgPSB7fTtcbiAgfVxuICBjb25zdCB7XG4gICAgeCxcbiAgICB5LFxuICAgIHBsYXRmb3JtLFxuICAgIHJlY3RzLFxuICAgIGVsZW1lbnRzLFxuICAgIHN0cmF0ZWd5XG4gIH0gPSBzdGF0ZTtcbiAgY29uc3Qge1xuICAgIGJvdW5kYXJ5ID0gJ2NsaXBwaW5nQW5jZXN0b3JzJyxcbiAgICByb290Qm91bmRhcnkgPSAndmlld3BvcnQnLFxuICAgIGVsZW1lbnRDb250ZXh0ID0gJ2Zsb2F0aW5nJyxcbiAgICBhbHRCb3VuZGFyeSA9IGZhbHNlLFxuICAgIHBhZGRpbmcgPSAwXG4gIH0gPSBldmFsdWF0ZShvcHRpb25zLCBzdGF0ZSk7XG4gIGNvbnN0IHBhZGRpbmdPYmplY3QgPSBnZXRQYWRkaW5nT2JqZWN0KHBhZGRpbmcpO1xuICBjb25zdCBhbHRDb250ZXh0ID0gZWxlbWVudENvbnRleHQgPT09ICdmbG9hdGluZycgPyAncmVmZXJlbmNlJyA6ICdmbG9hdGluZyc7XG4gIGNvbnN0IGVsZW1lbnQgPSBlbGVtZW50c1thbHRCb3VuZGFyeSA/IGFsdENvbnRleHQgOiBlbGVtZW50Q29udGV4dF07XG4gIGNvbnN0IGNsaXBwaW5nQ2xpZW50UmVjdCA9IHJlY3RUb0NsaWVudFJlY3QoYXdhaXQgcGxhdGZvcm0uZ2V0Q2xpcHBpbmdSZWN0KHtcbiAgICBlbGVtZW50OiAoKF9hd2FpdCRwbGF0Zm9ybSRpc0VsZSA9IGF3YWl0IChwbGF0Zm9ybS5pc0VsZW1lbnQgPT0gbnVsbCA/IHZvaWQgMCA6IHBsYXRmb3JtLmlzRWxlbWVudChlbGVtZW50KSkpICE9IG51bGwgPyBfYXdhaXQkcGxhdGZvcm0kaXNFbGUgOiB0cnVlKSA/IGVsZW1lbnQgOiBlbGVtZW50LmNvbnRleHRFbGVtZW50IHx8IChhd2FpdCAocGxhdGZvcm0uZ2V0RG9jdW1lbnRFbGVtZW50ID09IG51bGwgPyB2b2lkIDAgOiBwbGF0Zm9ybS5nZXREb2N1bWVudEVsZW1lbnQoZWxlbWVudHMuZmxvYXRpbmcpKSksXG4gICAgYm91bmRhcnksXG4gICAgcm9vdEJvdW5kYXJ5LFxuICAgIHN0cmF0ZWd5XG4gIH0pKTtcbiAgY29uc3QgcmVjdCA9IGVsZW1lbnRDb250ZXh0ID09PSAnZmxvYXRpbmcnID8ge1xuICAgIHgsXG4gICAgeSxcbiAgICB3aWR0aDogcmVjdHMuZmxvYXRpbmcud2lkdGgsXG4gICAgaGVpZ2h0OiByZWN0cy5mbG9hdGluZy5oZWlnaHRcbiAgfSA6IHJlY3RzLnJlZmVyZW5jZTtcbiAgY29uc3Qgb2Zmc2V0UGFyZW50ID0gYXdhaXQgKHBsYXRmb3JtLmdldE9mZnNldFBhcmVudCA9PSBudWxsID8gdm9pZCAwIDogcGxhdGZvcm0uZ2V0T2Zmc2V0UGFyZW50KGVsZW1lbnRzLmZsb2F0aW5nKSk7XG4gIGNvbnN0IG9mZnNldFNjYWxlID0gKGF3YWl0IChwbGF0Zm9ybS5pc0VsZW1lbnQgPT0gbnVsbCA/IHZvaWQgMCA6IHBsYXRmb3JtLmlzRWxlbWVudChvZmZzZXRQYXJlbnQpKSkgPyAoYXdhaXQgKHBsYXRmb3JtLmdldFNjYWxlID09IG51bGwgPyB2b2lkIDAgOiBwbGF0Zm9ybS5nZXRTY2FsZShvZmZzZXRQYXJlbnQpKSkgfHwge1xuICAgIHg6IDEsXG4gICAgeTogMVxuICB9IDoge1xuICAgIHg6IDEsXG4gICAgeTogMVxuICB9O1xuICBjb25zdCBlbGVtZW50Q2xpZW50UmVjdCA9IHJlY3RUb0NsaWVudFJlY3QocGxhdGZvcm0uY29udmVydE9mZnNldFBhcmVudFJlbGF0aXZlUmVjdFRvVmlld3BvcnRSZWxhdGl2ZVJlY3QgPyBhd2FpdCBwbGF0Zm9ybS5jb252ZXJ0T2Zmc2V0UGFyZW50UmVsYXRpdmVSZWN0VG9WaWV3cG9ydFJlbGF0aXZlUmVjdCh7XG4gICAgZWxlbWVudHMsXG4gICAgcmVjdCxcbiAgICBvZmZzZXRQYXJlbnQsXG4gICAgc3RyYXRlZ3lcbiAgfSkgOiByZWN0KTtcbiAgcmV0dXJuIHtcbiAgICB0b3A6IChjbGlwcGluZ0NsaWVudFJlY3QudG9wIC0gZWxlbWVudENsaWVudFJlY3QudG9wICsgcGFkZGluZ09iamVjdC50b3ApIC8gb2Zmc2V0U2NhbGUueSxcbiAgICBib3R0b206IChlbGVtZW50Q2xpZW50UmVjdC5ib3R0b20gLSBjbGlwcGluZ0NsaWVudFJlY3QuYm90dG9tICsgcGFkZGluZ09iamVjdC5ib3R0b20pIC8gb2Zmc2V0U2NhbGUueSxcbiAgICBsZWZ0OiAoY2xpcHBpbmdDbGllbnRSZWN0LmxlZnQgLSBlbGVtZW50Q2xpZW50UmVjdC5sZWZ0ICsgcGFkZGluZ09iamVjdC5sZWZ0KSAvIG9mZnNldFNjYWxlLngsXG4gICAgcmlnaHQ6IChlbGVtZW50Q2xpZW50UmVjdC5yaWdodCAtIGNsaXBwaW5nQ2xpZW50UmVjdC5yaWdodCArIHBhZGRpbmdPYmplY3QucmlnaHQpIC8gb2Zmc2V0U2NhbGUueFxuICB9O1xufVxuXG4vKipcbiAqIFByb3ZpZGVzIGRhdGEgdG8gcG9zaXRpb24gYW4gaW5uZXIgZWxlbWVudCBvZiB0aGUgZmxvYXRpbmcgZWxlbWVudCBzbyB0aGF0IGl0XG4gKiBhcHBlYXJzIGNlbnRlcmVkIHRvIHRoZSByZWZlcmVuY2UgZWxlbWVudC5cbiAqIEBzZWUgaHR0cHM6Ly9mbG9hdGluZy11aS5jb20vZG9jcy9hcnJvd1xuICovXG5jb25zdCBhcnJvdyA9IG9wdGlvbnMgPT4gKHtcbiAgbmFtZTogJ2Fycm93JyxcbiAgb3B0aW9ucyxcbiAgYXN5bmMgZm4oc3RhdGUpIHtcbiAgICBjb25zdCB7XG4gICAgICB4LFxuICAgICAgeSxcbiAgICAgIHBsYWNlbWVudCxcbiAgICAgIHJlY3RzLFxuICAgICAgcGxhdGZvcm0sXG4gICAgICBlbGVtZW50cyxcbiAgICAgIG1pZGRsZXdhcmVEYXRhXG4gICAgfSA9IHN0YXRlO1xuICAgIC8vIFNpbmNlIGBlbGVtZW50YCBpcyByZXF1aXJlZCwgd2UgZG9uJ3QgUGFydGlhbDw+IHRoZSB0eXBlLlxuICAgIGNvbnN0IHtcbiAgICAgIGVsZW1lbnQsXG4gICAgICBwYWRkaW5nID0gMFxuICAgIH0gPSBldmFsdWF0ZShvcHRpb25zLCBzdGF0ZSkgfHwge307XG4gICAgaWYgKGVsZW1lbnQgPT0gbnVsbCkge1xuICAgICAgcmV0dXJuIHt9O1xuICAgIH1cbiAgICBjb25zdCBwYWRkaW5nT2JqZWN0ID0gZ2V0UGFkZGluZ09iamVjdChwYWRkaW5nKTtcbiAgICBjb25zdCBjb29yZHMgPSB7XG4gICAgICB4LFxuICAgICAgeVxuICAgIH07XG4gICAgY29uc3QgYXhpcyA9IGdldEFsaWdubWVudEF4aXMocGxhY2VtZW50KTtcbiAgICBjb25zdCBsZW5ndGggPSBnZXRBeGlzTGVuZ3RoKGF4aXMpO1xuICAgIGNvbnN0IGFycm93RGltZW5zaW9ucyA9IGF3YWl0IHBsYXRmb3JtLmdldERpbWVuc2lvbnMoZWxlbWVudCk7XG4gICAgY29uc3QgaXNZQXhpcyA9IGF4aXMgPT09ICd5JztcbiAgICBjb25zdCBtaW5Qcm9wID0gaXNZQXhpcyA/ICd0b3AnIDogJ2xlZnQnO1xuICAgIGNvbnN0IG1heFByb3AgPSBpc1lBeGlzID8gJ2JvdHRvbScgOiAncmlnaHQnO1xuICAgIGNvbnN0IGNsaWVudFByb3AgPSBpc1lBeGlzID8gJ2NsaWVudEhlaWdodCcgOiAnY2xpZW50V2lkdGgnO1xuICAgIGNvbnN0IGVuZERpZmYgPSByZWN0cy5yZWZlcmVuY2VbbGVuZ3RoXSArIHJlY3RzLnJlZmVyZW5jZVtheGlzXSAtIGNvb3Jkc1theGlzXSAtIHJlY3RzLmZsb2F0aW5nW2xlbmd0aF07XG4gICAgY29uc3Qgc3RhcnREaWZmID0gY29vcmRzW2F4aXNdIC0gcmVjdHMucmVmZXJlbmNlW2F4aXNdO1xuICAgIGNvbnN0IGFycm93T2Zmc2V0UGFyZW50ID0gYXdhaXQgKHBsYXRmb3JtLmdldE9mZnNldFBhcmVudCA9PSBudWxsID8gdm9pZCAwIDogcGxhdGZvcm0uZ2V0T2Zmc2V0UGFyZW50KGVsZW1lbnQpKTtcbiAgICBsZXQgY2xpZW50U2l6ZSA9IGFycm93T2Zmc2V0UGFyZW50ID8gYXJyb3dPZmZzZXRQYXJlbnRbY2xpZW50UHJvcF0gOiAwO1xuXG4gICAgLy8gRE9NIHBsYXRmb3JtIGNhbiByZXR1cm4gYHdpbmRvd2AgYXMgdGhlIGBvZmZzZXRQYXJlbnRgLlxuICAgIGlmICghY2xpZW50U2l6ZSB8fCAhKGF3YWl0IChwbGF0Zm9ybS5pc0VsZW1lbnQgPT0gbnVsbCA/IHZvaWQgMCA6IHBsYXRmb3JtLmlzRWxlbWVudChhcnJvd09mZnNldFBhcmVudCkpKSkge1xuICAgICAgY2xpZW50U2l6ZSA9IGVsZW1lbnRzLmZsb2F0aW5nW2NsaWVudFByb3BdIHx8IHJlY3RzLmZsb2F0aW5nW2xlbmd0aF07XG4gICAgfVxuICAgIGNvbnN0IGNlbnRlclRvUmVmZXJlbmNlID0gZW5kRGlmZiAvIDIgLSBzdGFydERpZmYgLyAyO1xuXG4gICAgLy8gSWYgdGhlIHBhZGRpbmcgaXMgbGFyZ2UgZW5vdWdoIHRoYXQgaXQgY2F1c2VzIHRoZSBhcnJvdyB0byBubyBsb25nZXIgYmVcbiAgICAvLyBjZW50ZXJlZCwgbW9kaWZ5IHRoZSBwYWRkaW5nIHNvIHRoYXQgaXQgaXMgY2VudGVyZWQuXG4gICAgY29uc3QgbGFyZ2VzdFBvc3NpYmxlUGFkZGluZyA9IGNsaWVudFNpemUgLyAyIC0gYXJyb3dEaW1lbnNpb25zW2xlbmd0aF0gLyAyIC0gMTtcbiAgICBjb25zdCBtaW5QYWRkaW5nID0gbWluKHBhZGRpbmdPYmplY3RbbWluUHJvcF0sIGxhcmdlc3RQb3NzaWJsZVBhZGRpbmcpO1xuICAgIGNvbnN0IG1heFBhZGRpbmcgPSBtaW4ocGFkZGluZ09iamVjdFttYXhQcm9wXSwgbGFyZ2VzdFBvc3NpYmxlUGFkZGluZyk7XG5cbiAgICAvLyBNYWtlIHN1cmUgdGhlIGFycm93IGRvZXNuJ3Qgb3ZlcmZsb3cgdGhlIGZsb2F0aW5nIGVsZW1lbnQgaWYgdGhlIGNlbnRlclxuICAgIC8vIHBvaW50IGlzIG91dHNpZGUgdGhlIGZsb2F0aW5nIGVsZW1lbnQncyBib3VuZHMuXG4gICAgY29uc3QgbWluJDEgPSBtaW5QYWRkaW5nO1xuICAgIGNvbnN0IG1heCA9IGNsaWVudFNpemUgLSBhcnJvd0RpbWVuc2lvbnNbbGVuZ3RoXSAtIG1heFBhZGRpbmc7XG4gICAgY29uc3QgY2VudGVyID0gY2xpZW50U2l6ZSAvIDIgLSBhcnJvd0RpbWVuc2lvbnNbbGVuZ3RoXSAvIDIgKyBjZW50ZXJUb1JlZmVyZW5jZTtcbiAgICBjb25zdCBvZmZzZXQgPSBjbGFtcChtaW4kMSwgY2VudGVyLCBtYXgpO1xuXG4gICAgLy8gSWYgdGhlIHJlZmVyZW5jZSBpcyBzbWFsbCBlbm91Z2ggdGhhdCB0aGUgYXJyb3cncyBwYWRkaW5nIGNhdXNlcyBpdCB0b1xuICAgIC8vIHRvIHBvaW50IHRvIG5vdGhpbmcgZm9yIGFuIGFsaWduZWQgcGxhY2VtZW50LCBhZGp1c3QgdGhlIG9mZnNldCBvZiB0aGVcbiAgICAvLyBmbG9hdGluZyBlbGVtZW50IGl0c2VsZi4gVG8gZW5zdXJlIGBzaGlmdCgpYCBjb250aW51ZXMgdG8gdGFrZSBhY3Rpb24sXG4gICAgLy8gYSBzaW5nbGUgcmVzZXQgaXMgcGVyZm9ybWVkIHdoZW4gdGhpcyBpcyB0cnVlLlxuICAgIGNvbnN0IHNob3VsZEFkZE9mZnNldCA9ICFtaWRkbGV3YXJlRGF0YS5hcnJvdyAmJiBnZXRBbGlnbm1lbnQocGxhY2VtZW50KSAhPSBudWxsICYmIGNlbnRlciAhPT0gb2Zmc2V0ICYmIHJlY3RzLnJlZmVyZW5jZVtsZW5ndGhdIC8gMiAtIChjZW50ZXIgPCBtaW4kMSA/IG1pblBhZGRpbmcgOiBtYXhQYWRkaW5nKSAtIGFycm93RGltZW5zaW9uc1tsZW5ndGhdIC8gMiA8IDA7XG4gICAgY29uc3QgYWxpZ25tZW50T2Zmc2V0ID0gc2hvdWxkQWRkT2Zmc2V0ID8gY2VudGVyIDwgbWluJDEgPyBjZW50ZXIgLSBtaW4kMSA6IGNlbnRlciAtIG1heCA6IDA7XG4gICAgcmV0dXJuIHtcbiAgICAgIFtheGlzXTogY29vcmRzW2F4aXNdICsgYWxpZ25tZW50T2Zmc2V0LFxuICAgICAgZGF0YToge1xuICAgICAgICBbYXhpc106IG9mZnNldCxcbiAgICAgICAgY2VudGVyT2Zmc2V0OiBjZW50ZXIgLSBvZmZzZXQgLSBhbGlnbm1lbnRPZmZzZXQsXG4gICAgICAgIC4uLihzaG91bGRBZGRPZmZzZXQgJiYge1xuICAgICAgICAgIGFsaWdubWVudE9mZnNldFxuICAgICAgICB9KVxuICAgICAgfSxcbiAgICAgIHJlc2V0OiBzaG91bGRBZGRPZmZzZXRcbiAgICB9O1xuICB9XG59KTtcblxuZnVuY3Rpb24gZ2V0UGxhY2VtZW50TGlzdChhbGlnbm1lbnQsIGF1dG9BbGlnbm1lbnQsIGFsbG93ZWRQbGFjZW1lbnRzKSB7XG4gIGNvbnN0IGFsbG93ZWRQbGFjZW1lbnRzU29ydGVkQnlBbGlnbm1lbnQgPSBhbGlnbm1lbnQgPyBbLi4uYWxsb3dlZFBsYWNlbWVudHMuZmlsdGVyKHBsYWNlbWVudCA9PiBnZXRBbGlnbm1lbnQocGxhY2VtZW50KSA9PT0gYWxpZ25tZW50KSwgLi4uYWxsb3dlZFBsYWNlbWVudHMuZmlsdGVyKHBsYWNlbWVudCA9PiBnZXRBbGlnbm1lbnQocGxhY2VtZW50KSAhPT0gYWxpZ25tZW50KV0gOiBhbGxvd2VkUGxhY2VtZW50cy5maWx0ZXIocGxhY2VtZW50ID0+IGdldFNpZGUocGxhY2VtZW50KSA9PT0gcGxhY2VtZW50KTtcbiAgcmV0dXJuIGFsbG93ZWRQbGFjZW1lbnRzU29ydGVkQnlBbGlnbm1lbnQuZmlsdGVyKHBsYWNlbWVudCA9PiB7XG4gICAgaWYgKGFsaWdubWVudCkge1xuICAgICAgcmV0dXJuIGdldEFsaWdubWVudChwbGFjZW1lbnQpID09PSBhbGlnbm1lbnQgfHwgKGF1dG9BbGlnbm1lbnQgPyBnZXRPcHBvc2l0ZUFsaWdubWVudFBsYWNlbWVudChwbGFjZW1lbnQpICE9PSBwbGFjZW1lbnQgOiBmYWxzZSk7XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xuICB9KTtcbn1cbi8qKlxuICogT3B0aW1pemVzIHRoZSB2aXNpYmlsaXR5IG9mIHRoZSBmbG9hdGluZyBlbGVtZW50IGJ5IGNob29zaW5nIHRoZSBwbGFjZW1lbnRcbiAqIHRoYXQgaGFzIHRoZSBtb3N0IHNwYWNlIGF2YWlsYWJsZSBhdXRvbWF0aWNhbGx5LCB3aXRob3V0IG5lZWRpbmcgdG8gc3BlY2lmeSBhXG4gKiBwcmVmZXJyZWQgcGxhY2VtZW50LiBBbHRlcm5hdGl2ZSB0byBgZmxpcGAuXG4gKiBAc2VlIGh0dHBzOi8vZmxvYXRpbmctdWkuY29tL2RvY3MvYXV0b1BsYWNlbWVudFxuICovXG5jb25zdCBhdXRvUGxhY2VtZW50ID0gZnVuY3Rpb24gKG9wdGlvbnMpIHtcbiAgaWYgKG9wdGlvbnMgPT09IHZvaWQgMCkge1xuICAgIG9wdGlvbnMgPSB7fTtcbiAgfVxuICByZXR1cm4ge1xuICAgIG5hbWU6ICdhdXRvUGxhY2VtZW50JyxcbiAgICBvcHRpb25zLFxuICAgIGFzeW5jIGZuKHN0YXRlKSB7XG4gICAgICB2YXIgX21pZGRsZXdhcmVEYXRhJGF1dG9QLCBfbWlkZGxld2FyZURhdGEkYXV0b1AyLCBfcGxhY2VtZW50c1RoYXRGaXRPbkU7XG4gICAgICBjb25zdCB7XG4gICAgICAgIHJlY3RzLFxuICAgICAgICBtaWRkbGV3YXJlRGF0YSxcbiAgICAgICAgcGxhY2VtZW50LFxuICAgICAgICBwbGF0Zm9ybSxcbiAgICAgICAgZWxlbWVudHNcbiAgICAgIH0gPSBzdGF0ZTtcbiAgICAgIGNvbnN0IHtcbiAgICAgICAgY3Jvc3NBeGlzID0gZmFsc2UsXG4gICAgICAgIGFsaWdubWVudCxcbiAgICAgICAgYWxsb3dlZFBsYWNlbWVudHMgPSBwbGFjZW1lbnRzLFxuICAgICAgICBhdXRvQWxpZ25tZW50ID0gdHJ1ZSxcbiAgICAgICAgLi4uZGV0ZWN0T3ZlcmZsb3dPcHRpb25zXG4gICAgICB9ID0gZXZhbHVhdGUob3B0aW9ucywgc3RhdGUpO1xuICAgICAgY29uc3QgcGxhY2VtZW50cyQxID0gYWxpZ25tZW50ICE9PSB1bmRlZmluZWQgfHwgYWxsb3dlZFBsYWNlbWVudHMgPT09IHBsYWNlbWVudHMgPyBnZXRQbGFjZW1lbnRMaXN0KGFsaWdubWVudCB8fCBudWxsLCBhdXRvQWxpZ25tZW50LCBhbGxvd2VkUGxhY2VtZW50cykgOiBhbGxvd2VkUGxhY2VtZW50cztcbiAgICAgIGNvbnN0IG92ZXJmbG93ID0gYXdhaXQgZGV0ZWN0T3ZlcmZsb3coc3RhdGUsIGRldGVjdE92ZXJmbG93T3B0aW9ucyk7XG4gICAgICBjb25zdCBjdXJyZW50SW5kZXggPSAoKF9taWRkbGV3YXJlRGF0YSRhdXRvUCA9IG1pZGRsZXdhcmVEYXRhLmF1dG9QbGFjZW1lbnQpID09IG51bGwgPyB2b2lkIDAgOiBfbWlkZGxld2FyZURhdGEkYXV0b1AuaW5kZXgpIHx8IDA7XG4gICAgICBjb25zdCBjdXJyZW50UGxhY2VtZW50ID0gcGxhY2VtZW50cyQxW2N1cnJlbnRJbmRleF07XG4gICAgICBpZiAoY3VycmVudFBsYWNlbWVudCA9PSBudWxsKSB7XG4gICAgICAgIHJldHVybiB7fTtcbiAgICAgIH1cbiAgICAgIGNvbnN0IGFsaWdubWVudFNpZGVzID0gZ2V0QWxpZ25tZW50U2lkZXMoY3VycmVudFBsYWNlbWVudCwgcmVjdHMsIGF3YWl0IChwbGF0Zm9ybS5pc1JUTCA9PSBudWxsID8gdm9pZCAwIDogcGxhdGZvcm0uaXNSVEwoZWxlbWVudHMuZmxvYXRpbmcpKSk7XG5cbiAgICAgIC8vIE1ha2UgYGNvbXB1dGVDb29yZHNgIHN0YXJ0IGZyb20gdGhlIHJpZ2h0IHBsYWNlLlxuICAgICAgaWYgKHBsYWNlbWVudCAhPT0gY3VycmVudFBsYWNlbWVudCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHJlc2V0OiB7XG4gICAgICAgICAgICBwbGFjZW1lbnQ6IHBsYWNlbWVudHMkMVswXVxuICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgIH1cbiAgICAgIGNvbnN0IGN1cnJlbnRPdmVyZmxvd3MgPSBbb3ZlcmZsb3dbZ2V0U2lkZShjdXJyZW50UGxhY2VtZW50KV0sIG92ZXJmbG93W2FsaWdubWVudFNpZGVzWzBdXSwgb3ZlcmZsb3dbYWxpZ25tZW50U2lkZXNbMV1dXTtcbiAgICAgIGNvbnN0IGFsbE92ZXJmbG93cyA9IFsuLi4oKChfbWlkZGxld2FyZURhdGEkYXV0b1AyID0gbWlkZGxld2FyZURhdGEuYXV0b1BsYWNlbWVudCkgPT0gbnVsbCA/IHZvaWQgMCA6IF9taWRkbGV3YXJlRGF0YSRhdXRvUDIub3ZlcmZsb3dzKSB8fCBbXSksIHtcbiAgICAgICAgcGxhY2VtZW50OiBjdXJyZW50UGxhY2VtZW50LFxuICAgICAgICBvdmVyZmxvd3M6IGN1cnJlbnRPdmVyZmxvd3NcbiAgICAgIH1dO1xuICAgICAgY29uc3QgbmV4dFBsYWNlbWVudCA9IHBsYWNlbWVudHMkMVtjdXJyZW50SW5kZXggKyAxXTtcblxuICAgICAgLy8gVGhlcmUgYXJlIG1vcmUgcGxhY2VtZW50cyB0byBjaGVjay5cbiAgICAgIGlmIChuZXh0UGxhY2VtZW50KSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgZGF0YToge1xuICAgICAgICAgICAgaW5kZXg6IGN1cnJlbnRJbmRleCArIDEsXG4gICAgICAgICAgICBvdmVyZmxvd3M6IGFsbE92ZXJmbG93c1xuICAgICAgICAgIH0sXG4gICAgICAgICAgcmVzZXQ6IHtcbiAgICAgICAgICAgIHBsYWNlbWVudDogbmV4dFBsYWNlbWVudFxuICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgIH1cbiAgICAgIGNvbnN0IHBsYWNlbWVudHNTb3J0ZWRCeU1vc3RTcGFjZSA9IGFsbE92ZXJmbG93cy5tYXAoZCA9PiB7XG4gICAgICAgIGNvbnN0IGFsaWdubWVudCA9IGdldEFsaWdubWVudChkLnBsYWNlbWVudCk7XG4gICAgICAgIHJldHVybiBbZC5wbGFjZW1lbnQsIGFsaWdubWVudCAmJiBjcm9zc0F4aXMgP1xuICAgICAgICAvLyBDaGVjayBhbG9uZyB0aGUgbWFpbkF4aXMgYW5kIG1haW4gY3Jvc3NBeGlzIHNpZGUuXG4gICAgICAgIGQub3ZlcmZsb3dzLnNsaWNlKDAsIDIpLnJlZHVjZSgoYWNjLCB2KSA9PiBhY2MgKyB2LCAwKSA6XG4gICAgICAgIC8vIENoZWNrIG9ubHkgdGhlIG1haW5BeGlzLlxuICAgICAgICBkLm92ZXJmbG93c1swXSwgZC5vdmVyZmxvd3NdO1xuICAgICAgfSkuc29ydCgoYSwgYikgPT4gYVsxXSAtIGJbMV0pO1xuICAgICAgY29uc3QgcGxhY2VtZW50c1RoYXRGaXRPbkVhY2hTaWRlID0gcGxhY2VtZW50c1NvcnRlZEJ5TW9zdFNwYWNlLmZpbHRlcihkID0+IGRbMl0uc2xpY2UoMCxcbiAgICAgIC8vIEFsaWduZWQgcGxhY2VtZW50cyBzaG91bGQgbm90IGNoZWNrIHRoZWlyIG9wcG9zaXRlIGNyb3NzQXhpc1xuICAgICAgLy8gc2lkZS5cbiAgICAgIGdldEFsaWdubWVudChkWzBdKSA/IDIgOiAzKS5ldmVyeSh2ID0+IHYgPD0gMCkpO1xuICAgICAgY29uc3QgcmVzZXRQbGFjZW1lbnQgPSAoKF9wbGFjZW1lbnRzVGhhdEZpdE9uRSA9IHBsYWNlbWVudHNUaGF0Rml0T25FYWNoU2lkZVswXSkgPT0gbnVsbCA/IHZvaWQgMCA6IF9wbGFjZW1lbnRzVGhhdEZpdE9uRVswXSkgfHwgcGxhY2VtZW50c1NvcnRlZEJ5TW9zdFNwYWNlWzBdWzBdO1xuICAgICAgaWYgKHJlc2V0UGxhY2VtZW50ICE9PSBwbGFjZW1lbnQpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBkYXRhOiB7XG4gICAgICAgICAgICBpbmRleDogY3VycmVudEluZGV4ICsgMSxcbiAgICAgICAgICAgIG92ZXJmbG93czogYWxsT3ZlcmZsb3dzXG4gICAgICAgICAgfSxcbiAgICAgICAgICByZXNldDoge1xuICAgICAgICAgICAgcGxhY2VtZW50OiByZXNldFBsYWNlbWVudFxuICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgIH1cbiAgICAgIHJldHVybiB7fTtcbiAgICB9XG4gIH07XG59O1xuXG4vKipcbiAqIE9wdGltaXplcyB0aGUgdmlzaWJpbGl0eSBvZiB0aGUgZmxvYXRpbmcgZWxlbWVudCBieSBmbGlwcGluZyB0aGUgYHBsYWNlbWVudGBcbiAqIGluIG9yZGVyIHRvIGtlZXAgaXQgaW4gdmlldyB3aGVuIHRoZSBwcmVmZXJyZWQgcGxhY2VtZW50KHMpIHdpbGwgb3ZlcmZsb3cgdGhlXG4gKiBjbGlwcGluZyBib3VuZGFyeS4gQWx0ZXJuYXRpdmUgdG8gYGF1dG9QbGFjZW1lbnRgLlxuICogQHNlZSBodHRwczovL2Zsb2F0aW5nLXVpLmNvbS9kb2NzL2ZsaXBcbiAqL1xuY29uc3QgZmxpcCA9IGZ1bmN0aW9uIChvcHRpb25zKSB7XG4gIGlmIChvcHRpb25zID09PSB2b2lkIDApIHtcbiAgICBvcHRpb25zID0ge307XG4gIH1cbiAgcmV0dXJuIHtcbiAgICBuYW1lOiAnZmxpcCcsXG4gICAgb3B0aW9ucyxcbiAgICBhc3luYyBmbihzdGF0ZSkge1xuICAgICAgdmFyIF9taWRkbGV3YXJlRGF0YSRhcnJvdywgX21pZGRsZXdhcmVEYXRhJGZsaXA7XG4gICAgICBjb25zdCB7XG4gICAgICAgIHBsYWNlbWVudCxcbiAgICAgICAgbWlkZGxld2FyZURhdGEsXG4gICAgICAgIHJlY3RzLFxuICAgICAgICBpbml0aWFsUGxhY2VtZW50LFxuICAgICAgICBwbGF0Zm9ybSxcbiAgICAgICAgZWxlbWVudHNcbiAgICAgIH0gPSBzdGF0ZTtcbiAgICAgIGNvbnN0IHtcbiAgICAgICAgbWFpbkF4aXM6IGNoZWNrTWFpbkF4aXMgPSB0cnVlLFxuICAgICAgICBjcm9zc0F4aXM6IGNoZWNrQ3Jvc3NBeGlzID0gdHJ1ZSxcbiAgICAgICAgZmFsbGJhY2tQbGFjZW1lbnRzOiBzcGVjaWZpZWRGYWxsYmFja1BsYWNlbWVudHMsXG4gICAgICAgIGZhbGxiYWNrU3RyYXRlZ3kgPSAnYmVzdEZpdCcsXG4gICAgICAgIGZhbGxiYWNrQXhpc1NpZGVEaXJlY3Rpb24gPSAnbm9uZScsXG4gICAgICAgIGZsaXBBbGlnbm1lbnQgPSB0cnVlLFxuICAgICAgICAuLi5kZXRlY3RPdmVyZmxvd09wdGlvbnNcbiAgICAgIH0gPSBldmFsdWF0ZShvcHRpb25zLCBzdGF0ZSk7XG5cbiAgICAgIC8vIElmIGEgcmVzZXQgYnkgdGhlIGFycm93IHdhcyBjYXVzZWQgZHVlIHRvIGFuIGFsaWdubWVudCBvZmZzZXQgYmVpbmdcbiAgICAgIC8vIGFkZGVkLCB3ZSBzaG91bGQgc2tpcCBhbnkgbG9naWMgbm93IHNpbmNlIGBmbGlwKClgIGhhcyBhbHJlYWR5IGRvbmUgaXRzXG4gICAgICAvLyB3b3JrLlxuICAgICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL2Zsb2F0aW5nLXVpL2Zsb2F0aW5nLXVpL2lzc3Vlcy8yNTQ5I2lzc3VlY29tbWVudC0xNzE5NjAxNjQzXG4gICAgICBpZiAoKF9taWRkbGV3YXJlRGF0YSRhcnJvdyA9IG1pZGRsZXdhcmVEYXRhLmFycm93KSAhPSBudWxsICYmIF9taWRkbGV3YXJlRGF0YSRhcnJvdy5hbGlnbm1lbnRPZmZzZXQpIHtcbiAgICAgICAgcmV0dXJuIHt9O1xuICAgICAgfVxuICAgICAgY29uc3Qgc2lkZSA9IGdldFNpZGUocGxhY2VtZW50KTtcbiAgICAgIGNvbnN0IGluaXRpYWxTaWRlQXhpcyA9IGdldFNpZGVBeGlzKGluaXRpYWxQbGFjZW1lbnQpO1xuICAgICAgY29uc3QgaXNCYXNlUGxhY2VtZW50ID0gZ2V0U2lkZShpbml0aWFsUGxhY2VtZW50KSA9PT0gaW5pdGlhbFBsYWNlbWVudDtcbiAgICAgIGNvbnN0IHJ0bCA9IGF3YWl0IChwbGF0Zm9ybS5pc1JUTCA9PSBudWxsID8gdm9pZCAwIDogcGxhdGZvcm0uaXNSVEwoZWxlbWVudHMuZmxvYXRpbmcpKTtcbiAgICAgIGNvbnN0IGZhbGxiYWNrUGxhY2VtZW50cyA9IHNwZWNpZmllZEZhbGxiYWNrUGxhY2VtZW50cyB8fCAoaXNCYXNlUGxhY2VtZW50IHx8ICFmbGlwQWxpZ25tZW50ID8gW2dldE9wcG9zaXRlUGxhY2VtZW50KGluaXRpYWxQbGFjZW1lbnQpXSA6IGdldEV4cGFuZGVkUGxhY2VtZW50cyhpbml0aWFsUGxhY2VtZW50KSk7XG4gICAgICBjb25zdCBoYXNGYWxsYmFja0F4aXNTaWRlRGlyZWN0aW9uID0gZmFsbGJhY2tBeGlzU2lkZURpcmVjdGlvbiAhPT0gJ25vbmUnO1xuICAgICAgaWYgKCFzcGVjaWZpZWRGYWxsYmFja1BsYWNlbWVudHMgJiYgaGFzRmFsbGJhY2tBeGlzU2lkZURpcmVjdGlvbikge1xuICAgICAgICBmYWxsYmFja1BsYWNlbWVudHMucHVzaCguLi5nZXRPcHBvc2l0ZUF4aXNQbGFjZW1lbnRzKGluaXRpYWxQbGFjZW1lbnQsIGZsaXBBbGlnbm1lbnQsIGZhbGxiYWNrQXhpc1NpZGVEaXJlY3Rpb24sIHJ0bCkpO1xuICAgICAgfVxuICAgICAgY29uc3QgcGxhY2VtZW50cyA9IFtpbml0aWFsUGxhY2VtZW50LCAuLi5mYWxsYmFja1BsYWNlbWVudHNdO1xuICAgICAgY29uc3Qgb3ZlcmZsb3cgPSBhd2FpdCBkZXRlY3RPdmVyZmxvdyhzdGF0ZSwgZGV0ZWN0T3ZlcmZsb3dPcHRpb25zKTtcbiAgICAgIGNvbnN0IG92ZXJmbG93cyA9IFtdO1xuICAgICAgbGV0IG92ZXJmbG93c0RhdGEgPSAoKF9taWRkbGV3YXJlRGF0YSRmbGlwID0gbWlkZGxld2FyZURhdGEuZmxpcCkgPT0gbnVsbCA/IHZvaWQgMCA6IF9taWRkbGV3YXJlRGF0YSRmbGlwLm92ZXJmbG93cykgfHwgW107XG4gICAgICBpZiAoY2hlY2tNYWluQXhpcykge1xuICAgICAgICBvdmVyZmxvd3MucHVzaChvdmVyZmxvd1tzaWRlXSk7XG4gICAgICB9XG4gICAgICBpZiAoY2hlY2tDcm9zc0F4aXMpIHtcbiAgICAgICAgY29uc3Qgc2lkZXMgPSBnZXRBbGlnbm1lbnRTaWRlcyhwbGFjZW1lbnQsIHJlY3RzLCBydGwpO1xuICAgICAgICBvdmVyZmxvd3MucHVzaChvdmVyZmxvd1tzaWRlc1swXV0sIG92ZXJmbG93W3NpZGVzWzFdXSk7XG4gICAgICB9XG4gICAgICBvdmVyZmxvd3NEYXRhID0gWy4uLm92ZXJmbG93c0RhdGEsIHtcbiAgICAgICAgcGxhY2VtZW50LFxuICAgICAgICBvdmVyZmxvd3NcbiAgICAgIH1dO1xuXG4gICAgICAvLyBPbmUgb3IgbW9yZSBzaWRlcyBpcyBvdmVyZmxvd2luZy5cbiAgICAgIGlmICghb3ZlcmZsb3dzLmV2ZXJ5KHNpZGUgPT4gc2lkZSA8PSAwKSkge1xuICAgICAgICB2YXIgX21pZGRsZXdhcmVEYXRhJGZsaXAyLCBfb3ZlcmZsb3dzRGF0YSRmaWx0ZXI7XG4gICAgICAgIGNvbnN0IG5leHRJbmRleCA9ICgoKF9taWRkbGV3YXJlRGF0YSRmbGlwMiA9IG1pZGRsZXdhcmVEYXRhLmZsaXApID09IG51bGwgPyB2b2lkIDAgOiBfbWlkZGxld2FyZURhdGEkZmxpcDIuaW5kZXgpIHx8IDApICsgMTtcbiAgICAgICAgY29uc3QgbmV4dFBsYWNlbWVudCA9IHBsYWNlbWVudHNbbmV4dEluZGV4XTtcbiAgICAgICAgaWYgKG5leHRQbGFjZW1lbnQpIHtcbiAgICAgICAgICB2YXIgX292ZXJmbG93c0RhdGEkO1xuICAgICAgICAgIGNvbnN0IGlnbm9yZUNyb3NzQXhpc092ZXJmbG93ID0gY2hlY2tDcm9zc0F4aXMgPT09ICdhbGlnbm1lbnQnID8gaW5pdGlhbFNpZGVBeGlzICE9PSBnZXRTaWRlQXhpcyhuZXh0UGxhY2VtZW50KSA6IGZhbHNlO1xuICAgICAgICAgIGNvbnN0IGhhc0luaXRpYWxNYWluQXhpc092ZXJmbG93ID0gKChfb3ZlcmZsb3dzRGF0YSQgPSBvdmVyZmxvd3NEYXRhWzBdKSA9PSBudWxsID8gdm9pZCAwIDogX292ZXJmbG93c0RhdGEkLm92ZXJmbG93c1swXSkgPiAwO1xuICAgICAgICAgIGlmICghaWdub3JlQ3Jvc3NBeGlzT3ZlcmZsb3cgfHwgaGFzSW5pdGlhbE1haW5BeGlzT3ZlcmZsb3cpIHtcbiAgICAgICAgICAgIC8vIFRyeSBuZXh0IHBsYWNlbWVudCBhbmQgcmUtcnVuIHRoZSBsaWZlY3ljbGUuXG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICBkYXRhOiB7XG4gICAgICAgICAgICAgICAgaW5kZXg6IG5leHRJbmRleCxcbiAgICAgICAgICAgICAgICBvdmVyZmxvd3M6IG92ZXJmbG93c0RhdGFcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgcmVzZXQ6IHtcbiAgICAgICAgICAgICAgICBwbGFjZW1lbnQ6IG5leHRQbGFjZW1lbnRcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAvLyBGaXJzdCwgZmluZCB0aGUgY2FuZGlkYXRlcyB0aGF0IGZpdCBvbiB0aGUgbWFpbkF4aXMgc2lkZSBvZiBvdmVyZmxvdyxcbiAgICAgICAgLy8gdGhlbiBmaW5kIHRoZSBwbGFjZW1lbnQgdGhhdCBmaXRzIHRoZSBiZXN0IG9uIHRoZSBtYWluIGNyb3NzQXhpcyBzaWRlLlxuICAgICAgICBsZXQgcmVzZXRQbGFjZW1lbnQgPSAoX292ZXJmbG93c0RhdGEkZmlsdGVyID0gb3ZlcmZsb3dzRGF0YS5maWx0ZXIoZCA9PiBkLm92ZXJmbG93c1swXSA8PSAwKS5zb3J0KChhLCBiKSA9PiBhLm92ZXJmbG93c1sxXSAtIGIub3ZlcmZsb3dzWzFdKVswXSkgPT0gbnVsbCA/IHZvaWQgMCA6IF9vdmVyZmxvd3NEYXRhJGZpbHRlci5wbGFjZW1lbnQ7XG5cbiAgICAgICAgLy8gT3RoZXJ3aXNlIGZhbGxiYWNrLlxuICAgICAgICBpZiAoIXJlc2V0UGxhY2VtZW50KSB7XG4gICAgICAgICAgc3dpdGNoIChmYWxsYmFja1N0cmF0ZWd5KSB7XG4gICAgICAgICAgICBjYXNlICdiZXN0Rml0JzpcbiAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgIHZhciBfb3ZlcmZsb3dzRGF0YSRmaWx0ZXIyO1xuICAgICAgICAgICAgICAgIGNvbnN0IHBsYWNlbWVudCA9IChfb3ZlcmZsb3dzRGF0YSRmaWx0ZXIyID0gb3ZlcmZsb3dzRGF0YS5maWx0ZXIoZCA9PiB7XG4gICAgICAgICAgICAgICAgICBpZiAoaGFzRmFsbGJhY2tBeGlzU2lkZURpcmVjdGlvbikge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBjdXJyZW50U2lkZUF4aXMgPSBnZXRTaWRlQXhpcyhkLnBsYWNlbWVudCk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBjdXJyZW50U2lkZUF4aXMgPT09IGluaXRpYWxTaWRlQXhpcyB8fFxuICAgICAgICAgICAgICAgICAgICAvLyBDcmVhdGUgYSBiaWFzIHRvIHRoZSBgeWAgc2lkZSBheGlzIGR1ZSB0byBob3Jpem9udGFsXG4gICAgICAgICAgICAgICAgICAgIC8vIHJlYWRpbmcgZGlyZWN0aW9ucyBmYXZvcmluZyBncmVhdGVyIHdpZHRoLlxuICAgICAgICAgICAgICAgICAgICBjdXJyZW50U2lkZUF4aXMgPT09ICd5JztcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICAgICAgICAgIH0pLm1hcChkID0+IFtkLnBsYWNlbWVudCwgZC5vdmVyZmxvd3MuZmlsdGVyKG92ZXJmbG93ID0+IG92ZXJmbG93ID4gMCkucmVkdWNlKChhY2MsIG92ZXJmbG93KSA9PiBhY2MgKyBvdmVyZmxvdywgMCldKS5zb3J0KChhLCBiKSA9PiBhWzFdIC0gYlsxXSlbMF0pID09IG51bGwgPyB2b2lkIDAgOiBfb3ZlcmZsb3dzRGF0YSRmaWx0ZXIyWzBdO1xuICAgICAgICAgICAgICAgIGlmIChwbGFjZW1lbnQpIHtcbiAgICAgICAgICAgICAgICAgIHJlc2V0UGxhY2VtZW50ID0gcGxhY2VtZW50O1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2FzZSAnaW5pdGlhbFBsYWNlbWVudCc6XG4gICAgICAgICAgICAgIHJlc2V0UGxhY2VtZW50ID0gaW5pdGlhbFBsYWNlbWVudDtcbiAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmIChwbGFjZW1lbnQgIT09IHJlc2V0UGxhY2VtZW50KSB7XG4gICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHJlc2V0OiB7XG4gICAgICAgICAgICAgIHBsYWNlbWVudDogcmVzZXRQbGFjZW1lbnRcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICByZXR1cm4ge307XG4gICAgfVxuICB9O1xufTtcblxuZnVuY3Rpb24gZ2V0U2lkZU9mZnNldHMob3ZlcmZsb3csIHJlY3QpIHtcbiAgcmV0dXJuIHtcbiAgICB0b3A6IG92ZXJmbG93LnRvcCAtIHJlY3QuaGVpZ2h0LFxuICAgIHJpZ2h0OiBvdmVyZmxvdy5yaWdodCAtIHJlY3Qud2lkdGgsXG4gICAgYm90dG9tOiBvdmVyZmxvdy5ib3R0b20gLSByZWN0LmhlaWdodCxcbiAgICBsZWZ0OiBvdmVyZmxvdy5sZWZ0IC0gcmVjdC53aWR0aFxuICB9O1xufVxuZnVuY3Rpb24gaXNBbnlTaWRlRnVsbHlDbGlwcGVkKG92ZXJmbG93KSB7XG4gIHJldHVybiBzaWRlcy5zb21lKHNpZGUgPT4gb3ZlcmZsb3dbc2lkZV0gPj0gMCk7XG59XG4vKipcbiAqIFByb3ZpZGVzIGRhdGEgdG8gaGlkZSB0aGUgZmxvYXRpbmcgZWxlbWVudCBpbiBhcHBsaWNhYmxlIHNpdHVhdGlvbnMsIHN1Y2ggYXNcbiAqIHdoZW4gaXQgaXMgbm90IGluIHRoZSBzYW1lIGNsaXBwaW5nIGNvbnRleHQgYXMgdGhlIHJlZmVyZW5jZSBlbGVtZW50LlxuICogQHNlZSBodHRwczovL2Zsb2F0aW5nLXVpLmNvbS9kb2NzL2hpZGVcbiAqL1xuY29uc3QgaGlkZSA9IGZ1bmN0aW9uIChvcHRpb25zKSB7XG4gIGlmIChvcHRpb25zID09PSB2b2lkIDApIHtcbiAgICBvcHRpb25zID0ge307XG4gIH1cbiAgcmV0dXJuIHtcbiAgICBuYW1lOiAnaGlkZScsXG4gICAgb3B0aW9ucyxcbiAgICBhc3luYyBmbihzdGF0ZSkge1xuICAgICAgY29uc3Qge1xuICAgICAgICByZWN0c1xuICAgICAgfSA9IHN0YXRlO1xuICAgICAgY29uc3Qge1xuICAgICAgICBzdHJhdGVneSA9ICdyZWZlcmVuY2VIaWRkZW4nLFxuICAgICAgICAuLi5kZXRlY3RPdmVyZmxvd09wdGlvbnNcbiAgICAgIH0gPSBldmFsdWF0ZShvcHRpb25zLCBzdGF0ZSk7XG4gICAgICBzd2l0Y2ggKHN0cmF0ZWd5KSB7XG4gICAgICAgIGNhc2UgJ3JlZmVyZW5jZUhpZGRlbic6XG4gICAgICAgICAge1xuICAgICAgICAgICAgY29uc3Qgb3ZlcmZsb3cgPSBhd2FpdCBkZXRlY3RPdmVyZmxvdyhzdGF0ZSwge1xuICAgICAgICAgICAgICAuLi5kZXRlY3RPdmVyZmxvd09wdGlvbnMsXG4gICAgICAgICAgICAgIGVsZW1lbnRDb250ZXh0OiAncmVmZXJlbmNlJ1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBjb25zdCBvZmZzZXRzID0gZ2V0U2lkZU9mZnNldHMob3ZlcmZsb3csIHJlY3RzLnJlZmVyZW5jZSk7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICBkYXRhOiB7XG4gICAgICAgICAgICAgICAgcmVmZXJlbmNlSGlkZGVuT2Zmc2V0czogb2Zmc2V0cyxcbiAgICAgICAgICAgICAgICByZWZlcmVuY2VIaWRkZW46IGlzQW55U2lkZUZ1bGx5Q2xpcHBlZChvZmZzZXRzKVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9O1xuICAgICAgICAgIH1cbiAgICAgICAgY2FzZSAnZXNjYXBlZCc6XG4gICAgICAgICAge1xuICAgICAgICAgICAgY29uc3Qgb3ZlcmZsb3cgPSBhd2FpdCBkZXRlY3RPdmVyZmxvdyhzdGF0ZSwge1xuICAgICAgICAgICAgICAuLi5kZXRlY3RPdmVyZmxvd09wdGlvbnMsXG4gICAgICAgICAgICAgIGFsdEJvdW5kYXJ5OiB0cnVlXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGNvbnN0IG9mZnNldHMgPSBnZXRTaWRlT2Zmc2V0cyhvdmVyZmxvdywgcmVjdHMuZmxvYXRpbmcpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgZGF0YToge1xuICAgICAgICAgICAgICAgIGVzY2FwZWRPZmZzZXRzOiBvZmZzZXRzLFxuICAgICAgICAgICAgICAgIGVzY2FwZWQ6IGlzQW55U2lkZUZ1bGx5Q2xpcHBlZChvZmZzZXRzKVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9O1xuICAgICAgICAgIH1cbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICB7XG4gICAgICAgICAgICByZXR1cm4ge307XG4gICAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfTtcbn07XG5cbmZ1bmN0aW9uIGdldEJvdW5kaW5nUmVjdChyZWN0cykge1xuICBjb25zdCBtaW5YID0gbWluKC4uLnJlY3RzLm1hcChyZWN0ID0+IHJlY3QubGVmdCkpO1xuICBjb25zdCBtaW5ZID0gbWluKC4uLnJlY3RzLm1hcChyZWN0ID0+IHJlY3QudG9wKSk7XG4gIGNvbnN0IG1heFggPSBtYXgoLi4ucmVjdHMubWFwKHJlY3QgPT4gcmVjdC5yaWdodCkpO1xuICBjb25zdCBtYXhZID0gbWF4KC4uLnJlY3RzLm1hcChyZWN0ID0+IHJlY3QuYm90dG9tKSk7XG4gIHJldHVybiB7XG4gICAgeDogbWluWCxcbiAgICB5OiBtaW5ZLFxuICAgIHdpZHRoOiBtYXhYIC0gbWluWCxcbiAgICBoZWlnaHQ6IG1heFkgLSBtaW5ZXG4gIH07XG59XG5mdW5jdGlvbiBnZXRSZWN0c0J5TGluZShyZWN0cykge1xuICBjb25zdCBzb3J0ZWRSZWN0cyA9IHJlY3RzLnNsaWNlKCkuc29ydCgoYSwgYikgPT4gYS55IC0gYi55KTtcbiAgY29uc3QgZ3JvdXBzID0gW107XG4gIGxldCBwcmV2UmVjdCA9IG51bGw7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgc29ydGVkUmVjdHMubGVuZ3RoOyBpKyspIHtcbiAgICBjb25zdCByZWN0ID0gc29ydGVkUmVjdHNbaV07XG4gICAgaWYgKCFwcmV2UmVjdCB8fCByZWN0LnkgLSBwcmV2UmVjdC55ID4gcHJldlJlY3QuaGVpZ2h0IC8gMikge1xuICAgICAgZ3JvdXBzLnB1c2goW3JlY3RdKTtcbiAgICB9IGVsc2Uge1xuICAgICAgZ3JvdXBzW2dyb3Vwcy5sZW5ndGggLSAxXS5wdXNoKHJlY3QpO1xuICAgIH1cbiAgICBwcmV2UmVjdCA9IHJlY3Q7XG4gIH1cbiAgcmV0dXJuIGdyb3Vwcy5tYXAocmVjdCA9PiByZWN0VG9DbGllbnRSZWN0KGdldEJvdW5kaW5nUmVjdChyZWN0KSkpO1xufVxuLyoqXG4gKiBQcm92aWRlcyBpbXByb3ZlZCBwb3NpdGlvbmluZyBmb3IgaW5saW5lIHJlZmVyZW5jZSBlbGVtZW50cyB0aGF0IGNhbiBzcGFuXG4gKiBvdmVyIG11bHRpcGxlIGxpbmVzLCBzdWNoIGFzIGh5cGVybGlua3Mgb3IgcmFuZ2Ugc2VsZWN0aW9ucy5cbiAqIEBzZWUgaHR0cHM6Ly9mbG9hdGluZy11aS5jb20vZG9jcy9pbmxpbmVcbiAqL1xuY29uc3QgaW5saW5lID0gZnVuY3Rpb24gKG9wdGlvbnMpIHtcbiAgaWYgKG9wdGlvbnMgPT09IHZvaWQgMCkge1xuICAgIG9wdGlvbnMgPSB7fTtcbiAgfVxuICByZXR1cm4ge1xuICAgIG5hbWU6ICdpbmxpbmUnLFxuICAgIG9wdGlvbnMsXG4gICAgYXN5bmMgZm4oc3RhdGUpIHtcbiAgICAgIGNvbnN0IHtcbiAgICAgICAgcGxhY2VtZW50LFxuICAgICAgICBlbGVtZW50cyxcbiAgICAgICAgcmVjdHMsXG4gICAgICAgIHBsYXRmb3JtLFxuICAgICAgICBzdHJhdGVneVxuICAgICAgfSA9IHN0YXRlO1xuICAgICAgLy8gQSBNb3VzZUV2ZW50J3MgY2xpZW50e1gsWX0gY29vcmRzIGNhbiBiZSB1cCB0byAyIHBpeGVscyBvZmYgYVxuICAgICAgLy8gQ2xpZW50UmVjdCdzIGJvdW5kcywgZGVzcGl0ZSB0aGUgZXZlbnQgbGlzdGVuZXIgYmVpbmcgdHJpZ2dlcmVkLiBBXG4gICAgICAvLyBwYWRkaW5nIG9mIDIgc2VlbXMgdG8gaGFuZGxlIHRoaXMgaXNzdWUuXG4gICAgICBjb25zdCB7XG4gICAgICAgIHBhZGRpbmcgPSAyLFxuICAgICAgICB4LFxuICAgICAgICB5XG4gICAgICB9ID0gZXZhbHVhdGUob3B0aW9ucywgc3RhdGUpO1xuICAgICAgY29uc3QgbmF0aXZlQ2xpZW50UmVjdHMgPSBBcnJheS5mcm9tKChhd2FpdCAocGxhdGZvcm0uZ2V0Q2xpZW50UmVjdHMgPT0gbnVsbCA/IHZvaWQgMCA6IHBsYXRmb3JtLmdldENsaWVudFJlY3RzKGVsZW1lbnRzLnJlZmVyZW5jZSkpKSB8fCBbXSk7XG4gICAgICBjb25zdCBjbGllbnRSZWN0cyA9IGdldFJlY3RzQnlMaW5lKG5hdGl2ZUNsaWVudFJlY3RzKTtcbiAgICAgIGNvbnN0IGZhbGxiYWNrID0gcmVjdFRvQ2xpZW50UmVjdChnZXRCb3VuZGluZ1JlY3QobmF0aXZlQ2xpZW50UmVjdHMpKTtcbiAgICAgIGNvbnN0IHBhZGRpbmdPYmplY3QgPSBnZXRQYWRkaW5nT2JqZWN0KHBhZGRpbmcpO1xuICAgICAgZnVuY3Rpb24gZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCkge1xuICAgICAgICAvLyBUaGVyZSBhcmUgdHdvIHJlY3RzIGFuZCB0aGV5IGFyZSBkaXNqb2luZWQuXG4gICAgICAgIGlmIChjbGllbnRSZWN0cy5sZW5ndGggPT09IDIgJiYgY2xpZW50UmVjdHNbMF0ubGVmdCA+IGNsaWVudFJlY3RzWzFdLnJpZ2h0ICYmIHggIT0gbnVsbCAmJiB5ICE9IG51bGwpIHtcbiAgICAgICAgICAvLyBGaW5kIHRoZSBmaXJzdCByZWN0IGluIHdoaWNoIHRoZSBwb2ludCBpcyBmdWxseSBpbnNpZGUuXG4gICAgICAgICAgcmV0dXJuIGNsaWVudFJlY3RzLmZpbmQocmVjdCA9PiB4ID4gcmVjdC5sZWZ0IC0gcGFkZGluZ09iamVjdC5sZWZ0ICYmIHggPCByZWN0LnJpZ2h0ICsgcGFkZGluZ09iamVjdC5yaWdodCAmJiB5ID4gcmVjdC50b3AgLSBwYWRkaW5nT2JqZWN0LnRvcCAmJiB5IDwgcmVjdC5ib3R0b20gKyBwYWRkaW5nT2JqZWN0LmJvdHRvbSkgfHwgZmFsbGJhY2s7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBUaGVyZSBhcmUgMiBvciBtb3JlIGNvbm5lY3RlZCByZWN0cy5cbiAgICAgICAgaWYgKGNsaWVudFJlY3RzLmxlbmd0aCA+PSAyKSB7XG4gICAgICAgICAgaWYgKGdldFNpZGVBeGlzKHBsYWNlbWVudCkgPT09ICd5Jykge1xuICAgICAgICAgICAgY29uc3QgZmlyc3RSZWN0ID0gY2xpZW50UmVjdHNbMF07XG4gICAgICAgICAgICBjb25zdCBsYXN0UmVjdCA9IGNsaWVudFJlY3RzW2NsaWVudFJlY3RzLmxlbmd0aCAtIDFdO1xuICAgICAgICAgICAgY29uc3QgaXNUb3AgPSBnZXRTaWRlKHBsYWNlbWVudCkgPT09ICd0b3AnO1xuICAgICAgICAgICAgY29uc3QgdG9wID0gZmlyc3RSZWN0LnRvcDtcbiAgICAgICAgICAgIGNvbnN0IGJvdHRvbSA9IGxhc3RSZWN0LmJvdHRvbTtcbiAgICAgICAgICAgIGNvbnN0IGxlZnQgPSBpc1RvcCA/IGZpcnN0UmVjdC5sZWZ0IDogbGFzdFJlY3QubGVmdDtcbiAgICAgICAgICAgIGNvbnN0IHJpZ2h0ID0gaXNUb3AgPyBmaXJzdFJlY3QucmlnaHQgOiBsYXN0UmVjdC5yaWdodDtcbiAgICAgICAgICAgIGNvbnN0IHdpZHRoID0gcmlnaHQgLSBsZWZ0O1xuICAgICAgICAgICAgY29uc3QgaGVpZ2h0ID0gYm90dG9tIC0gdG9wO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgdG9wLFxuICAgICAgICAgICAgICBib3R0b20sXG4gICAgICAgICAgICAgIGxlZnQsXG4gICAgICAgICAgICAgIHJpZ2h0LFxuICAgICAgICAgICAgICB3aWR0aCxcbiAgICAgICAgICAgICAgaGVpZ2h0LFxuICAgICAgICAgICAgICB4OiBsZWZ0LFxuICAgICAgICAgICAgICB5OiB0b3BcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgfVxuICAgICAgICAgIGNvbnN0IGlzTGVmdFNpZGUgPSBnZXRTaWRlKHBsYWNlbWVudCkgPT09ICdsZWZ0JztcbiAgICAgICAgICBjb25zdCBtYXhSaWdodCA9IG1heCguLi5jbGllbnRSZWN0cy5tYXAocmVjdCA9PiByZWN0LnJpZ2h0KSk7XG4gICAgICAgICAgY29uc3QgbWluTGVmdCA9IG1pbiguLi5jbGllbnRSZWN0cy5tYXAocmVjdCA9PiByZWN0LmxlZnQpKTtcbiAgICAgICAgICBjb25zdCBtZWFzdXJlUmVjdHMgPSBjbGllbnRSZWN0cy5maWx0ZXIocmVjdCA9PiBpc0xlZnRTaWRlID8gcmVjdC5sZWZ0ID09PSBtaW5MZWZ0IDogcmVjdC5yaWdodCA9PT0gbWF4UmlnaHQpO1xuICAgICAgICAgIGNvbnN0IHRvcCA9IG1lYXN1cmVSZWN0c1swXS50b3A7XG4gICAgICAgICAgY29uc3QgYm90dG9tID0gbWVhc3VyZVJlY3RzW21lYXN1cmVSZWN0cy5sZW5ndGggLSAxXS5ib3R0b207XG4gICAgICAgICAgY29uc3QgbGVmdCA9IG1pbkxlZnQ7XG4gICAgICAgICAgY29uc3QgcmlnaHQgPSBtYXhSaWdodDtcbiAgICAgICAgICBjb25zdCB3aWR0aCA9IHJpZ2h0IC0gbGVmdDtcbiAgICAgICAgICBjb25zdCBoZWlnaHQgPSBib3R0b20gLSB0b3A7XG4gICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHRvcCxcbiAgICAgICAgICAgIGJvdHRvbSxcbiAgICAgICAgICAgIGxlZnQsXG4gICAgICAgICAgICByaWdodCxcbiAgICAgICAgICAgIHdpZHRoLFxuICAgICAgICAgICAgaGVpZ2h0LFxuICAgICAgICAgICAgeDogbGVmdCxcbiAgICAgICAgICAgIHk6IHRvcFxuICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGZhbGxiYWNrO1xuICAgICAgfVxuICAgICAgY29uc3QgcmVzZXRSZWN0cyA9IGF3YWl0IHBsYXRmb3JtLmdldEVsZW1lbnRSZWN0cyh7XG4gICAgICAgIHJlZmVyZW5jZToge1xuICAgICAgICAgIGdldEJvdW5kaW5nQ2xpZW50UmVjdFxuICAgICAgICB9LFxuICAgICAgICBmbG9hdGluZzogZWxlbWVudHMuZmxvYXRpbmcsXG4gICAgICAgIHN0cmF0ZWd5XG4gICAgICB9KTtcbiAgICAgIGlmIChyZWN0cy5yZWZlcmVuY2UueCAhPT0gcmVzZXRSZWN0cy5yZWZlcmVuY2UueCB8fCByZWN0cy5yZWZlcmVuY2UueSAhPT0gcmVzZXRSZWN0cy5yZWZlcmVuY2UueSB8fCByZWN0cy5yZWZlcmVuY2Uud2lkdGggIT09IHJlc2V0UmVjdHMucmVmZXJlbmNlLndpZHRoIHx8IHJlY3RzLnJlZmVyZW5jZS5oZWlnaHQgIT09IHJlc2V0UmVjdHMucmVmZXJlbmNlLmhlaWdodCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHJlc2V0OiB7XG4gICAgICAgICAgICByZWN0czogcmVzZXRSZWN0c1xuICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgIH1cbiAgICAgIHJldHVybiB7fTtcbiAgICB9XG4gIH07XG59O1xuXG4vLyBGb3IgdHlwZSBiYWNrd2FyZHMtY29tcGF0aWJpbGl0eSwgdGhlIGBPZmZzZXRPcHRpb25zYCB0eXBlIHdhcyBhbHNvXG4vLyBEZXJpdmFibGUuXG5cbmFzeW5jIGZ1bmN0aW9uIGNvbnZlcnRWYWx1ZVRvQ29vcmRzKHN0YXRlLCBvcHRpb25zKSB7XG4gIGNvbnN0IHtcbiAgICBwbGFjZW1lbnQsXG4gICAgcGxhdGZvcm0sXG4gICAgZWxlbWVudHNcbiAgfSA9IHN0YXRlO1xuICBjb25zdCBydGwgPSBhd2FpdCAocGxhdGZvcm0uaXNSVEwgPT0gbnVsbCA/IHZvaWQgMCA6IHBsYXRmb3JtLmlzUlRMKGVsZW1lbnRzLmZsb2F0aW5nKSk7XG4gIGNvbnN0IHNpZGUgPSBnZXRTaWRlKHBsYWNlbWVudCk7XG4gIGNvbnN0IGFsaWdubWVudCA9IGdldEFsaWdubWVudChwbGFjZW1lbnQpO1xuICBjb25zdCBpc1ZlcnRpY2FsID0gZ2V0U2lkZUF4aXMocGxhY2VtZW50KSA9PT0gJ3knO1xuICBjb25zdCBtYWluQXhpc011bHRpID0gWydsZWZ0JywgJ3RvcCddLmluY2x1ZGVzKHNpZGUpID8gLTEgOiAxO1xuICBjb25zdCBjcm9zc0F4aXNNdWx0aSA9IHJ0bCAmJiBpc1ZlcnRpY2FsID8gLTEgOiAxO1xuICBjb25zdCByYXdWYWx1ZSA9IGV2YWx1YXRlKG9wdGlvbnMsIHN0YXRlKTtcblxuICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcHJlZmVyLWNvbnN0XG4gIGxldCB7XG4gICAgbWFpbkF4aXMsXG4gICAgY3Jvc3NBeGlzLFxuICAgIGFsaWdubWVudEF4aXNcbiAgfSA9IHR5cGVvZiByYXdWYWx1ZSA9PT0gJ251bWJlcicgPyB7XG4gICAgbWFpbkF4aXM6IHJhd1ZhbHVlLFxuICAgIGNyb3NzQXhpczogMCxcbiAgICBhbGlnbm1lbnRBeGlzOiBudWxsXG4gIH0gOiB7XG4gICAgbWFpbkF4aXM6IHJhd1ZhbHVlLm1haW5BeGlzIHx8IDAsXG4gICAgY3Jvc3NBeGlzOiByYXdWYWx1ZS5jcm9zc0F4aXMgfHwgMCxcbiAgICBhbGlnbm1lbnRBeGlzOiByYXdWYWx1ZS5hbGlnbm1lbnRBeGlzXG4gIH07XG4gIGlmIChhbGlnbm1lbnQgJiYgdHlwZW9mIGFsaWdubWVudEF4aXMgPT09ICdudW1iZXInKSB7XG4gICAgY3Jvc3NBeGlzID0gYWxpZ25tZW50ID09PSAnZW5kJyA/IGFsaWdubWVudEF4aXMgKiAtMSA6IGFsaWdubWVudEF4aXM7XG4gIH1cbiAgcmV0dXJuIGlzVmVydGljYWwgPyB7XG4gICAgeDogY3Jvc3NBeGlzICogY3Jvc3NBeGlzTXVsdGksXG4gICAgeTogbWFpbkF4aXMgKiBtYWluQXhpc011bHRpXG4gIH0gOiB7XG4gICAgeDogbWFpbkF4aXMgKiBtYWluQXhpc011bHRpLFxuICAgIHk6IGNyb3NzQXhpcyAqIGNyb3NzQXhpc011bHRpXG4gIH07XG59XG5cbi8qKlxuICogTW9kaWZpZXMgdGhlIHBsYWNlbWVudCBieSB0cmFuc2xhdGluZyB0aGUgZmxvYXRpbmcgZWxlbWVudCBhbG9uZyB0aGVcbiAqIHNwZWNpZmllZCBheGVzLlxuICogQSBudW1iZXIgKHNob3J0aGFuZCBmb3IgYG1haW5BeGlzYCBvciBkaXN0YW5jZSksIG9yIGFuIGF4ZXMgY29uZmlndXJhdGlvblxuICogb2JqZWN0IG1heSBiZSBwYXNzZWQuXG4gKiBAc2VlIGh0dHBzOi8vZmxvYXRpbmctdWkuY29tL2RvY3Mvb2Zmc2V0XG4gKi9cbmNvbnN0IG9mZnNldCA9IGZ1bmN0aW9uIChvcHRpb25zKSB7XG4gIGlmIChvcHRpb25zID09PSB2b2lkIDApIHtcbiAgICBvcHRpb25zID0gMDtcbiAgfVxuICByZXR1cm4ge1xuICAgIG5hbWU6ICdvZmZzZXQnLFxuICAgIG9wdGlvbnMsXG4gICAgYXN5bmMgZm4oc3RhdGUpIHtcbiAgICAgIHZhciBfbWlkZGxld2FyZURhdGEkb2Zmc2UsIF9taWRkbGV3YXJlRGF0YSRhcnJvdztcbiAgICAgIGNvbnN0IHtcbiAgICAgICAgeCxcbiAgICAgICAgeSxcbiAgICAgICAgcGxhY2VtZW50LFxuICAgICAgICBtaWRkbGV3YXJlRGF0YVxuICAgICAgfSA9IHN0YXRlO1xuICAgICAgY29uc3QgZGlmZkNvb3JkcyA9IGF3YWl0IGNvbnZlcnRWYWx1ZVRvQ29vcmRzKHN0YXRlLCBvcHRpb25zKTtcblxuICAgICAgLy8gSWYgdGhlIHBsYWNlbWVudCBpcyB0aGUgc2FtZSBhbmQgdGhlIGFycm93IGNhdXNlZCBhbiBhbGlnbm1lbnQgb2Zmc2V0XG4gICAgICAvLyB0aGVuIHdlIGRvbid0IG5lZWQgdG8gY2hhbmdlIHRoZSBwb3NpdGlvbmluZyBjb29yZGluYXRlcy5cbiAgICAgIGlmIChwbGFjZW1lbnQgPT09ICgoX21pZGRsZXdhcmVEYXRhJG9mZnNlID0gbWlkZGxld2FyZURhdGEub2Zmc2V0KSA9PSBudWxsID8gdm9pZCAwIDogX21pZGRsZXdhcmVEYXRhJG9mZnNlLnBsYWNlbWVudCkgJiYgKF9taWRkbGV3YXJlRGF0YSRhcnJvdyA9IG1pZGRsZXdhcmVEYXRhLmFycm93KSAhPSBudWxsICYmIF9taWRkbGV3YXJlRGF0YSRhcnJvdy5hbGlnbm1lbnRPZmZzZXQpIHtcbiAgICAgICAgcmV0dXJuIHt9O1xuICAgICAgfVxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgeDogeCArIGRpZmZDb29yZHMueCxcbiAgICAgICAgeTogeSArIGRpZmZDb29yZHMueSxcbiAgICAgICAgZGF0YToge1xuICAgICAgICAgIC4uLmRpZmZDb29yZHMsXG4gICAgICAgICAgcGxhY2VtZW50XG4gICAgICAgIH1cbiAgICAgIH07XG4gICAgfVxuICB9O1xufTtcblxuLyoqXG4gKiBPcHRpbWl6ZXMgdGhlIHZpc2liaWxpdHkgb2YgdGhlIGZsb2F0aW5nIGVsZW1lbnQgYnkgc2hpZnRpbmcgaXQgaW4gb3JkZXIgdG9cbiAqIGtlZXAgaXQgaW4gdmlldyB3aGVuIGl0IHdpbGwgb3ZlcmZsb3cgdGhlIGNsaXBwaW5nIGJvdW5kYXJ5LlxuICogQHNlZSBodHRwczovL2Zsb2F0aW5nLXVpLmNvbS9kb2NzL3NoaWZ0XG4gKi9cbmNvbnN0IHNoaWZ0ID0gZnVuY3Rpb24gKG9wdGlvbnMpIHtcbiAgaWYgKG9wdGlvbnMgPT09IHZvaWQgMCkge1xuICAgIG9wdGlvbnMgPSB7fTtcbiAgfVxuICByZXR1cm4ge1xuICAgIG5hbWU6ICdzaGlmdCcsXG4gICAgb3B0aW9ucyxcbiAgICBhc3luYyBmbihzdGF0ZSkge1xuICAgICAgY29uc3Qge1xuICAgICAgICB4LFxuICAgICAgICB5LFxuICAgICAgICBwbGFjZW1lbnRcbiAgICAgIH0gPSBzdGF0ZTtcbiAgICAgIGNvbnN0IHtcbiAgICAgICAgbWFpbkF4aXM6IGNoZWNrTWFpbkF4aXMgPSB0cnVlLFxuICAgICAgICBjcm9zc0F4aXM6IGNoZWNrQ3Jvc3NBeGlzID0gZmFsc2UsXG4gICAgICAgIGxpbWl0ZXIgPSB7XG4gICAgICAgICAgZm46IF9yZWYgPT4ge1xuICAgICAgICAgICAgbGV0IHtcbiAgICAgICAgICAgICAgeCxcbiAgICAgICAgICAgICAgeVxuICAgICAgICAgICAgfSA9IF9yZWY7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICB4LFxuICAgICAgICAgICAgICB5XG4gICAgICAgICAgICB9O1xuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgLi4uZGV0ZWN0T3ZlcmZsb3dPcHRpb25zXG4gICAgICB9ID0gZXZhbHVhdGUob3B0aW9ucywgc3RhdGUpO1xuICAgICAgY29uc3QgY29vcmRzID0ge1xuICAgICAgICB4LFxuICAgICAgICB5XG4gICAgICB9O1xuICAgICAgY29uc3Qgb3ZlcmZsb3cgPSBhd2FpdCBkZXRlY3RPdmVyZmxvdyhzdGF0ZSwgZGV0ZWN0T3ZlcmZsb3dPcHRpb25zKTtcbiAgICAgIGNvbnN0IGNyb3NzQXhpcyA9IGdldFNpZGVBeGlzKGdldFNpZGUocGxhY2VtZW50KSk7XG4gICAgICBjb25zdCBtYWluQXhpcyA9IGdldE9wcG9zaXRlQXhpcyhjcm9zc0F4aXMpO1xuICAgICAgbGV0IG1haW5BeGlzQ29vcmQgPSBjb29yZHNbbWFpbkF4aXNdO1xuICAgICAgbGV0IGNyb3NzQXhpc0Nvb3JkID0gY29vcmRzW2Nyb3NzQXhpc107XG4gICAgICBpZiAoY2hlY2tNYWluQXhpcykge1xuICAgICAgICBjb25zdCBtaW5TaWRlID0gbWFpbkF4aXMgPT09ICd5JyA/ICd0b3AnIDogJ2xlZnQnO1xuICAgICAgICBjb25zdCBtYXhTaWRlID0gbWFpbkF4aXMgPT09ICd5JyA/ICdib3R0b20nIDogJ3JpZ2h0JztcbiAgICAgICAgY29uc3QgbWluID0gbWFpbkF4aXNDb29yZCArIG92ZXJmbG93W21pblNpZGVdO1xuICAgICAgICBjb25zdCBtYXggPSBtYWluQXhpc0Nvb3JkIC0gb3ZlcmZsb3dbbWF4U2lkZV07XG4gICAgICAgIG1haW5BeGlzQ29vcmQgPSBjbGFtcChtaW4sIG1haW5BeGlzQ29vcmQsIG1heCk7XG4gICAgICB9XG4gICAgICBpZiAoY2hlY2tDcm9zc0F4aXMpIHtcbiAgICAgICAgY29uc3QgbWluU2lkZSA9IGNyb3NzQXhpcyA9PT0gJ3knID8gJ3RvcCcgOiAnbGVmdCc7XG4gICAgICAgIGNvbnN0IG1heFNpZGUgPSBjcm9zc0F4aXMgPT09ICd5JyA/ICdib3R0b20nIDogJ3JpZ2h0JztcbiAgICAgICAgY29uc3QgbWluID0gY3Jvc3NBeGlzQ29vcmQgKyBvdmVyZmxvd1ttaW5TaWRlXTtcbiAgICAgICAgY29uc3QgbWF4ID0gY3Jvc3NBeGlzQ29vcmQgLSBvdmVyZmxvd1ttYXhTaWRlXTtcbiAgICAgICAgY3Jvc3NBeGlzQ29vcmQgPSBjbGFtcChtaW4sIGNyb3NzQXhpc0Nvb3JkLCBtYXgpO1xuICAgICAgfVxuICAgICAgY29uc3QgbGltaXRlZENvb3JkcyA9IGxpbWl0ZXIuZm4oe1xuICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgW21haW5BeGlzXTogbWFpbkF4aXNDb29yZCxcbiAgICAgICAgW2Nyb3NzQXhpc106IGNyb3NzQXhpc0Nvb3JkXG4gICAgICB9KTtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLmxpbWl0ZWRDb29yZHMsXG4gICAgICAgIGRhdGE6IHtcbiAgICAgICAgICB4OiBsaW1pdGVkQ29vcmRzLnggLSB4LFxuICAgICAgICAgIHk6IGxpbWl0ZWRDb29yZHMueSAtIHksXG4gICAgICAgICAgZW5hYmxlZDoge1xuICAgICAgICAgICAgW21haW5BeGlzXTogY2hlY2tNYWluQXhpcyxcbiAgICAgICAgICAgIFtjcm9zc0F4aXNdOiBjaGVja0Nyb3NzQXhpc1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfTtcbiAgICB9XG4gIH07XG59O1xuLyoqXG4gKiBCdWlsdC1pbiBgbGltaXRlcmAgdGhhdCB3aWxsIHN0b3AgYHNoaWZ0KClgIGF0IGEgY2VydGFpbiBwb2ludC5cbiAqL1xuY29uc3QgbGltaXRTaGlmdCA9IGZ1bmN0aW9uIChvcHRpb25zKSB7XG4gIGlmIChvcHRpb25zID09PSB2b2lkIDApIHtcbiAgICBvcHRpb25zID0ge307XG4gIH1cbiAgcmV0dXJuIHtcbiAgICBvcHRpb25zLFxuICAgIGZuKHN0YXRlKSB7XG4gICAgICBjb25zdCB7XG4gICAgICAgIHgsXG4gICAgICAgIHksXG4gICAgICAgIHBsYWNlbWVudCxcbiAgICAgICAgcmVjdHMsXG4gICAgICAgIG1pZGRsZXdhcmVEYXRhXG4gICAgICB9ID0gc3RhdGU7XG4gICAgICBjb25zdCB7XG4gICAgICAgIG9mZnNldCA9IDAsXG4gICAgICAgIG1haW5BeGlzOiBjaGVja01haW5BeGlzID0gdHJ1ZSxcbiAgICAgICAgY3Jvc3NBeGlzOiBjaGVja0Nyb3NzQXhpcyA9IHRydWVcbiAgICAgIH0gPSBldmFsdWF0ZShvcHRpb25zLCBzdGF0ZSk7XG4gICAgICBjb25zdCBjb29yZHMgPSB7XG4gICAgICAgIHgsXG4gICAgICAgIHlcbiAgICAgIH07XG4gICAgICBjb25zdCBjcm9zc0F4aXMgPSBnZXRTaWRlQXhpcyhwbGFjZW1lbnQpO1xuICAgICAgY29uc3QgbWFpbkF4aXMgPSBnZXRPcHBvc2l0ZUF4aXMoY3Jvc3NBeGlzKTtcbiAgICAgIGxldCBtYWluQXhpc0Nvb3JkID0gY29vcmRzW21haW5BeGlzXTtcbiAgICAgIGxldCBjcm9zc0F4aXNDb29yZCA9IGNvb3Jkc1tjcm9zc0F4aXNdO1xuICAgICAgY29uc3QgcmF3T2Zmc2V0ID0gZXZhbHVhdGUob2Zmc2V0LCBzdGF0ZSk7XG4gICAgICBjb25zdCBjb21wdXRlZE9mZnNldCA9IHR5cGVvZiByYXdPZmZzZXQgPT09ICdudW1iZXInID8ge1xuICAgICAgICBtYWluQXhpczogcmF3T2Zmc2V0LFxuICAgICAgICBjcm9zc0F4aXM6IDBcbiAgICAgIH0gOiB7XG4gICAgICAgIG1haW5BeGlzOiAwLFxuICAgICAgICBjcm9zc0F4aXM6IDAsXG4gICAgICAgIC4uLnJhd09mZnNldFxuICAgICAgfTtcbiAgICAgIGlmIChjaGVja01haW5BeGlzKSB7XG4gICAgICAgIGNvbnN0IGxlbiA9IG1haW5BeGlzID09PSAneScgPyAnaGVpZ2h0JyA6ICd3aWR0aCc7XG4gICAgICAgIGNvbnN0IGxpbWl0TWluID0gcmVjdHMucmVmZXJlbmNlW21haW5BeGlzXSAtIHJlY3RzLmZsb2F0aW5nW2xlbl0gKyBjb21wdXRlZE9mZnNldC5tYWluQXhpcztcbiAgICAgICAgY29uc3QgbGltaXRNYXggPSByZWN0cy5yZWZlcmVuY2VbbWFpbkF4aXNdICsgcmVjdHMucmVmZXJlbmNlW2xlbl0gLSBjb21wdXRlZE9mZnNldC5tYWluQXhpcztcbiAgICAgICAgaWYgKG1haW5BeGlzQ29vcmQgPCBsaW1pdE1pbikge1xuICAgICAgICAgIG1haW5BeGlzQ29vcmQgPSBsaW1pdE1pbjtcbiAgICAgICAgfSBlbHNlIGlmIChtYWluQXhpc0Nvb3JkID4gbGltaXRNYXgpIHtcbiAgICAgICAgICBtYWluQXhpc0Nvb3JkID0gbGltaXRNYXg7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIGlmIChjaGVja0Nyb3NzQXhpcykge1xuICAgICAgICB2YXIgX21pZGRsZXdhcmVEYXRhJG9mZnNlLCBfbWlkZGxld2FyZURhdGEkb2Zmc2UyO1xuICAgICAgICBjb25zdCBsZW4gPSBtYWluQXhpcyA9PT0gJ3knID8gJ3dpZHRoJyA6ICdoZWlnaHQnO1xuICAgICAgICBjb25zdCBpc09yaWdpblNpZGUgPSBbJ3RvcCcsICdsZWZ0J10uaW5jbHVkZXMoZ2V0U2lkZShwbGFjZW1lbnQpKTtcbiAgICAgICAgY29uc3QgbGltaXRNaW4gPSByZWN0cy5yZWZlcmVuY2VbY3Jvc3NBeGlzXSAtIHJlY3RzLmZsb2F0aW5nW2xlbl0gKyAoaXNPcmlnaW5TaWRlID8gKChfbWlkZGxld2FyZURhdGEkb2Zmc2UgPSBtaWRkbGV3YXJlRGF0YS5vZmZzZXQpID09IG51bGwgPyB2b2lkIDAgOiBfbWlkZGxld2FyZURhdGEkb2Zmc2VbY3Jvc3NBeGlzXSkgfHwgMCA6IDApICsgKGlzT3JpZ2luU2lkZSA/IDAgOiBjb21wdXRlZE9mZnNldC5jcm9zc0F4aXMpO1xuICAgICAgICBjb25zdCBsaW1pdE1heCA9IHJlY3RzLnJlZmVyZW5jZVtjcm9zc0F4aXNdICsgcmVjdHMucmVmZXJlbmNlW2xlbl0gKyAoaXNPcmlnaW5TaWRlID8gMCA6ICgoX21pZGRsZXdhcmVEYXRhJG9mZnNlMiA9IG1pZGRsZXdhcmVEYXRhLm9mZnNldCkgPT0gbnVsbCA/IHZvaWQgMCA6IF9taWRkbGV3YXJlRGF0YSRvZmZzZTJbY3Jvc3NBeGlzXSkgfHwgMCkgLSAoaXNPcmlnaW5TaWRlID8gY29tcHV0ZWRPZmZzZXQuY3Jvc3NBeGlzIDogMCk7XG4gICAgICAgIGlmIChjcm9zc0F4aXNDb29yZCA8IGxpbWl0TWluKSB7XG4gICAgICAgICAgY3Jvc3NBeGlzQ29vcmQgPSBsaW1pdE1pbjtcbiAgICAgICAgfSBlbHNlIGlmIChjcm9zc0F4aXNDb29yZCA+IGxpbWl0TWF4KSB7XG4gICAgICAgICAgY3Jvc3NBeGlzQ29vcmQgPSBsaW1pdE1heDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgW21haW5BeGlzXTogbWFpbkF4aXNDb29yZCxcbiAgICAgICAgW2Nyb3NzQXhpc106IGNyb3NzQXhpc0Nvb3JkXG4gICAgICB9O1xuICAgIH1cbiAgfTtcbn07XG5cbi8qKlxuICogUHJvdmlkZXMgZGF0YSB0aGF0IGFsbG93cyB5b3UgdG8gY2hhbmdlIHRoZSBzaXplIG9mIHRoZSBmbG9hdGluZyBlbGVtZW50IOKAlFxuICogZm9yIGluc3RhbmNlLCBwcmV2ZW50IGl0IGZyb20gb3ZlcmZsb3dpbmcgdGhlIGNsaXBwaW5nIGJvdW5kYXJ5IG9yIG1hdGNoIHRoZVxuICogd2lkdGggb2YgdGhlIHJlZmVyZW5jZSBlbGVtZW50LlxuICogQHNlZSBodHRwczovL2Zsb2F0aW5nLXVpLmNvbS9kb2NzL3NpemVcbiAqL1xuY29uc3Qgc2l6ZSA9IGZ1bmN0aW9uIChvcHRpb25zKSB7XG4gIGlmIChvcHRpb25zID09PSB2b2lkIDApIHtcbiAgICBvcHRpb25zID0ge307XG4gIH1cbiAgcmV0dXJuIHtcbiAgICBuYW1lOiAnc2l6ZScsXG4gICAgb3B0aW9ucyxcbiAgICBhc3luYyBmbihzdGF0ZSkge1xuICAgICAgdmFyIF9zdGF0ZSRtaWRkbGV3YXJlRGF0YSwgX3N0YXRlJG1pZGRsZXdhcmVEYXRhMjtcbiAgICAgIGNvbnN0IHtcbiAgICAgICAgcGxhY2VtZW50LFxuICAgICAgICByZWN0cyxcbiAgICAgICAgcGxhdGZvcm0sXG4gICAgICAgIGVsZW1lbnRzXG4gICAgICB9ID0gc3RhdGU7XG4gICAgICBjb25zdCB7XG4gICAgICAgIGFwcGx5ID0gKCkgPT4ge30sXG4gICAgICAgIC4uLmRldGVjdE92ZXJmbG93T3B0aW9uc1xuICAgICAgfSA9IGV2YWx1YXRlKG9wdGlvbnMsIHN0YXRlKTtcbiAgICAgIGNvbnN0IG92ZXJmbG93ID0gYXdhaXQgZGV0ZWN0T3ZlcmZsb3coc3RhdGUsIGRldGVjdE92ZXJmbG93T3B0aW9ucyk7XG4gICAgICBjb25zdCBzaWRlID0gZ2V0U2lkZShwbGFjZW1lbnQpO1xuICAgICAgY29uc3QgYWxpZ25tZW50ID0gZ2V0QWxpZ25tZW50KHBsYWNlbWVudCk7XG4gICAgICBjb25zdCBpc1lBeGlzID0gZ2V0U2lkZUF4aXMocGxhY2VtZW50KSA9PT0gJ3knO1xuICAgICAgY29uc3Qge1xuICAgICAgICB3aWR0aCxcbiAgICAgICAgaGVpZ2h0XG4gICAgICB9ID0gcmVjdHMuZmxvYXRpbmc7XG4gICAgICBsZXQgaGVpZ2h0U2lkZTtcbiAgICAgIGxldCB3aWR0aFNpZGU7XG4gICAgICBpZiAoc2lkZSA9PT0gJ3RvcCcgfHwgc2lkZSA9PT0gJ2JvdHRvbScpIHtcbiAgICAgICAgaGVpZ2h0U2lkZSA9IHNpZGU7XG4gICAgICAgIHdpZHRoU2lkZSA9IGFsaWdubWVudCA9PT0gKChhd2FpdCAocGxhdGZvcm0uaXNSVEwgPT0gbnVsbCA/IHZvaWQgMCA6IHBsYXRmb3JtLmlzUlRMKGVsZW1lbnRzLmZsb2F0aW5nKSkpID8gJ3N0YXJ0JyA6ICdlbmQnKSA/ICdsZWZ0JyA6ICdyaWdodCc7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB3aWR0aFNpZGUgPSBzaWRlO1xuICAgICAgICBoZWlnaHRTaWRlID0gYWxpZ25tZW50ID09PSAnZW5kJyA/ICd0b3AnIDogJ2JvdHRvbSc7XG4gICAgICB9XG4gICAgICBjb25zdCBtYXhpbXVtQ2xpcHBpbmdIZWlnaHQgPSBoZWlnaHQgLSBvdmVyZmxvdy50b3AgLSBvdmVyZmxvdy5ib3R0b207XG4gICAgICBjb25zdCBtYXhpbXVtQ2xpcHBpbmdXaWR0aCA9IHdpZHRoIC0gb3ZlcmZsb3cubGVmdCAtIG92ZXJmbG93LnJpZ2h0O1xuICAgICAgY29uc3Qgb3ZlcmZsb3dBdmFpbGFibGVIZWlnaHQgPSBtaW4oaGVpZ2h0IC0gb3ZlcmZsb3dbaGVpZ2h0U2lkZV0sIG1heGltdW1DbGlwcGluZ0hlaWdodCk7XG4gICAgICBjb25zdCBvdmVyZmxvd0F2YWlsYWJsZVdpZHRoID0gbWluKHdpZHRoIC0gb3ZlcmZsb3dbd2lkdGhTaWRlXSwgbWF4aW11bUNsaXBwaW5nV2lkdGgpO1xuICAgICAgY29uc3Qgbm9TaGlmdCA9ICFzdGF0ZS5taWRkbGV3YXJlRGF0YS5zaGlmdDtcbiAgICAgIGxldCBhdmFpbGFibGVIZWlnaHQgPSBvdmVyZmxvd0F2YWlsYWJsZUhlaWdodDtcbiAgICAgIGxldCBhdmFpbGFibGVXaWR0aCA9IG92ZXJmbG93QXZhaWxhYmxlV2lkdGg7XG4gICAgICBpZiAoKF9zdGF0ZSRtaWRkbGV3YXJlRGF0YSA9IHN0YXRlLm1pZGRsZXdhcmVEYXRhLnNoaWZ0KSAhPSBudWxsICYmIF9zdGF0ZSRtaWRkbGV3YXJlRGF0YS5lbmFibGVkLngpIHtcbiAgICAgICAgYXZhaWxhYmxlV2lkdGggPSBtYXhpbXVtQ2xpcHBpbmdXaWR0aDtcbiAgICAgIH1cbiAgICAgIGlmICgoX3N0YXRlJG1pZGRsZXdhcmVEYXRhMiA9IHN0YXRlLm1pZGRsZXdhcmVEYXRhLnNoaWZ0KSAhPSBudWxsICYmIF9zdGF0ZSRtaWRkbGV3YXJlRGF0YTIuZW5hYmxlZC55KSB7XG4gICAgICAgIGF2YWlsYWJsZUhlaWdodCA9IG1heGltdW1DbGlwcGluZ0hlaWdodDtcbiAgICAgIH1cbiAgICAgIGlmIChub1NoaWZ0ICYmICFhbGlnbm1lbnQpIHtcbiAgICAgICAgY29uc3QgeE1pbiA9IG1heChvdmVyZmxvdy5sZWZ0LCAwKTtcbiAgICAgICAgY29uc3QgeE1heCA9IG1heChvdmVyZmxvdy5yaWdodCwgMCk7XG4gICAgICAgIGNvbnN0IHlNaW4gPSBtYXgob3ZlcmZsb3cudG9wLCAwKTtcbiAgICAgICAgY29uc3QgeU1heCA9IG1heChvdmVyZmxvdy5ib3R0b20sIDApO1xuICAgICAgICBpZiAoaXNZQXhpcykge1xuICAgICAgICAgIGF2YWlsYWJsZVdpZHRoID0gd2lkdGggLSAyICogKHhNaW4gIT09IDAgfHwgeE1heCAhPT0gMCA/IHhNaW4gKyB4TWF4IDogbWF4KG92ZXJmbG93LmxlZnQsIG92ZXJmbG93LnJpZ2h0KSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgYXZhaWxhYmxlSGVpZ2h0ID0gaGVpZ2h0IC0gMiAqICh5TWluICE9PSAwIHx8IHlNYXggIT09IDAgPyB5TWluICsgeU1heCA6IG1heChvdmVyZmxvdy50b3AsIG92ZXJmbG93LmJvdHRvbSkpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICBhd2FpdCBhcHBseSh7XG4gICAgICAgIC4uLnN0YXRlLFxuICAgICAgICBhdmFpbGFibGVXaWR0aCxcbiAgICAgICAgYXZhaWxhYmxlSGVpZ2h0XG4gICAgICB9KTtcbiAgICAgIGNvbnN0IG5leHREaW1lbnNpb25zID0gYXdhaXQgcGxhdGZvcm0uZ2V0RGltZW5zaW9ucyhlbGVtZW50cy5mbG9hdGluZyk7XG4gICAgICBpZiAod2lkdGggIT09IG5leHREaW1lbnNpb25zLndpZHRoIHx8IGhlaWdodCAhPT0gbmV4dERpbWVuc2lvbnMuaGVpZ2h0KSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgcmVzZXQ6IHtcbiAgICAgICAgICAgIHJlY3RzOiB0cnVlXG4gICAgICAgICAgfVxuICAgICAgICB9O1xuICAgICAgfVxuICAgICAgcmV0dXJuIHt9O1xuICAgIH1cbiAgfTtcbn07XG5cbmV4cG9ydCB7IGFycm93LCBhdXRvUGxhY2VtZW50LCBjb21wdXRlUG9zaXRpb24sIGRldGVjdE92ZXJmbG93LCBmbGlwLCBoaWRlLCBpbmxpbmUsIGxpbWl0U2hpZnQsIG9mZnNldCwgc2hpZnQsIHNpemUgfTtcbiJdLCJuYW1lcyI6WyJnZXRTaWRlQXhpcyIsImdldEFsaWdubWVudEF4aXMiLCJnZXRBeGlzTGVuZ3RoIiwiZ2V0U2lkZSIsImdldEFsaWdubWVudCIsImV2YWx1YXRlIiwiZ2V0UGFkZGluZ09iamVjdCIsInJlY3RUb0NsaWVudFJlY3QiLCJtaW4iLCJjbGFtcCIsInBsYWNlbWVudHMiLCJnZXRBbGlnbm1lbnRTaWRlcyIsImdldE9wcG9zaXRlQWxpZ25tZW50UGxhY2VtZW50IiwiZ2V0T3Bwb3NpdGVQbGFjZW1lbnQiLCJnZXRFeHBhbmRlZFBsYWNlbWVudHMiLCJnZXRPcHBvc2l0ZUF4aXNQbGFjZW1lbnRzIiwic2lkZXMiLCJtYXgiLCJnZXRPcHBvc2l0ZUF4aXMiLCJjb21wdXRlQ29vcmRzRnJvbVBsYWNlbWVudCIsIl9yZWYiLCJwbGFjZW1lbnQiLCJydGwiLCJyZWZlcmVuY2UiLCJmbG9hdGluZyIsInNpZGVBeGlzIiwiYWxpZ25tZW50QXhpcyIsImFsaWduTGVuZ3RoIiwic2lkZSIsImlzVmVydGljYWwiLCJjb21tb25YIiwieCIsIndpZHRoIiwiY29tbW9uWSIsInkiLCJoZWlnaHQiLCJjb21tb25BbGlnbiIsImNvb3JkcyIsImNvbXB1dGVQb3NpdGlvbiIsImNvbmZpZyIsInN0cmF0ZWd5IiwibWlkZGxld2FyZSIsInBsYXRmb3JtIiwidmFsaWRNaWRkbGV3YXJlIiwiZmlsdGVyIiwiQm9vbGVhbiIsImlzUlRMIiwicmVjdHMiLCJnZXRFbGVtZW50UmVjdHMiLCJzdGF0ZWZ1bFBsYWNlbWVudCIsIm1pZGRsZXdhcmVEYXRhIiwicmVzZXRDb3VudCIsImkiLCJsZW5ndGgiLCJuYW1lIiwiZm4iLCJuZXh0WCIsIm5leHRZIiwiZGF0YSIsInJlc2V0IiwiaW5pdGlhbFBsYWNlbWVudCIsImVsZW1lbnRzIiwiX29iamVjdFNwcmVhZCIsImRldGVjdE92ZXJmbG93Iiwic3RhdGUiLCJvcHRpb25zIiwiX2F3YWl0JHBsYXRmb3JtJGlzRWxlIiwiYm91bmRhcnkiLCJyb290Qm91bmRhcnkiLCJlbGVtZW50Q29udGV4dCIsImFsdEJvdW5kYXJ5IiwicGFkZGluZyIsInBhZGRpbmdPYmplY3QiLCJhbHRDb250ZXh0IiwiZWxlbWVudCIsImNsaXBwaW5nQ2xpZW50UmVjdCIsImdldENsaXBwaW5nUmVjdCIsImlzRWxlbWVudCIsImNvbnRleHRFbGVtZW50IiwiZ2V0RG9jdW1lbnRFbGVtZW50IiwicmVjdCIsIm9mZnNldFBhcmVudCIsImdldE9mZnNldFBhcmVudCIsIm9mZnNldFNjYWxlIiwiZ2V0U2NhbGUiLCJlbGVtZW50Q2xpZW50UmVjdCIsImNvbnZlcnRPZmZzZXRQYXJlbnRSZWxhdGl2ZVJlY3RUb1ZpZXdwb3J0UmVsYXRpdmVSZWN0IiwidG9wIiwiYm90dG9tIiwibGVmdCIsInJpZ2h0IiwiYXJyb3ciLCJheGlzIiwiYXJyb3dEaW1lbnNpb25zIiwiZ2V0RGltZW5zaW9ucyIsImlzWUF4aXMiLCJtaW5Qcm9wIiwibWF4UHJvcCIsImNsaWVudFByb3AiLCJlbmREaWZmIiwic3RhcnREaWZmIiwiYXJyb3dPZmZzZXRQYXJlbnQiLCJjbGllbnRTaXplIiwiY2VudGVyVG9SZWZlcmVuY2UiLCJsYXJnZXN0UG9zc2libGVQYWRkaW5nIiwibWluUGFkZGluZyIsIm1heFBhZGRpbmciLCJtaW4kMSIsImNlbnRlciIsIm9mZnNldCIsInNob3VsZEFkZE9mZnNldCIsImFsaWdubWVudE9mZnNldCIsImNlbnRlck9mZnNldCIsImdldFBsYWNlbWVudExpc3QiLCJhbGlnbm1lbnQiLCJhdXRvQWxpZ25tZW50IiwiYWxsb3dlZFBsYWNlbWVudHMiLCJhbGxvd2VkUGxhY2VtZW50c1NvcnRlZEJ5QWxpZ25tZW50IiwiYXV0b1BsYWNlbWVudCIsIl9taWRkbGV3YXJlRGF0YSRhdXRvUCIsIl9taWRkbGV3YXJlRGF0YSRhdXRvUDIiLCJfcGxhY2VtZW50c1RoYXRGaXRPbkUiLCJfZXZhbHVhdGUiLCJjcm9zc0F4aXMiLCJkZXRlY3RPdmVyZmxvd09wdGlvbnMiLCJfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMiLCJfZXhjbHVkZWQiLCJwbGFjZW1lbnRzJDEiLCJ1bmRlZmluZWQiLCJvdmVyZmxvdyIsImN1cnJlbnRJbmRleCIsImluZGV4IiwiY3VycmVudFBsYWNlbWVudCIsImFsaWdubWVudFNpZGVzIiwiY3VycmVudE92ZXJmbG93cyIsImFsbE92ZXJmbG93cyIsIm92ZXJmbG93cyIsIm5leHRQbGFjZW1lbnQiLCJwbGFjZW1lbnRzU29ydGVkQnlNb3N0U3BhY2UiLCJtYXAiLCJkIiwic2xpY2UiLCJyZWR1Y2UiLCJhY2MiLCJ2Iiwic29ydCIsImEiLCJiIiwicGxhY2VtZW50c1RoYXRGaXRPbkVhY2hTaWRlIiwiZXZlcnkiLCJyZXNldFBsYWNlbWVudCIsImZsaXAiLCJfbWlkZGxld2FyZURhdGEkYXJyb3ciLCJfbWlkZGxld2FyZURhdGEkZmxpcCIsIl9ldmFsdWF0ZTIiLCJtYWluQXhpcyIsImNoZWNrTWFpbkF4aXMiLCJjaGVja0Nyb3NzQXhpcyIsImZhbGxiYWNrUGxhY2VtZW50cyIsInNwZWNpZmllZEZhbGxiYWNrUGxhY2VtZW50cyIsImZhbGxiYWNrU3RyYXRlZ3kiLCJmYWxsYmFja0F4aXNTaWRlRGlyZWN0aW9uIiwiZmxpcEFsaWdubWVudCIsIl9leGNsdWRlZDIiLCJpbml0aWFsU2lkZUF4aXMiLCJpc0Jhc2VQbGFjZW1lbnQiLCJoYXNGYWxsYmFja0F4aXNTaWRlRGlyZWN0aW9uIiwicHVzaCIsIm92ZXJmbG93c0RhdGEiLCJfbWlkZGxld2FyZURhdGEkZmxpcDIiLCJfb3ZlcmZsb3dzRGF0YSRmaWx0ZXIiLCJuZXh0SW5kZXgiLCJfb3ZlcmZsb3dzRGF0YSQiLCJpZ25vcmVDcm9zc0F4aXNPdmVyZmxvdyIsImhhc0luaXRpYWxNYWluQXhpc092ZXJmbG93IiwiX292ZXJmbG93c0RhdGEkZmlsdGVyMiIsImN1cnJlbnRTaWRlQXhpcyIsImdldFNpZGVPZmZzZXRzIiwiaXNBbnlTaWRlRnVsbHlDbGlwcGVkIiwic29tZSIsImhpZGUiLCJfZXZhbHVhdGUzIiwiX2V4Y2x1ZGVkMyIsIm9mZnNldHMiLCJyZWZlcmVuY2VIaWRkZW5PZmZzZXRzIiwicmVmZXJlbmNlSGlkZGVuIiwiZXNjYXBlZE9mZnNldHMiLCJlc2NhcGVkIiwiZ2V0Qm91bmRpbmdSZWN0IiwibWluWCIsIm1pblkiLCJtYXhYIiwibWF4WSIsImdldFJlY3RzQnlMaW5lIiwic29ydGVkUmVjdHMiLCJncm91cHMiLCJwcmV2UmVjdCIsImlubGluZSIsIm5hdGl2ZUNsaWVudFJlY3RzIiwiQXJyYXkiLCJmcm9tIiwiZ2V0Q2xpZW50UmVjdHMiLCJjbGllbnRSZWN0cyIsImZhbGxiYWNrIiwiZ2V0Qm91bmRpbmdDbGllbnRSZWN0IiwiZmluZCIsImZpcnN0UmVjdCIsImxhc3RSZWN0IiwiaXNUb3AiLCJpc0xlZnRTaWRlIiwibWF4UmlnaHQiLCJtaW5MZWZ0IiwibWVhc3VyZVJlY3RzIiwicmVzZXRSZWN0cyIsImNvbnZlcnRWYWx1ZVRvQ29vcmRzIiwibWFpbkF4aXNNdWx0aSIsImluY2x1ZGVzIiwiY3Jvc3NBeGlzTXVsdGkiLCJyYXdWYWx1ZSIsIl9taWRkbGV3YXJlRGF0YSRvZmZzZSIsImRpZmZDb29yZHMiLCJzaGlmdCIsIl9ldmFsdWF0ZTQiLCJsaW1pdGVyIiwiX2V4Y2x1ZGVkNCIsIm1haW5BeGlzQ29vcmQiLCJjcm9zc0F4aXNDb29yZCIsIm1pblNpZGUiLCJtYXhTaWRlIiwibGltaXRlZENvb3JkcyIsImVuYWJsZWQiLCJsaW1pdFNoaWZ0IiwicmF3T2Zmc2V0IiwiY29tcHV0ZWRPZmZzZXQiLCJsZW4iLCJsaW1pdE1pbiIsImxpbWl0TWF4IiwiX21pZGRsZXdhcmVEYXRhJG9mZnNlMiIsImlzT3JpZ2luU2lkZSIsInNpemUiLCJfc3RhdGUkbWlkZGxld2FyZURhdGEiLCJfc3RhdGUkbWlkZGxld2FyZURhdGEyIiwiX2V2YWx1YXRlNSIsImFwcGx5IiwiX2V4Y2x1ZGVkNSIsImhlaWdodFNpZGUiLCJ3aWR0aFNpZGUiLCJtYXhpbXVtQ2xpcHBpbmdIZWlnaHQiLCJtYXhpbXVtQ2xpcHBpbmdXaWR0aCIsIm92ZXJmbG93QXZhaWxhYmxlSGVpZ2h0Iiwib3ZlcmZsb3dBdmFpbGFibGVXaWR0aCIsIm5vU2hpZnQiLCJhdmFpbGFibGVIZWlnaHQiLCJhdmFpbGFibGVXaWR0aCIsInhNaW4iLCJ4TWF4IiwieU1pbiIsInlNYXgiLCJuZXh0RGltZW5zaW9ucyJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@floating-ui/core/dist/floating-ui.core.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrow: () => (/* binding */ arrow),\n/* harmony export */   autoPlacement: () => (/* binding */ autoPlacement),\n/* harmony export */   autoUpdate: () => (/* binding */ autoUpdate),\n/* harmony export */   computePosition: () => (/* binding */ computePosition),\n/* harmony export */   detectOverflow: () => (/* binding */ detectOverflow),\n/* harmony export */   flip: () => (/* binding */ flip),\n/* harmony export */   getOverflowAncestors: () => (/* reexport safe */ _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors),\n/* harmony export */   hide: () => (/* binding */ hide),\n/* harmony export */   inline: () => (/* binding */ inline),\n/* harmony export */   limitShift: () => (/* binding */ limitShift),\n/* harmony export */   offset: () => (/* binding */ offset),\n/* harmony export */   platform: () => (/* binding */ platform),\n/* harmony export */   shift: () => (/* binding */ shift),\n/* harmony export */   size: () => (/* binding */ size)\n/* harmony export */ });\n/* harmony import */ var _floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @floating-ui/utils */ \"(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs\");\n/* harmony import */ var _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @floating-ui/core */ \"(ssr)/./node_modules/@floating-ui/core/dist/floating-ui.core.mjs\");\n/* harmony import */ var _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @floating-ui/utils/dom */ \"(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs\");\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n\n\n\n\nfunction getCssDimensions(element) {\n  const css = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(width) !== offsetWidth || (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\nfunction unwrapElement(element) {\n  return !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(element) ? element.contextElement : element;\n}\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(domElement)) {\n    return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(rect.width) : rect.width) / width;\n  let y = ($ ? (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\nconst noOffsets = /*#__PURE__*/(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\nfunction getVisualOffsets(element) {\n  const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element);\n  if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isWebKit)() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element)) {\n    return false;\n  }\n  return isFixed;\n}\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(domElement);\n    const offsetWin = offsetParent && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(offsetParent) ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(offsetParent) : offsetParent;\n    let currentWin = win;\n    let currentIFrame = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getFrameElement)(currentWin);\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentWin = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(currentIFrame);\n      currentIFrame = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getFrameElement)(currentWin);\n    }\n  }\n  return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.rectToClientRect)({\n    width,\n    height,\n    x,\n    y\n  });\n}\n\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n  const leftScroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(element).scrollLeft;\n  if (!rect) {\n    return getBoundingClientRect((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element)).left + leftScroll;\n  }\n  return rect.left + leftScroll;\n}\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\n  if (ignoreScrollbarX === void 0) {\n    ignoreScrollbarX = false;\n  }\n  const htmlRect = documentElement.getBoundingClientRect();\n  const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 :\n  // RTL <body> scrollbar.\n  getWindowScrollBarX(documentElement, htmlRect));\n  const y = htmlRect.top + scroll.scrollTop;\n  return {\n    x,\n    y\n  };\n}\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isFixed = strategy === 'fixed';\n  const documentElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(offsetParent);\n  const topLayer = elements ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTopLayer)(elements.floating) : false;\n  if (offsetParent === documentElement || topLayer && isFixed) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n  const offsets = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n  const isOffsetParentAnElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(offsetParent);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeName)(offsetParent) !== 'body' || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isOverflowElement)(documentElement)) {\n      scroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(offsetParent);\n    }\n    if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n  };\n}\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element);\n  const scroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(element);\n  const body = element.ownerDocument.body;\n  const width = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(body).direction === 'rtl') {\n    x += (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getViewportRect(element, strategy) {\n  const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element);\n  const html = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isWebKit)();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) ? getScale(element) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element));\n  } else if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y,\n      width: clippingAncestor.width,\n      height: clippingAncestor.height\n    };\n  }\n  return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.rectToClientRect)(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(element);\n  if (parentNode === stopNode || !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(parentNode) || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(parentNode)) {\n    return false;\n  }\n  return (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors)(element, [], false).filter(el => (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(el) && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeName)(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).position === 'fixed';\n  let currentNode = elementIsFixed ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(currentNode) && !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(currentNode)) {\n    const computedStyle = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(currentNode);\n    const currentNodeIsContaining = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isContainingBlock)(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isOverflowElement)(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTopLayer)(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(rect.top, accRect.top);\n    accRect.right = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.min)(rect.right, accRect.right);\n    accRect.bottom = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.min)(rect.bottom, accRect.bottom);\n    accRect.left = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(offsetParent);\n  const documentElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n\n  // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n  // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n  function setLeftRTLScrollbarOffset() {\n    offsets.x = getWindowScrollBarX(documentElement);\n  }\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeName)(offsetParent) !== 'body' || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isOverflowElement)(documentElement)) {\n      scroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      setLeftRTLScrollbarOffset();\n    }\n  }\n  if (isFixed && !isOffsetParentAnElement && documentElement) {\n    setLeftRTLScrollbarOffset();\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n  const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n  const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n  return {\n    x,\n    y,\n    width: rect.width,\n    height: rect.height\n  };\n}\nfunction isStaticPositioned(element) {\n  return (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).position === 'static';\n}\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  let rawOffsetParent = element.offsetParent;\n\n  // Firefox returns the <html> element as the offsetParent if it's non-static,\n  // while Chrome and Safari return the <body> element. The <body> element must\n  // be used to perform the correct calculations even if the <html> element is\n  // non-static.\n  if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element) === rawOffsetParent) {\n    rawOffsetParent = rawOffsetParent.ownerDocument.body;\n  }\n  return rawOffsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element);\n  if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTopLayer)(element)) {\n    return win;\n  }\n  if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element)) {\n    let svgOffsetParent = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(element);\n    while (svgOffsetParent && !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(svgOffsetParent)) {\n      if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n        return svgOffsetParent;\n      }\n      svgOffsetParent = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(svgOffsetParent);\n    }\n    return win;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTableElement)(offsetParent) && isStaticPositioned(offsetParent)) {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(offsetParent) && isStaticPositioned(offsetParent) && !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isContainingBlock)(offsetParent)) {\n    return win;\n  }\n  return offsetParent || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getContainingBlock)(element) || win;\n}\nconst getElementRects = async function (data) {\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  const floatingDimensions = await getDimensionsFn(data.floating);\n  return {\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      width: floatingDimensions.width,\n      height: floatingDimensions.height\n    }\n  };\n};\nfunction isRTL(element) {\n  return (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).direction === 'rtl';\n}\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement: _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement: _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement,\n  isRTL\n};\nfunction rectsAreEqual(a, b) {\n  return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element);\n  function cleanup() {\n    var _io;\n    clearTimeout(timeoutId);\n    (_io = io) == null || _io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const elementRectForRootMargin = element.getBoundingClientRect();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = elementRectForRootMargin;\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(top);\n    const insetRight = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(root.clientWidth - (left + width));\n    const insetBottom = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(root.clientHeight - (top + height));\n    const insetLeft = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(0, (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.min)(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\n          // to prevent an infinite loop of updates.\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 1000);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n        // It's possible that even though the ratio is reported as 1, the\n        // element is not actually fully within the IntersectionObserver's root\n        // area anymore. This can happen under performance constraints. This may\n        // be a bug in the browser's IntersectionObserver implementation. To\n        // work around this, we compare the element's bounding rect now with\n        // what it was at the time we created the IntersectionObserver. If they\n        // are not equal then the element moved, so we refresh.\n        refresh();\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, _objectSpread(_objectSpread({}, options), {}, {\n        // Handle <iframe>s\n        root: root.ownerDocument\n      }));\n    } catch (_e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors)(referenceEl) : []), ...(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors)(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _resizeObserver2;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo == null || cleanupIo();\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nconst detectOverflow = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.detectOverflow;\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.offset;\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.autoPlacement;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.shift;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.flip;\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.size;\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.hide;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.arrow;\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.inline;\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.limitShift;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = _objectSpread({\n    platform\n  }, options);\n  const platformWithCache = _objectSpread(_objectSpread({}, mergedOptions.platform), {}, {\n    _c: cache\n  });\n  return (0,_floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.computePosition)(reference, floating, _objectSpread(_objectSpread({}, mergedOptions), {}, {\n    platform: platformWithCache\n  }));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getComputedStyle: () => (/* binding */ getComputedStyle),\n/* harmony export */   getContainingBlock: () => (/* binding */ getContainingBlock),\n/* harmony export */   getDocumentElement: () => (/* binding */ getDocumentElement),\n/* harmony export */   getFrameElement: () => (/* binding */ getFrameElement),\n/* harmony export */   getNearestOverflowAncestor: () => (/* binding */ getNearestOverflowAncestor),\n/* harmony export */   getNodeName: () => (/* binding */ getNodeName),\n/* harmony export */   getNodeScroll: () => (/* binding */ getNodeScroll),\n/* harmony export */   getOverflowAncestors: () => (/* binding */ getOverflowAncestors),\n/* harmony export */   getParentNode: () => (/* binding */ getParentNode),\n/* harmony export */   getWindow: () => (/* binding */ getWindow),\n/* harmony export */   isContainingBlock: () => (/* binding */ isContainingBlock),\n/* harmony export */   isElement: () => (/* binding */ isElement),\n/* harmony export */   isHTMLElement: () => (/* binding */ isHTMLElement),\n/* harmony export */   isLastTraversableNode: () => (/* binding */ isLastTraversableNode),\n/* harmony export */   isNode: () => (/* binding */ isNode),\n/* harmony export */   isOverflowElement: () => (/* binding */ isOverflowElement),\n/* harmony export */   isShadowRoot: () => (/* binding */ isShadowRoot),\n/* harmony export */   isTableElement: () => (/* binding */ isTableElement),\n/* harmony export */   isTopLayer: () => (/* binding */ isTopLayer),\n/* harmony export */   isWebKit: () => (/* binding */ isWebKit)\n/* harmony export */ });\nfunction hasWindow() {\n  return false;\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isTopLayer(element) {\n  return [':popover-open', ':modal'].some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (e) {\n      return false;\n    }\n  });\n}\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return ['transform', 'translate', 'scale', 'rotate', 'perspective'].some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alignments: () => (/* binding */ alignments),\n/* harmony export */   clamp: () => (/* binding */ clamp),\n/* harmony export */   createCoords: () => (/* binding */ createCoords),\n/* harmony export */   evaluate: () => (/* binding */ evaluate),\n/* harmony export */   expandPaddingObject: () => (/* binding */ expandPaddingObject),\n/* harmony export */   floor: () => (/* binding */ floor),\n/* harmony export */   getAlignment: () => (/* binding */ getAlignment),\n/* harmony export */   getAlignmentAxis: () => (/* binding */ getAlignmentAxis),\n/* harmony export */   getAlignmentSides: () => (/* binding */ getAlignmentSides),\n/* harmony export */   getAxisLength: () => (/* binding */ getAxisLength),\n/* harmony export */   getExpandedPlacements: () => (/* binding */ getExpandedPlacements),\n/* harmony export */   getOppositeAlignmentPlacement: () => (/* binding */ getOppositeAlignmentPlacement),\n/* harmony export */   getOppositeAxis: () => (/* binding */ getOppositeAxis),\n/* harmony export */   getOppositeAxisPlacements: () => (/* binding */ getOppositeAxisPlacements),\n/* harmony export */   getOppositePlacement: () => (/* binding */ getOppositePlacement),\n/* harmony export */   getPaddingObject: () => (/* binding */ getPaddingObject),\n/* harmony export */   getSide: () => (/* binding */ getSide),\n/* harmony export */   getSideAxis: () => (/* binding */ getSideAxis),\n/* harmony export */   max: () => (/* binding */ max),\n/* harmony export */   min: () => (/* binding */ min),\n/* harmony export */   placements: () => (/* binding */ placements),\n/* harmony export */   rectToClientRect: () => (/* binding */ rectToClientRect),\n/* harmony export */   round: () => (/* binding */ round),\n/* harmony export */   sides: () => (/* binding */ sides)\n/* harmony export */ });\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return _objectSpread({\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  }, padding);\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs\n");

/***/ })

};
;