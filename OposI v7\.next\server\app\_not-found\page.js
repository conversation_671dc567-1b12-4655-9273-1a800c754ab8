/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClientLayout.tsx */ \"(rsc)/./src/components/ClientLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDT3Bvc0klMjB2NyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDT3Bvc0klMjB2NyU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNDbGllbnRMYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEtBQW1LIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcbmFhdGFcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcT3Bvc0lcXFxcT3Bvc0kgdjdcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcQ2xpZW50TGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"cda73054f454\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiY2RhNzMwNTRmNDU0XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_ClientLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ClientLayout */ \"(rsc)/./src/components/ClientLayout.tsx\");\n\n\n\nconst metadata = {\n    title: 'OposiAI - Asistente IA para Oposiciones',\n    description: 'Aplicación de preguntas y respuestas con IA para temarios de oposiciones'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"es\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUN1QjtBQUM4QjtBQUU5QyxNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFVO3NCQUNkLDRFQUFDVCxnRUFBWUE7MEJBQ1ZLOzs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXE9wb3NJIHY3XFxzcmNcXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCc7XG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnO1xuaW1wb3J0IENsaWVudExheW91dCBmcm9tICdAL2NvbXBvbmVudHMvQ2xpZW50TGF5b3V0JztcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdPcG9zaUFJIC0gQXNpc3RlbnRlIElBIHBhcmEgT3Bvc2ljaW9uZXMnLFxuICBkZXNjcmlwdGlvbjogJ0FwbGljYWNpw7NuIGRlIHByZWd1bnRhcyB5IHJlc3B1ZXN0YXMgY29uIElBIHBhcmEgdGVtYXJpb3MgZGUgb3Bvc2ljaW9uZXMnLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZXNcIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT1cImZvbnQtc2FucyBiZy1ncmF5LTEwMFwiPlxuICAgICAgICA8Q2xpZW50TGF5b3V0PlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9DbGllbnRMYXlvdXQ+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkNsaWVudExheW91dCIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ClientLayout.tsx":
/*!*****************************************!*\
  !*** ./src/components/ClientLayout.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\ClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\OposI v7\\src\\components\\ClientLayout.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClientLayout.tsx */ \"(ssr)/./src/components/ClientLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDT3Bvc0klMjB2NyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDT3Bvc0klMjB2NyU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNDbGllbnRMYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEtBQW1LIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcbmFhdGFcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcT3Bvc0lcXFxcT3Bvc0kgdjdcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcQ2xpZW50TGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5COposI%20v7%5C%5Csrc%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/AuthManager.tsx":
/*!****************************************!*\
  !*** ./src/components/AuthManager.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthManager)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n/**\n * Componente para manejar errores comunes de autenticación\n * y sincronización de tiempo en Supabase\n */ function AuthManager() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"AuthManager.useEffect\": ()=>{\n            // Verificar si hay problemas de sincronización de tiempo\n            const checkTimeSync = {\n                \"AuthManager.useEffect.checkTimeSync\": async ()=>{\n                    try {\n                        // Usar variables de entorno para la configuración de Supabase\n                        const supabaseUrl = \"https://fxnhpxjijinfuxxxplzj.supabase.co\";\n                        const supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\";\n                        if (!supabaseUrl || !supabaseKey) {\n                            console.warn('Variables de entorno de Supabase no configuradas');\n                            return;\n                        }\n                        // Obtener la hora del servidor de Supabase\n                        const response = await fetch(`${supabaseUrl}/rest/v1/`, {\n                            method: 'GET',\n                            headers: {\n                                'Content-Type': 'application/json',\n                                'apikey': supabaseKey\n                            }\n                        });\n                        // Obtener la fecha del servidor desde las cabeceras\n                        const serverDate = new Date(response.headers.get('date') || '');\n                        const clientDate = new Date();\n                        // Calcular la diferencia en segundos\n                        const timeDiff = Math.abs((serverDate.getTime() - clientDate.getTime()) / 1000);\n                        // Si la diferencia es mayor a 60 segundos, mostrar una advertencia\n                        if (timeDiff > 60) {\n                            console.warn(`Posible problema de sincronización de tiempo detectado. ` + `La diferencia entre tu hora local y el servidor es de ${Math.round(timeDiff)} segundos. ` + `Esto puede causar problemas de autenticación.`);\n                        }\n                    } catch (error) {\n                        console.error('Error al verificar sincronización de tiempo:', error);\n                    }\n                }\n            }[\"AuthManager.useEffect.checkTimeSync\"];\n            // Ejecutar la verificación\n            checkTimeSync();\n            // Configurar un listener para eventos de autenticación\n            const { data: authListener } = _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.onAuthStateChange({\n                \"AuthManager.useEffect\": (event, session)=>{\n                    console.log('[AuthManager] Auth event:', event, 'Session:', !!session);\n                    if (event === 'SIGNED_OUT') {\n                        // Supabase ya maneja la limpieza de tokens internamente\n                        console.log('[AuthManager] Sesión cerrada');\n                    } else if (event === 'SIGNED_IN') {\n                        console.log('[AuthManager] Sesión iniciada correctamente');\n                    }\n                }\n            }[\"AuthManager.useEffect\"]);\n            return ({\n                \"AuthManager.useEffect\": ()=>{\n                    authListener.subscription.unsubscribe();\n                }\n            })[\"AuthManager.useEffect\"];\n        }\n    }[\"AuthManager.useEffect\"], []);\n    // Este componente no renderiza nada visible\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AuthManager.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ClientLayout.tsx":
/*!*****************************************!*\
  !*** ./src/components/ClientLayout.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_AuthManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthManager */ \"(ssr)/./src/components/AuthManager.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n // Importar Toaster\nfunction ClientLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\ClientLayout.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.Toaster, {\n                position: \"top-right\" // Posición de los toasts\n                ,\n                toastOptions: {\n                    // Opciones por defecto para los toasts\n                    duration: 5000,\n                    style: {\n                        background: '#363636',\n                        color: '#fff'\n                    },\n                    success: {\n                        duration: 3000,\n                        theme: {\n                            primary: 'green',\n                            secondary: 'black'\n                        }\n                    },\n                    error: {\n                        duration: 5000,\n                        theme: {\n                            primary: 'red',\n                            secondary: 'black'\n                        }\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\ClientLayout.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\ClientLayout.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ClientLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _lib_supabase_authService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase/authService */ \"(ssr)/./src/lib/supabase/authService.ts\");\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); // Start true: loading initial auth state\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Effect for auth state listener and initial session check\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            setIsLoading(true); // Explicitly set loading true at the start of auth setup\n            const { data: authListener } = _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": (event, currentSession)=>{\n                    setSession(currentSession);\n                    setUser(currentSession?.user ?? null);\n                    setError(null); // Clear previous errors on any auth state change\n                    // Centralize setIsLoading(false) after processing the event.\n                    if (event === 'INITIAL_SESSION' || event === 'SIGNED_IN' || event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED' || event === 'USER_UPDATED' || event === 'PASSWORD_RECOVERY') {\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect\"]);\n            // Initial session fetch. onAuthStateChange with INITIAL_SESSION will also fire.\n            _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession().then({\n                \"AuthProvider.useEffect\": ({ data: { session: initialSessionCheck }, error: getSessionError })=>{\n                    if (getSessionError) {\n                        setError(getSessionError.message);\n                        setIsLoading(false); // Ensure loading is false if initial getSession fails\n                    }\n                    // Para dispositivos móviles, verificar también localStorage si no hay sesión\n                    if (!initialSessionCheck && \"undefined\" !== 'undefined') {}\n                // If INITIAL_SESSION hasn't fired and set loading to false, and this fails, we ensure it's false.\n                // Note: if getSession is successful, `setIsLoading(false)` is primarily handled by INITIAL_SESSION event.\n                }\n            }[\"AuthProvider.useEffect\"]).catch({\n                \"AuthProvider.useEffect\": (error)=>{\n                    setError(error.message);\n                    setIsLoading(false); // Ensure loading is false if initial getSession throws\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    authListener?.subscription.unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []); // Runs once on mount\n    // Effect for handling redirections based on auth state and pathname\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // No realizar redirecciones mientras se está cargando\n            if (isLoading) {\n                return;\n            }\n            // No aplicar redirecciones a rutas de API o recursos estáticos\n            if (pathname.startsWith('/api') || pathname.startsWith('/_next')) {\n                return;\n            }\n            // Definir rutas públicas (mantener sincronizado con middleware)\n            const publicPaths = [\n                '/login'\n            ];\n            // Si hay sesión y estamos en /login, el middleware ya debería haber redirigido.\n            // Esta es una salvaguarda del lado del cliente.\n            if (session && pathname === '/login') {\n                router.replace('/'); // router.replace es mejor aquí para evitar entradas en el historial\n                return; // Importante retornar para no evaluar la siguiente condición\n            }\n            // Si NO hay sesión y NO estamos en una ruta pública (y no es una ruta API/interna)\n            // Esta lógica es para cuando el estado cambia en el cliente (ej. logout)\n            if (!session && !publicPaths.includes(pathname) && !pathname.startsWith('/api') && !pathname.startsWith('/_next')) {\n                router.replace('/login');\n                return;\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        session,\n        isLoading,\n        pathname,\n        router\n    ]);\n    const iniciarSesion = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[iniciarSesion]\": async (email, password_provided)=>{\n            setIsLoading(true);\n            setError(null);\n            try {\n                const { user: loggedInUser, session: currentAuthSession, error: loginError } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_4__.iniciarSesion)(email, password_provided);\n                if (loginError) {\n                    setError(loginError);\n                    setIsLoading(false); // Ensure loading is false on error\n                    return {\n                        user: null,\n                        session: null,\n                        error: loginError\n                    };\n                }\n                // Verificar que la sesión se haya establecido correctamente antes de redirigir\n                if (currentAuthSession) {\n                    // Esperar un momento adicional para asegurar que las cookies se propaguen\n                    // antes de la redirección\n                    await new Promise({\n                        \"AuthProvider.useCallback[iniciarSesion]\": (resolve)=>setTimeout(resolve, 300)\n                    }[\"AuthProvider.useCallback[iniciarSesion]\"]);\n                    // Redirigir a la página principal usando replace para evitar entradas en el historial\n                    router.replace('/');\n                }\n                // If successful, onAuthStateChange (SIGNED_IN) will set user, session, and isLoading to false.\n                return {\n                    user: loggedInUser,\n                    session: currentAuthSession,\n                    error: null\n                };\n            } catch (e) {\n                const errorMessage = e instanceof Error && e.message ? e.message : 'Error desconocido durante el inicio de sesión.';\n                setError(errorMessage);\n                setIsLoading(false); // Ensure loading is false on exception\n                return {\n                    user: null,\n                    session: null,\n                    error: errorMessage\n                };\n            }\n        }\n    }[\"AuthProvider.useCallback[iniciarSesion]\"], [\n        router\n    ]); // Added router dependency\n    const cerrarSesion = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[cerrarSesion]\": async ()=>{\n            setIsLoading(true);\n            setError(null);\n            const { error: logoutError } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_4__.cerrarSesion)();\n            if (logoutError) {\n                setError(logoutError);\n                setIsLoading(false); // Ensure loading is false on error\n            }\n        // If successful, onAuthStateChange (SIGNED_OUT) handles state updates and isLoading.\n        // The redirection useEffect will then handle redirecting to /login.\n        }\n    }[\"AuthProvider.useCallback[cerrarSesion]\"], []); // Assuming cerrarSesionService is a stable import\n    const estaAutenticado = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[estaAutenticado]\": ()=>!!user && !!session && !isLoading\n    }[\"AuthProvider.useCallback[estaAutenticado]\"], [\n        user,\n        session,\n        isLoading\n    ]);\n    const value = {\n        user,\n        session,\n        isLoading,\n        error,\n        iniciarSesion,\n        cerrarSesion,\n        estaAutenticado\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 181,\n        columnNumber: 10\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth debe ser utilizado dentro de un AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/authService.ts":
/*!*****************************************!*\
  !*** ./src/lib/supabase/authService.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cerrarSesion: () => (/* binding */ cerrarSesion),\n/* harmony export */   estaAutenticado: () => (/* binding */ estaAutenticado),\n/* harmony export */   iniciarSesion: () => (/* binding */ iniciarSesion),\n/* harmony export */   obtenerSesion: () => (/* binding */ obtenerSesion),\n/* harmony export */   obtenerUsuarioActual: () => (/* binding */ obtenerUsuarioActual)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n\n/**\n * Inicia sesión con email y contraseña\n */ async function iniciarSesion(email, password) {\n    try {\n        // Verificar que el email y la contraseña no estén vacíos\n        if (!email || !password) {\n            return {\n                user: null,\n                session: null,\n                error: 'Por favor, ingresa tu email y contraseña'\n            };\n        }\n        // No cerramos la sesión antes de iniciar una nueva, esto causa un ciclo\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email: email.trim(),\n            password: password\n        });\n        if (error) {\n            // Manejar específicamente el error de sincronización de tiempo\n            if (error.message.includes('issued in the future') || error.message.includes('clock for skew')) {\n                return {\n                    user: null,\n                    session: null,\n                    error: 'Error de sincronización de tiempo. Por favor, verifica que la hora de tu dispositivo esté correctamente configurada.'\n                };\n            }\n            // Manejar error de credenciales inválidas de forma más amigable\n            if (error.message.includes('Invalid login credentials')) {\n                return {\n                    user: null,\n                    session: null,\n                    error: 'Email o contraseña incorrectos. Por favor, verifica tus credenciales.'\n                };\n            }\n            return {\n                user: null,\n                session: null,\n                error: error.message\n            }; // Added session\n        }\n        // Ensure data.user and data.session exist before returning\n        if (data && data.user && data.session) {\n            // Esperar un momento para asegurar que las cookies se establezcan\n            // Esto es importante para que el middleware pueda detectar la sesión\n            await new Promise((resolve)=>setTimeout(resolve, 800));\n            // Verificar que la sesión esté disponible después de establecer las cookies\n            await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            return {\n                user: data.user,\n                session: data.session,\n                error: null\n            }; // Added session\n        } else {\n            // This case should ideally not be reached if Supabase call is successful\n            // but provides a fallback if data or its properties are unexpectedly null/undefined.\n            return {\n                user: null,\n                session: null,\n                error: 'Respuesta inesperada del servidor al iniciar sesión.'\n            };\n        }\n    } catch (e) {\n        // Check if 'e' is an Error object and has a message property\n        const errorMessage = e instanceof Error && e.message ? e.message : 'Ha ocurrido un error inesperado al iniciar sesión';\n        return {\n            user: null,\n            session: null,\n            error: errorMessage\n        };\n    }\n}\n/**\n * Cierra la sesión del usuario actual\n */ async function cerrarSesion() {\n    try {\n        const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut();\n        if (error) {\n            return {\n                error: error.message\n            };\n        }\n        return {\n            error: null\n        };\n    } catch (error) {\n        return {\n            error: 'Ha ocurrido un error inesperado al cerrar sesión'\n        };\n    }\n}\n/**\n * Obtiene la sesión actual del usuario\n */ async function obtenerSesion() {\n    try {\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    session: null,\n                    error: null\n                };\n            }\n            return {\n                session: null,\n                error: error.message\n            };\n        }\n        return {\n            session: data.session,\n            error: null\n        };\n    } catch (error) {\n        return {\n            session: null,\n            error: 'Ha ocurrido un error inesperado al obtener la sesión'\n        };\n    }\n}\n/**\n * Obtiene el usuario actual\n */ async function obtenerUsuarioActual() {\n    try {\n        const { data: { user }, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    user: null,\n                    error: null\n                };\n            }\n            return {\n                user: null,\n                error: error.message\n            };\n        }\n        return {\n            user,\n            error: null\n        };\n    } catch (error) {\n        return {\n            user: null,\n            error: 'Ha ocurrido un error inesperado al obtener el usuario actual'\n        };\n    }\n}\n/**\n * Verifica si el usuario está autenticado\n */ async function estaAutenticado() {\n    const { session } = await obtenerSesion();\n    return session !== null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/authService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/supabaseClient.ts":
/*!********************************************!*\
  !*** ./src/lib/supabase/supabaseClient.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSupabaseClient: () => (/* binding */ getSupabaseClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Configuración del cliente de Supabase\nconst supabaseUrl = \"https://fxnhpxjijinfuxxxplzj.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\";\n// Opciones adicionales para el cliente de Supabase\nconst supabaseOptions = {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true,\n        // Configuración mejorada para dispositivos móviles\n        storageKey: 'supabase.auth.token',\n        storage: {\n            getItem: (key)=>{\n                if (false) {}\n                return null;\n            },\n            setItem: (key, value)=>{\n                if (false) {}\n            },\n            removeItem: (key)=>{\n                if (false) {}\n            }\n        }\n    }\n};\n// Implementar patrón singleton para evitar múltiples instancias\nlet supabaseInstance = null;\nconst getSupabaseClient = ()=>{\n    if (!supabaseUrl || !supabaseAnonKey) {\n        throw new Error('Supabase URL or anon key is not defined. Make sure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set in your environment variables.');\n    }\n    if (supabaseInstance === null) {\n        supabaseInstance = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, supabaseOptions);\n    }\n    return supabaseInstance;\n};\n// Exportar una instancia única del cliente de Supabase\nconst supabase = getSupabaseClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/supabaseClient.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();