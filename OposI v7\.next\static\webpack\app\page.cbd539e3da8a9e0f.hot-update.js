"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activarConversacion: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.activarConversacion),\n/* harmony export */   actualizarConversacion: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.actualizarConversacion),\n/* harmony export */   actualizarFlashcard: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.actualizarFlashcard),\n/* harmony export */   actualizarProgresoFlashcard: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.actualizarProgresoFlashcard),\n/* harmony export */   crearColeccionFlashcards: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.crearColeccionFlashcards),\n/* harmony export */   crearConversacion: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.crearConversacion),\n/* harmony export */   crearFlashcard: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.crearFlashcard),\n/* harmony export */   crearPreguntaTest: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.crearPreguntaTest),\n/* harmony export */   crearTest: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.crearTest),\n/* harmony export */   createClient: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.createClient),\n/* harmony export */   desactivarTodasLasConversaciones: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.desactivarTodasLasConversaciones),\n/* harmony export */   eliminarDocumento: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.eliminarDocumento),\n/* harmony export */   eliminarFlashcard: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.eliminarFlashcard),\n/* harmony export */   guardarDocumento: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.guardarDocumento),\n/* harmony export */   guardarFlashcards: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.guardarFlashcards),\n/* harmony export */   guardarMensaje: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.guardarMensaje),\n/* harmony export */   guardarPreguntasTest: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.guardarPreguntasTest),\n/* harmony export */   guardarRevisionHistorial: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.guardarRevisionHistorial),\n/* harmony export */   obtenerColeccionFlashcardsPorId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerColeccionFlashcardsPorId),\n/* harmony export */   obtenerColeccionesFlashcards: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerColeccionesFlashcards),\n/* harmony export */   obtenerConversacionActiva: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerConversacionActiva),\n/* harmony export */   obtenerConversacionPorId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerConversacionPorId),\n/* harmony export */   obtenerConversaciones: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerConversaciones),\n/* harmony export */   obtenerDocumentoPorId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerDocumentoPorId),\n/* harmony export */   obtenerDocumentos: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerDocumentos),\n/* harmony export */   obtenerEstadisticasColeccion: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerEstadisticasColeccion),\n/* harmony export */   obtenerEstadisticasDetalladas: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerEstadisticasDetalladas),\n/* harmony export */   obtenerEstadisticasEstudio: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerEstadisticasEstudio),\n/* harmony export */   obtenerEstadisticasGeneralesTests: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerEstadisticasGeneralesTests),\n/* harmony export */   obtenerEstadisticasTest: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerEstadisticasTest),\n/* harmony export */   obtenerFlashcardPorId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerFlashcardPorId),\n/* harmony export */   obtenerFlashcardsParaEstudiar: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerFlashcardsParaEstudiar),\n/* harmony export */   obtenerFlashcardsPorColeccion: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerFlashcardsPorColeccion),\n/* harmony export */   obtenerFlashcardsPorColeccionId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerFlashcardsPorColeccionId),\n/* harmony export */   obtenerHistorialRevisiones: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerHistorialRevisiones),\n/* harmony export */   obtenerMensajesPorConversacionId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerMensajesPorConversacionId),\n/* harmony export */   obtenerPreguntasPorTestId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerPreguntasPorTestId),\n/* harmony export */   obtenerPreguntasTestCount: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerPreguntasTestCount),\n/* harmony export */   obtenerProgresoFlashcard: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerProgresoFlashcard),\n/* harmony export */   obtenerTestPorId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerTestPorId),\n/* harmony export */   obtenerTests: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerTests),\n/* harmony export */   registrarRespuestaFlashcard: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.registrarRespuestaFlashcard),\n/* harmony export */   registrarRespuestaTest: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.registrarRespuestaTest),\n/* harmony export */   reiniciarProgresoFlashcard: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.reiniciarProgresoFlashcard),\n/* harmony export */   supabase: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase/index */ \"(app-pages-browser)/./src/lib/supabase/index.ts\");\n// Este archivo es un punto de entrada para mantener la compatibilidad con el código existente\n// Redirige todas las exportaciones a la nueva estructura modular\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc3VwYWJhc2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLDhGQUE4RjtBQUM5RixpRUFBaUU7QUFFaEMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXE9wb3NJIHY3XFxzcmNcXGxpYlxcc3VwYWJhc2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXN0ZSBhcmNoaXZvIGVzIHVuIHB1bnRvIGRlIGVudHJhZGEgcGFyYSBtYW50ZW5lciBsYSBjb21wYXRpYmlsaWRhZCBjb24gZWwgY8OzZGlnbyBleGlzdGVudGVcbi8vIFJlZGlyaWdlIHRvZGFzIGxhcyBleHBvcnRhY2lvbmVzIGEgbGEgbnVldmEgZXN0cnVjdHVyYSBtb2R1bGFyXG5cbmV4cG9ydCAqIGZyb20gXCIuL3N1cGFiYXNlL2luZGV4XCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/supabase/flashcardsService.ts":
/*!***********************************************!*\
  !*** ./src/lib/supabase/flashcardsService.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   actualizarFlashcard: () => (/* binding */ actualizarFlashcard),\n/* harmony export */   actualizarProgresoFlashcard: () => (/* binding */ actualizarProgresoFlashcard),\n/* harmony export */   crearColeccionFlashcards: () => (/* binding */ crearColeccionFlashcards),\n/* harmony export */   crearFlashcard: () => (/* binding */ crearFlashcard),\n/* harmony export */   eliminarFlashcard: () => (/* binding */ eliminarFlashcard),\n/* harmony export */   guardarFlashcards: () => (/* binding */ guardarFlashcards),\n/* harmony export */   guardarRevisionHistorial: () => (/* binding */ guardarRevisionHistorial),\n/* harmony export */   obtenerColeccionFlashcardsPorId: () => (/* binding */ obtenerColeccionFlashcardsPorId),\n/* harmony export */   obtenerColeccionesFlashcards: () => (/* binding */ obtenerColeccionesFlashcards),\n/* harmony export */   obtenerFlashcardPorId: () => (/* binding */ obtenerFlashcardPorId),\n/* harmony export */   obtenerFlashcardsParaEstudiar: () => (/* binding */ obtenerFlashcardsParaEstudiar),\n/* harmony export */   obtenerFlashcardsPorColeccion: () => (/* binding */ obtenerFlashcardsPorColeccion),\n/* harmony export */   obtenerFlashcardsPorColeccionId: () => (/* binding */ obtenerFlashcardsPorColeccionId),\n/* harmony export */   obtenerProgresoFlashcard: () => (/* binding */ obtenerProgresoFlashcard),\n/* harmony export */   registrarRespuestaFlashcard: () => (/* binding */ registrarRespuestaFlashcard),\n/* harmony export */   reiniciarProgresoFlashcard: () => (/* binding */ reiniciarProgresoFlashcard)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./authService */ \"(app-pages-browser)/./src/lib/supabase/authService.ts\");\n\n\n/**\n * Crea una nueva colección de flashcards\n */ async function crearColeccionFlashcards(titulo, descripcion) {\n    try {\n        var _data_;\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').insert([\n            {\n                titulo,\n                descripcion,\n                user_id: user.id\n            }\n        ]).select();\n        if (error) {\n            console.error('Error al crear colección de flashcards:', error);\n            return null;\n        }\n        return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n    } catch (error) {\n        console.error('Error al crear colección de flashcards:', error);\n        return null;\n    }\n}\n/**\n * Obtiene todas las colecciones de flashcards del usuario actual\n */ async function obtenerColeccionesFlashcards() {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return [];\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').select('*, flashcards(count)') // Fetch all columns from colecciones_flashcards and the count of related flashcards\n        .eq('user_id', user.id).order('creado_en', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener colecciones de flashcards:', error);\n            return [];\n        }\n        // Supabase returns the count in an array, e.g., flashcards: [{ count: 5 }]\n        // We need to transform this to a direct property numero_flashcards\n        return (data === null || data === void 0 ? void 0 : data.map((coleccion)=>({\n                ...coleccion,\n                // @ts-ignore Supabase types might not be perfect here for related counts\n                numero_flashcards: coleccion.flashcards && Array.isArray(coleccion.flashcards) && coleccion.flashcards.length > 0 ? coleccion.flashcards[0].count : 0\n            }))) || [];\n    } catch (error) {\n        console.error('Error al obtener colecciones de flashcards:', error);\n        return [];\n    }\n}\n/**\n * Obtiene una colección de flashcards por su ID (solo si pertenece al usuario actual)\n */ async function obtenerColeccionFlashcardsPorId(id) {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').select('*').eq('id', id).eq('user_id', user.id).single();\n        if (error) {\n            console.error('Error al obtener colección de flashcards:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error al obtener colección de flashcards:', error);\n        return null;\n    }\n}\n/**\n * Crea una nueva flashcard\n */ async function crearFlashcard(coleccionId, pregunta, respuesta) {\n    var _data_;\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').insert([\n        {\n            coleccion_id: coleccionId,\n            pregunta,\n            respuesta\n        }\n    ]).select();\n    if (error) {\n        console.error('Error al crear flashcard:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n}\n/**\n * Obtiene todas las flashcards de una colección\n */ async function obtenerFlashcardsPorColeccionId(coleccionId) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('*').eq('coleccion_id', coleccionId).order('creado_en', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error al obtener flashcards:', error);\n        return [];\n    }\n    return data || [];\n}\n// Alias para mantener compatibilidad con el código existente\nconst obtenerFlashcardsPorColeccion = obtenerFlashcardsPorColeccionId;\n/**\n * Guarda múltiples flashcards en la base de datos\n */ async function guardarFlashcards(flashcards) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').insert(flashcards).select();\n    if (error) {\n        console.error('Error al guardar flashcards:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : data.map((card)=>card.id)) || null;\n}\n/**\n * Obtiene una flashcard por su ID\n */ async function obtenerFlashcardPorId(id) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('*').eq('id', id).single();\n    if (error) {\n        console.error('Error al obtener flashcard:', error);\n        return null;\n    }\n    return data;\n}\n/**\n * Obtiene el progreso de una flashcard\n */ async function obtenerProgresoFlashcard(flashcardId) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('*').eq('flashcard_id', flashcardId).single();\n    if (error && error.code !== 'PGRST116') {\n        console.error('Error al obtener progreso de flashcard:', error);\n        return null;\n    }\n    return data || null;\n}\n/**\n * Obtiene todas las flashcards con su progreso para una colección\n */ async function obtenerFlashcardsParaEstudiar(coleccionId) {\n    // Obtener todas las flashcards de la colección\n    const flashcards = await obtenerFlashcardsPorColeccionId(coleccionId);\n    // Obtener el progreso de todas las flashcards en una sola consulta\n    const { data: progresos, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('*').in('flashcard_id', flashcards.map((f)=>f.id));\n    if (error) {\n        console.error('Error al obtener progreso de flashcards:', error);\n        return [];\n    }\n    // Fecha actual para comparar\n    const ahora = new Date();\n    const hoy = new Date(ahora.getFullYear(), ahora.getMonth(), ahora.getDate());\n    // Combinar flashcards con su progreso\n    return flashcards.map((flashcard)=>{\n        const progreso = progresos === null || progresos === void 0 ? void 0 : progresos.find((p)=>p.flashcard_id === flashcard.id);\n        if (!progreso) {\n            // Si no hay progreso, es una tarjeta nueva que debe estudiarse\n            return {\n                ...flashcard,\n                debeEstudiar: true\n            };\n        }\n        // Determinar si la flashcard debe estudiarse hoy\n        const proximaRevision = new Date(progreso.proxima_revision);\n        const proximaRevisionSinHora = new Date(proximaRevision.getFullYear(), proximaRevision.getMonth(), proximaRevision.getDate());\n        const debeEstudiar = proximaRevisionSinHora <= hoy;\n        return {\n            ...flashcard,\n            debeEstudiar,\n            progreso: {\n                factor_facilidad: progreso.factor_facilidad,\n                intervalo: progreso.intervalo,\n                repeticiones: progreso.repeticiones,\n                estado: progreso.estado,\n                proxima_revision: progreso.proxima_revision\n            }\n        };\n    });\n}\n/**\n * Registra una respuesta a una flashcard y actualiza su progreso\n */ async function registrarRespuestaFlashcard(flashcardId, dificultad) {\n    // Obtener el progreso actual de la flashcard\n    const progresoActual = await obtenerProgresoFlashcard(flashcardId);\n    // Valores por defecto para una nueva tarjeta\n    let factorFacilidad = 2.5;\n    let intervalo = 1;\n    let repeticiones = 0;\n    let estado = 'nuevo';\n    // Si ya existe un progreso, usar esos valores\n    if (progresoActual) {\n        factorFacilidad = progresoActual.factor_facilidad;\n        intervalo = progresoActual.intervalo;\n        repeticiones = progresoActual.repeticiones;\n        estado = progresoActual.estado;\n    }\n    // Aplicar el algoritmo SM-2 para calcular el nuevo progreso\n    let nuevoFactorFacilidad = factorFacilidad;\n    let nuevoIntervalo = intervalo;\n    let nuevasRepeticiones = repeticiones;\n    let nuevoEstado = estado;\n    // Ajustar el factor de facilidad según la dificultad reportada\n    if (dificultad === 'dificil') {\n        nuevoFactorFacilidad = Math.max(1.3, factorFacilidad - 0.3);\n        nuevasRepeticiones = 0;\n        nuevoIntervalo = 1;\n        nuevoEstado = 'aprendiendo';\n    } else {\n        nuevasRepeticiones++;\n        if (dificultad === 'normal') {\n            nuevoFactorFacilidad = factorFacilidad - 0.15;\n        } else if (dificultad === 'facil') {\n            nuevoFactorFacilidad = factorFacilidad + 0.1;\n        }\n        nuevoFactorFacilidad = Math.max(1.3, Math.min(2.5, nuevoFactorFacilidad));\n        // Calcular el nuevo intervalo\n        if (nuevasRepeticiones === 1) {\n            nuevoIntervalo = 1;\n            nuevoEstado = 'aprendiendo';\n        } else if (nuevasRepeticiones === 2) {\n            nuevoIntervalo = 6;\n            nuevoEstado = 'repasando';\n        } else {\n            nuevoIntervalo = Math.round(intervalo * nuevoFactorFacilidad);\n            nuevoEstado = nuevoIntervalo > 30 ? 'aprendido' : 'repasando';\n        }\n    }\n    // Calcular la próxima fecha de revisión\n    const ahora = new Date();\n    const proximaRevision = new Date(ahora);\n    proximaRevision.setDate(proximaRevision.getDate() + nuevoIntervalo);\n    // Guardar el nuevo progreso\n    const { error: errorProgreso } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').upsert({\n        flashcard_id: flashcardId,\n        factor_facilidad: nuevoFactorFacilidad,\n        intervalo: nuevoIntervalo,\n        repeticiones: nuevasRepeticiones,\n        estado: nuevoEstado,\n        ultima_revision: ahora.toISOString(),\n        proxima_revision: proximaRevision.toISOString()\n    });\n    if (errorProgreso) {\n        console.error('Error al actualizar progreso:', errorProgreso);\n        return false;\n    }\n    // Guardar en el historial de revisiones\n    const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').insert({\n        flashcard_id: flashcardId,\n        dificultad,\n        factor_facilidad: nuevoFactorFacilidad,\n        intervalo: nuevoIntervalo,\n        repeticiones: nuevasRepeticiones,\n        fecha: ahora.toISOString()\n    });\n    if (errorHistorial) {\n        console.error('Error al guardar historial:', errorHistorial);\n    // No retornamos false aquí porque el progreso ya se guardó correctamente\n    }\n    return true;\n}\n// Alias para mantener compatibilidad con el código existente\nconst actualizarProgresoFlashcard = registrarRespuestaFlashcard;\n/**\n * Guarda una revisión en el historial\n */ async function guardarRevisionHistorial(flashcardId, dificultad, factorFacilidad, intervalo, repeticiones) {\n    var _data_;\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').insert([\n        {\n            flashcard_id: flashcardId,\n            dificultad,\n            factor_facilidad: factorFacilidad,\n            intervalo,\n            repeticiones\n        }\n    ]).select();\n    if (error) {\n        console.error('Error al guardar revisión en historial:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n}\n/**\n * Reinicia el progreso de una flashcard\n */ async function reiniciarProgresoFlashcard(flashcardId) {\n    const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').update({\n        factor_facilidad: 2.5,\n        intervalo: 0,\n        repeticiones: 0,\n        estado: 'nuevo',\n        ultima_revision: new Date().toISOString(),\n        proxima_revision: new Date().toISOString()\n    }).eq('flashcard_id', flashcardId);\n    if (error) {\n        console.error('Error al reiniciar progreso de flashcard:', error);\n        return false;\n    }\n    return true;\n}\n/**\n * Actualiza una flashcard existente\n */ async function actualizarFlashcard(flashcardId, pregunta, respuesta) {\n    try {\n        console.log('✏️ Actualizando flashcard:', flashcardId);\n        const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').update({\n            pregunta,\n            respuesta,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', flashcardId);\n        if (error) {\n            console.error('❌ Error al actualizar flashcard:', error);\n            return false;\n        }\n        console.log('✅ Flashcard actualizada exitosamente');\n        return true;\n    } catch (error) {\n        console.error('💥 Error inesperado al actualizar flashcard:', error);\n        return false;\n    }\n}\n/**\n * Elimina una flashcard y todo su progreso asociado\n */ async function eliminarFlashcard(flashcardId) {\n    try {\n        console.log('🗑️ Eliminando flashcard:', flashcardId);\n        // Primero eliminar el progreso asociado\n        const { error: errorProgreso } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').delete().eq('flashcard_id', flashcardId);\n        if (errorProgreso) {\n            console.error('❌ Error al eliminar progreso de flashcard:', errorProgreso);\n            return false;\n        }\n        // Eliminar el historial de revisiones\n        const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').delete().eq('flashcard_id', flashcardId);\n        if (errorHistorial) {\n            console.error('❌ Error al eliminar historial de flashcard:', errorHistorial);\n            return false;\n        }\n        // Finalmente eliminar la flashcard\n        const { error: errorFlashcard, count } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').delete({\n            count: 'exact'\n        }).eq('id', flashcardId);\n        if (errorFlashcard) {\n            console.error('❌ Error al eliminar flashcard:', errorFlashcard);\n            return false;\n        }\n        console.log('✅ Flashcard eliminada exitosamente. Filas afectadas:', count);\n        if (count === 0) {\n            console.warn('⚠️ No se eliminó ninguna flashcard. Posible causa: flashcard no existe');\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('💥 Error inesperado al eliminar flashcard:', error);\n        return false;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/flashcardsService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/supabase/index.ts":
/*!***********************************!*\
  !*** ./src/lib/supabase/index.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activarConversacion: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.activarConversacion),\n/* harmony export */   actualizarConversacion: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.actualizarConversacion),\n/* harmony export */   actualizarFlashcard: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.actualizarFlashcard),\n/* harmony export */   actualizarProgresoFlashcard: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.actualizarProgresoFlashcard),\n/* harmony export */   crearColeccionFlashcards: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.crearColeccionFlashcards),\n/* harmony export */   crearConversacion: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.crearConversacion),\n/* harmony export */   crearFlashcard: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.crearFlashcard),\n/* harmony export */   crearPreguntaTest: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.crearPreguntaTest),\n/* harmony export */   crearTest: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.crearTest),\n/* harmony export */   createClient: () => (/* reexport safe */ _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.createClient),\n/* harmony export */   desactivarTodasLasConversaciones: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.desactivarTodasLasConversaciones),\n/* harmony export */   eliminarDocumento: () => (/* reexport safe */ _documentosService__WEBPACK_IMPORTED_MODULE_1__.eliminarDocumento),\n/* harmony export */   eliminarFlashcard: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.eliminarFlashcard),\n/* harmony export */   guardarDocumento: () => (/* reexport safe */ _documentosService__WEBPACK_IMPORTED_MODULE_1__.guardarDocumento),\n/* harmony export */   guardarFlashcards: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.guardarFlashcards),\n/* harmony export */   guardarMensaje: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.guardarMensaje),\n/* harmony export */   guardarPreguntasTest: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.guardarPreguntasTest),\n/* harmony export */   guardarRevisionHistorial: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.guardarRevisionHistorial),\n/* harmony export */   obtenerColeccionFlashcardsPorId: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerColeccionFlashcardsPorId),\n/* harmony export */   obtenerColeccionesFlashcards: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerColeccionesFlashcards),\n/* harmony export */   obtenerConversacionActiva: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.obtenerConversacionActiva),\n/* harmony export */   obtenerConversacionPorId: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.obtenerConversacionPorId),\n/* harmony export */   obtenerConversaciones: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.obtenerConversaciones),\n/* harmony export */   obtenerDocumentoPorId: () => (/* reexport safe */ _documentosService__WEBPACK_IMPORTED_MODULE_1__.obtenerDocumentoPorId),\n/* harmony export */   obtenerDocumentos: () => (/* reexport safe */ _documentosService__WEBPACK_IMPORTED_MODULE_1__.obtenerDocumentos),\n/* harmony export */   obtenerEstadisticasColeccion: () => (/* reexport safe */ _estadisticasService__WEBPACK_IMPORTED_MODULE_4__.obtenerEstadisticasColeccion),\n/* harmony export */   obtenerEstadisticasDetalladas: () => (/* reexport safe */ _estadisticasService__WEBPACK_IMPORTED_MODULE_4__.obtenerEstadisticasDetalladas),\n/* harmony export */   obtenerEstadisticasEstudio: () => (/* reexport safe */ _estadisticasService__WEBPACK_IMPORTED_MODULE_4__.obtenerEstadisticasEstudio),\n/* harmony export */   obtenerEstadisticasGeneralesTests: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.obtenerEstadisticasGeneralesTests),\n/* harmony export */   obtenerEstadisticasTest: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.obtenerEstadisticasTest),\n/* harmony export */   obtenerFlashcardPorId: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerFlashcardPorId),\n/* harmony export */   obtenerFlashcardsParaEstudiar: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerFlashcardsParaEstudiar),\n/* harmony export */   obtenerFlashcardsPorColeccion: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerFlashcardsPorColeccion),\n/* harmony export */   obtenerFlashcardsPorColeccionId: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerFlashcardsPorColeccionId),\n/* harmony export */   obtenerHistorialRevisiones: () => (/* reexport safe */ _estadisticasService__WEBPACK_IMPORTED_MODULE_4__.obtenerHistorialRevisiones),\n/* harmony export */   obtenerMensajesPorConversacionId: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.obtenerMensajesPorConversacionId),\n/* harmony export */   obtenerPreguntasPorTestId: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.obtenerPreguntasPorTestId),\n/* harmony export */   obtenerPreguntasTestCount: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.obtenerPreguntasTestCount),\n/* harmony export */   obtenerProgresoFlashcard: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerProgresoFlashcard),\n/* harmony export */   obtenerTestPorId: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.obtenerTestPorId),\n/* harmony export */   obtenerTests: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.obtenerTests),\n/* harmony export */   registrarRespuestaFlashcard: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.registrarRespuestaFlashcard),\n/* harmony export */   registrarRespuestaTest: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.registrarRespuestaTest),\n/* harmony export */   reiniciarProgresoFlashcard: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.reiniciarProgresoFlashcard),\n/* harmony export */   supabase: () => (/* reexport safe */ _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _documentosService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./documentosService */ \"(app-pages-browser)/./src/lib/supabase/documentosService.ts\");\n/* harmony import */ var _conversacionesService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./conversacionesService */ \"(app-pages-browser)/./src/lib/supabase/conversacionesService.ts\");\n/* harmony import */ var _flashcardsService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./flashcardsService */ \"(app-pages-browser)/./src/lib/supabase/flashcardsService.ts\");\n/* harmony import */ var _estadisticasService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./estadisticasService */ \"(app-pages-browser)/./src/lib/supabase/estadisticasService.ts\");\n/* harmony import */ var _testsService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./testsService */ \"(app-pages-browser)/./src/lib/supabase/testsService.ts\");\n// Exportar todo desde los archivos individuales\n\n\n\n\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc3VwYWJhc2UvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsZ0RBQWdEO0FBQ2Y7QUFDRztBQUNJO0FBQ0o7QUFDRTtBQUNQIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcc3JjXFxsaWJcXHN1cGFiYXNlXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRhciB0b2RvIGRlc2RlIGxvcyBhcmNoaXZvcyBpbmRpdmlkdWFsZXNcbmV4cG9ydCAqIGZyb20gJy4vc3VwYWJhc2VDbGllbnQnO1xuZXhwb3J0ICogZnJvbSAnLi9kb2N1bWVudG9zU2VydmljZSc7XG5leHBvcnQgKiBmcm9tICcuL2NvbnZlcnNhY2lvbmVzU2VydmljZSc7XG5leHBvcnQgKiBmcm9tICcuL2ZsYXNoY2FyZHNTZXJ2aWNlJztcbmV4cG9ydCAqIGZyb20gJy4vZXN0YWRpc3RpY2FzU2VydmljZSc7XG5leHBvcnQgKiBmcm9tICcuL3Rlc3RzU2VydmljZSc7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/index.ts\n"));

/***/ })

});