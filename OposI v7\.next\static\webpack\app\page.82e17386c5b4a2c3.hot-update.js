"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/DocumentUploader.tsx":
/*!*********************************************!*\
  !*** ./src/components/DocumentUploader.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocumentUploader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n // Importar toast\nfunction DocumentUploader(param) {\n    let { onSuccess } = param;\n    _s();\n    const [titulo, setTitulo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [contenido, setContenido] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [categoria, setCategoria] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [numeroTema, setNumeroTema] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Conservamos 'mensaje' para errores de validación inline o mensajes muy específicos del formulario\n    const [mensaje, setMensaje] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        texto: '',\n        tipo: ''\n    });\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setMensaje({\n            texto: '',\n            tipo: ''\n        }); // Limpiar mensajes inline\n        let loadingToastId = undefined;\n        if (selectedFile) {\n            loadingToastId = react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].loading(\"Subiendo \".concat(selectedFile.name, \"...\"));\n            const formData = new FormData();\n            formData.append('file', selectedFile);\n            // Enviar también los datos del formulario\n            formData.append('titulo', titulo);\n            if (categoria) formData.append('categoria', categoria);\n            if (numeroTema) formData.append('numero_tema', numeroTema);\n            try {\n                const response = await fetch('/api/document/upload', {\n                    method: 'POST',\n                    body: formData\n                });\n                if (response.ok) {\n                    const result = await response.json();\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success('Documento \"'.concat(selectedFile.name, '\" subido y procesado con ID: ').concat(result.documentId, \".\"), {\n                        id: loadingToastId\n                    });\n                    setTitulo('');\n                    setContenido('');\n                    setCategoria('');\n                    setNumeroTema('');\n                    setSelectedFile(null);\n                    if (fileInputRef.current) {\n                        fileInputRef.current.value = '';\n                    }\n                    if (onSuccess) {\n                        onSuccess();\n                    }\n                } else {\n                    const errorResult = await response.json();\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"Error al subir archivo: \".concat(errorResult.error || response.statusText), {\n                        id: loadingToastId\n                    });\n                }\n            } catch (error) {\n                console.error('Error en la subida del archivo:', error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error('Error de conexión o inesperado al subir el archivo.', {\n                    id: loadingToastId\n                });\n            } finally{\n                setIsLoading(false);\n            }\n        } else {\n            // Fallback to manual input\n            if (!titulo.trim() || !contenido.trim()) {\n                setMensaje({\n                    texto: 'El título y el contenido son obligatorios si no se selecciona un archivo.',\n                    tipo: 'error'\n                });\n                setIsLoading(false);\n                return;\n            }\n            loadingToastId = react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].loading('Guardando documento manualmente...');\n            try {\n                const documento = {\n                    titulo,\n                    contenido,\n                    categoria: categoria || undefined,\n                    numero_tema: numeroTema ? parseInt(numeroTema) : undefined\n                };\n                const id = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.guardarDocumento)(documento); // Direct Supabase call\n                if (id) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success('Documento guardado manualmente correctamente.', {\n                        id: loadingToastId\n                    });\n                    setTitulo('');\n                    setContenido('');\n                    setCategoria('');\n                    setNumeroTema('');\n                    if (onSuccess) {\n                        onSuccess();\n                    }\n                } else {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error('Error al guardar el documento manualmente.', {\n                        id: loadingToastId\n                    });\n                }\n            } catch (error) {\n                console.error('Error al guardar documento manualmente:', error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error('Ha ocurrido un error al guardar el documento manualmente.', {\n                    id: loadingToastId\n                });\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    };\n    const handleFileUpload = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        setMensaje({\n            texto: '',\n            tipo: ''\n        }); // Limpiar mensajes inline al cambiar archivo\n        if (!file) {\n            setSelectedFile(null);\n            return;\n        }\n        const MAX_SIZE_MB = 5;\n        if (file.size > MAX_SIZE_MB * 1024 * 1024) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"El archivo es demasiado grande. El tama\\xf1o m\\xe1ximo es \".concat(MAX_SIZE_MB, \"MB.\"));\n            if (fileInputRef.current) {\n                fileInputRef.current.value = '';\n            }\n            setSelectedFile(null);\n            return;\n        }\n        setSelectedFile(file);\n        setTitulo(file.name);\n        if (file.type === 'text/plain') {\n            const reader = new FileReader();\n            reader.onload = (event)=>{\n                var _event_target;\n                if ((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result) {\n                    setContenido(event.target.result);\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success('Archivo TXT leído y listo para vista previa.');\n                } else {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error('Error al leer el archivo TXT.');\n                    setContenido('');\n                }\n            };\n            reader.onerror = ()=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error('Error al leer el archivo TXT.');\n                setContenido('');\n            };\n            reader.readAsText(file);\n        } else if (file.type === 'application/pdf') {\n            setContenido('El contenido se extraerá del PDF al guardar. Puedes editar el título si es necesario.');\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success('Archivo PDF seleccionado. El contenido se procesará en el servidor.');\n        } else {\n            setContenido('Este tipo de archivo no tiene previsualización. El contenido se procesará en el servidor si es compatible.');\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].info(\"Archivo \".concat(file.name, \" seleccionado. El tipo no es previsualizable.\"));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-xl font-bold mb-4\",\n                children: \"Subir nuevo documento\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"titulo\",\n                                className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                children: \"T\\xedtulo:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                id: \"titulo\",\n                                className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                value: titulo,\n                                onChange: (e)=>setTitulo(e.target.value),\n                                placeholder: \"T\\xedtulo del documento (se autocompleta con el nombre del archivo)\",\n                                disabled: isLoading,\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"categoria\",\n                                        className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                        children: \"Categor\\xeda:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"categoria\",\n                                        className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                        value: categoria,\n                                        onChange: (e)=>setCategoria(e.target.value),\n                                        disabled: isLoading,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Seleccionar categor\\xeda\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"tema\",\n                                                children: \"Tema\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"anexo\",\n                                                children: \"Anexo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"resumen\",\n                                                children: \"Resumen\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"numeroTema\",\n                                        className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                        children: \"N\\xfamero de tema:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        id: \"numeroTema\",\n                                        className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                        value: numeroTema,\n                                        onChange: (e)=>setNumeroTema(e.target.value),\n                                        placeholder: \"Opcional\",\n                                        min: \"1\",\n                                        disabled: isLoading\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"contenido\",\n                                className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                children: \"Contenido (manual o previsualizaci\\xf3n de .txt):\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"contenido\",\n                                className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                rows: 10,\n                                value: contenido,\n                                onChange: (e)=>setContenido(e.target.value),\n                                placeholder: \"Escribe o pega el contenido aqu\\xed, o selecciona un archivo .txt para previsualizarlo. Para PDFs, el contenido se extraer\\xe1 autom\\xe1ticamente.\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"archivo\",\n                                className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                children: \"O sube un archivo (.txt o .pdf):\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"file\",\n                                id: \"archivo\",\n                                ref: fileInputRef,\n                                className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                onChange: handleFileUpload,\n                                accept: \".txt,.pdf\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 mt-1\",\n                                children: [\n                                    \"Solo archivos .txt o .pdf. M\\xe1ximo \",\n                                    5,\n                                    \"MB.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    mensaje.texto && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 rounded \".concat(mensaje.tipo === 'error' ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'),\n                        children: mensaje.texto\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                            disabled: isLoading,\n                            children: isLoading ? 'Guardando...' : 'Guardar documento'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DocumentUploader.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentUploader, \"npBYiEd1Kmy+0Kg9pRrwc061Whc=\");\n_c = DocumentUploader;\nvar _c;\n$RefreshReg$(_c, \"DocumentUploader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DocumentUploader.tsx\n"));

/***/ })

});