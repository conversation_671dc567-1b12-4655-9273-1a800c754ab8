import {
  supabase,
  ColeccionFlashcards,
  Flashcard,
  ProgresoFlashcard,
  FlashcardConProgreso,
  DificultadRespuesta,
  RevisionHistorial
} from './supabaseClient';
import { obtenerUsuarioActual } from './authService';

/**
 * Crea una nueva colección de flashcards
 */
export async function crearColeccionFlashcards(titulo: string, descripcion?: string): Promise<string | null> {
  try {
    // Obtener el usuario actual
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return null;
    }

    const { data, error } = await supabase
      .from('colecciones_flashcards')
      .insert([{
        titulo,
        descripcion,
        user_id: user.id
      }])
      .select();

    if (error) {
      console.error('Error al crear colección de flashcards:', error);
      return null;
    }

    return data?.[0]?.id || null;
  } catch (error) {
    console.error('Error al crear colección de flashcards:', error);
    return null;
  }
}

/**
 * Obtiene todas las colecciones de flashcards del usuario actual
 */
export async function obtenerColeccionesFlashcards(): Promise<ColeccionFlashcards[]> {
  try {
    // Obtener el usuario actual
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return [];
    }

    const { data, error } = await supabase
      .from('colecciones_flashcards')
      .select('*, flashcards(count)') // Fetch all columns from colecciones_flashcards and the count of related flashcards
      .eq('user_id', user.id)
      .order('creado_en', { ascending: false });

    if (error) {
      console.error('Error al obtener colecciones de flashcards:', error);
      return [];
    }

    // Supabase returns the count in an array, e.g., flashcards: [{ count: 5 }]
    // We need to transform this to a direct property numero_flashcards
    return data?.map(coleccion => ({
      ...coleccion,
      // @ts-ignore Supabase types might not be perfect here for related counts
      numero_flashcards: coleccion.flashcards && Array.isArray(coleccion.flashcards) && coleccion.flashcards.length > 0
                         // @ts-ignore
                         ? coleccion.flashcards[0].count
                         : 0,
    })) || [];
  } catch (error) {
    console.error('Error al obtener colecciones de flashcards:', error);
    return [];
  }
}

/**
 * Obtiene una colección de flashcards por su ID (solo si pertenece al usuario actual)
 */
export async function obtenerColeccionFlashcardsPorId(id: string): Promise<ColeccionFlashcards | null> {
  try {
    // Obtener el usuario actual
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return null;
    }

    const { data, error } = await supabase
      .from('colecciones_flashcards')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (error) {
      console.error('Error al obtener colección de flashcards:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error al obtener colección de flashcards:', error);
    return null;
  }
}

/**
 * Crea una nueva flashcard
 */
export async function crearFlashcard(coleccionId: string, pregunta: string, respuesta: string): Promise<string | null> {
  const { data, error } = await supabase
    .from('flashcards')
    .insert([{ coleccion_id: coleccionId, pregunta, respuesta }])
    .select();

  if (error) {
    console.error('Error al crear flashcard:', error);
    return null;
  }

  return data?.[0]?.id || null;
}

/**
 * Obtiene todas las flashcards de una colección
 */
export async function obtenerFlashcardsPorColeccionId(coleccionId: string): Promise<Flashcard[]> {
  const { data, error } = await supabase
    .from('flashcards')
    .select('*')
    .eq('coleccion_id', coleccionId)
    .order('creado_en', { ascending: true });

  if (error) {
    console.error('Error al obtener flashcards:', error);
    return [];
  }

  return data || [];
}

// Alias para mantener compatibilidad con el código existente
export const obtenerFlashcardsPorColeccion = obtenerFlashcardsPorColeccionId;

/**
 * Guarda múltiples flashcards en la base de datos
 */
export async function guardarFlashcards(flashcards: Omit<Flashcard, 'id' | 'creado_en' | 'actualizado_en'>[]): Promise<string[] | null> {
  const { data, error } = await supabase
    .from('flashcards')
    .insert(flashcards)
    .select();

  if (error) {
    console.error('Error al guardar flashcards:', error);
    return null;
  }

  return data?.map(card => card.id) || null;
}

/**
 * Obtiene una flashcard por su ID
 */
export async function obtenerFlashcardPorId(id: string): Promise<Flashcard | null> {
  const { data, error } = await supabase
    .from('flashcards')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error al obtener flashcard:', error);
    return null;
  }

  return data;
}

/**
 * Obtiene el progreso de una flashcard
 */
export async function obtenerProgresoFlashcard(flashcardId: string): Promise<ProgresoFlashcard | null> {
  const { data, error } = await supabase
    .from('progreso_flashcards')
    .select('*')
    .eq('flashcard_id', flashcardId)
    .single();

  if (error && error.code !== 'PGRST116') { // PGRST116 es el código de error cuando no se encuentra ningún registro
    console.error('Error al obtener progreso de flashcard:', error);
    return null;
  }

  return data || null;
}

/**
 * Obtiene todas las flashcards con su progreso para una colección
 */
export async function obtenerFlashcardsParaEstudiar(coleccionId: string): Promise<FlashcardConProgreso[]> {
  // Obtener todas las flashcards de la colección
  const flashcards = await obtenerFlashcardsPorColeccionId(coleccionId);

  // Obtener el progreso de todas las flashcards en una sola consulta
  const { data: progresos, error } = await supabase
    .from('progreso_flashcards')
    .select('*')
    .in('flashcard_id', flashcards.map(f => f.id));

  if (error) {
    console.error('Error al obtener progreso de flashcards:', error);
    return [];
  }

  // Fecha actual para comparar
  const ahora = new Date();
  const hoy = new Date(
    ahora.getFullYear(),
    ahora.getMonth(),
    ahora.getDate()
  );

  // Combinar flashcards con su progreso
  return flashcards.map(flashcard => {
    const progreso = progresos?.find(p => p.flashcard_id === flashcard.id);

    if (!progreso) {
      // Si no hay progreso, es una tarjeta nueva que debe estudiarse
      return {
        ...flashcard,
        debeEstudiar: true,
      };
    }

    // Determinar si la flashcard debe estudiarse hoy
    const proximaRevision = new Date(progreso.proxima_revision);
    const proximaRevisionSinHora = new Date(
      proximaRevision.getFullYear(),
      proximaRevision.getMonth(),
      proximaRevision.getDate()
    );
    const debeEstudiar = proximaRevisionSinHora <= hoy;

    return {
      ...flashcard,
      debeEstudiar,
      progreso: {
        factor_facilidad: progreso.factor_facilidad,
        intervalo: progreso.intervalo,
        repeticiones: progreso.repeticiones,
        estado: progreso.estado,
        proxima_revision: progreso.proxima_revision,
      },
    };
  });
}

/**
 * Registra una respuesta a una flashcard y actualiza su progreso
 */
export async function registrarRespuestaFlashcard(
  flashcardId: string,
  dificultad: DificultadRespuesta
): Promise<boolean> {
  // Obtener el progreso actual de la flashcard
  const progresoActual = await obtenerProgresoFlashcard(flashcardId);

  // Valores por defecto para una nueva tarjeta
  let factorFacilidad = 2.5;
  let intervalo = 1;
  let repeticiones = 0;
  let estado: 'nuevo' | 'aprendiendo' | 'repasando' | 'aprendido' = 'nuevo';

  // Si ya existe un progreso, usar esos valores
  if (progresoActual) {
    factorFacilidad = progresoActual.factor_facilidad;
    intervalo = progresoActual.intervalo;
    repeticiones = progresoActual.repeticiones;
    estado = progresoActual.estado as any;
  }

  // Aplicar el algoritmo SM-2 para calcular el nuevo progreso
  let nuevoFactorFacilidad = factorFacilidad;
  let nuevoIntervalo = intervalo;
  let nuevasRepeticiones = repeticiones;
  let nuevoEstado = estado;

  // Ajustar el factor de facilidad según la dificultad reportada
  if (dificultad === 'dificil') {
    nuevoFactorFacilidad = Math.max(1.3, factorFacilidad - 0.3);
    nuevasRepeticiones = 0;
    nuevoIntervalo = 1;
    nuevoEstado = 'aprendiendo';
  } else {
    nuevasRepeticiones++;

    if (dificultad === 'normal') {
      nuevoFactorFacilidad = factorFacilidad - 0.15;
    } else if (dificultad === 'facil') {
      nuevoFactorFacilidad = factorFacilidad + 0.1;
    }

    nuevoFactorFacilidad = Math.max(1.3, Math.min(2.5, nuevoFactorFacilidad));

    // Calcular el nuevo intervalo
    if (nuevasRepeticiones === 1) {
      nuevoIntervalo = 1;
      nuevoEstado = 'aprendiendo';
    } else if (nuevasRepeticiones === 2) {
      nuevoIntervalo = 6;
      nuevoEstado = 'repasando';
    } else {
      nuevoIntervalo = Math.round(intervalo * nuevoFactorFacilidad);
      nuevoEstado = nuevoIntervalo > 30 ? 'aprendido' : 'repasando';
    }
  }

  // Calcular la próxima fecha de revisión
  const ahora = new Date();
  const proximaRevision = new Date(ahora);
  proximaRevision.setDate(proximaRevision.getDate() + nuevoIntervalo);

  // Guardar el nuevo progreso
  const { error: errorProgreso } = await supabase
    .from('progreso_flashcards')
    .upsert({
      flashcard_id: flashcardId,
      factor_facilidad: nuevoFactorFacilidad,
      intervalo: nuevoIntervalo,
      repeticiones: nuevasRepeticiones,
      estado: nuevoEstado,
      ultima_revision: ahora.toISOString(),
      proxima_revision: proximaRevision.toISOString(),
    });

  if (errorProgreso) {
    console.error('Error al actualizar progreso:', errorProgreso);
    return false;
  }

  // Guardar en el historial de revisiones
  const { error: errorHistorial } = await supabase
    .from('historial_revisiones')
    .insert({
      flashcard_id: flashcardId,
      dificultad,
      factor_facilidad: nuevoFactorFacilidad,
      intervalo: nuevoIntervalo,
      repeticiones: nuevasRepeticiones,
      fecha: ahora.toISOString(),
    });

  if (errorHistorial) {
    console.error('Error al guardar historial:', errorHistorial);
    // No retornamos false aquí porque el progreso ya se guardó correctamente
  }

  return true;
}

// Alias para mantener compatibilidad con el código existente
export const actualizarProgresoFlashcard = registrarRespuestaFlashcard;

/**
 * Guarda una revisión en el historial
 */
export async function guardarRevisionHistorial(
  flashcardId: string,
  dificultad: DificultadRespuesta,
  factorFacilidad: number,
  intervalo: number,
  repeticiones: number
): Promise<string | null> {
  const { data, error } = await supabase
    .from('historial_revisiones')
    .insert([{
      flashcard_id: flashcardId,
      dificultad,
      factor_facilidad: factorFacilidad,
      intervalo,
      repeticiones
    }])
    .select();

  if (error) {
    console.error('Error al guardar revisión en historial:', error);
    return null;
  }

  return data?.[0]?.id || null;
}

/**
 * Reinicia el progreso de una flashcard
 */
export async function reiniciarProgresoFlashcard(flashcardId: string): Promise<boolean> {
  const { error } = await supabase
    .from('progreso_flashcards')
    .update({
      factor_facilidad: 2.5,
      intervalo: 0,
      repeticiones: 0,
      estado: 'nuevo',
      ultima_revision: new Date().toISOString(),
      proxima_revision: new Date().toISOString()
    })
    .eq('flashcard_id', flashcardId);

  if (error) {
    console.error('Error al reiniciar progreso de flashcard:', error);
    return false;
  }

  return true;
}

/**
 * Actualiza una flashcard existente
 */
export async function actualizarFlashcard(
  flashcardId: string,
  pregunta: string,
  respuesta: string
): Promise<boolean> {
  try {
    console.log('✏️ Actualizando flashcard:', flashcardId);

    const { error } = await supabase
      .from('flashcards')
      .update({
        pregunta,
        respuesta,
        actualizado_en: new Date().toISOString()
      })
      .eq('id', flashcardId);

    if (error) {
      console.error('❌ Error al actualizar flashcard:', error);
      return false;
    }

    console.log('✅ Flashcard actualizada exitosamente');
    return true;
  } catch (error) {
    console.error('💥 Error inesperado al actualizar flashcard:', error);
    return false;
  }
}

/**
 * Elimina una flashcard y todo su progreso asociado
 */
export async function eliminarFlashcard(flashcardId: string): Promise<boolean> {
  try {
    console.log('🗑️ Eliminando flashcard:', flashcardId);

    // Primero eliminar el progreso asociado
    const { error: errorProgreso } = await supabase
      .from('progreso_flashcards')
      .delete()
      .eq('flashcard_id', flashcardId);

    if (errorProgreso) {
      console.error('❌ Error al eliminar progreso de flashcard:', errorProgreso);
      return false;
    }

    // Eliminar el historial de revisiones
    const { error: errorHistorial } = await supabase
      .from('historial_revisiones')
      .delete()
      .eq('flashcard_id', flashcardId);

    if (errorHistorial) {
      console.error('❌ Error al eliminar historial de flashcard:', errorHistorial);
      return false;
    }

    // Finalmente eliminar la flashcard
    const { error: errorFlashcard, count } = await supabase
      .from('flashcards')
      .delete({ count: 'exact' })
      .eq('id', flashcardId);

    if (errorFlashcard) {
      console.error('❌ Error al eliminar flashcard:', errorFlashcard);
      return false;
    }

    console.log('✅ Flashcard eliminada exitosamente. Filas afectadas:', count);

    if (count === 0) {
      console.warn('⚠️ No se eliminó ninguna flashcard. Posible causa: flashcard no existe');
      return false;
    }

    return true;
  } catch (error) {
    console.error('💥 Error inesperado al eliminar flashcard:', error);
    return false;
  }
}
