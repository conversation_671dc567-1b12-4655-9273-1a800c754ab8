/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/document/upload/route";
exports.ids = ["app/api/document/upload/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdocument%2Fupload%2Froute&page=%2Fapi%2Fdocument%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdocument%2Fupload%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdocument%2Fupload%2Froute&page=%2Fapi%2Fdocument%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdocument%2Fupload%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_OposI_v7_src_app_api_document_upload_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/document/upload/route.ts */ \"(rsc)/./src/app/api/document/upload/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/document/upload/route\",\n        pathname: \"/api/document/upload\",\n        filename: \"route\",\n        bundlePath: \"app/api/document/upload/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\api\\\\document\\\\upload\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_naata_Documents_augment_projects_OposI_OposI_v7_src_app_api_document_upload_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdocument%2Fupload%2Froute&page=%2Fapi%2Fdocument%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdocument%2Fupload%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/document/upload/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/document/upload/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function POST(request) {\n    try {\n        const contentType = request.headers.get('content-type');\n        if (!contentType || !contentType.includes('multipart/form-data')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"El cuerpo de la petición debe ser 'multipart/form-data'.\"\n            }, {\n                status: 415\n            } // Unsupported Media Type\n            );\n        }\n        const formData = await request.formData();\n        const file = formData.get('file');\n        // Obtener datos adicionales del formulario\n        const titulo = formData.get('titulo') || '';\n        const categoria = formData.get('categoria') || undefined;\n        const numeroTemaStr = formData.get('numero_tema');\n        const numeroTema = numeroTemaStr ? parseInt(numeroTemaStr) : undefined;\n        if (!file) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No se proporcionó ningún archivo.'\n            }, {\n                status: 400\n            });\n        }\n        let extractedText;\n        let originalFileType;\n        const fileBuffer = await file.arrayBuffer();\n        if (file.type === 'application/pdf') {\n            try {\n                // Import pdf-lib\n                const { PDFDocument } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/pdf-lib\"), __webpack_require__.e(\"vendor-chunks/@pdf-lib\"), __webpack_require__.e(\"vendor-chunks/pako\")]).then(__webpack_require__.bind(__webpack_require__, /*! pdf-lib */ \"(rsc)/./node_modules/pdf-lib/es/index.js\"));\n                // Load the PDF with pdf-lib\n                const pdfDoc = await PDFDocument.load(fileBuffer);\n                const numPages = pdfDoc.getPageCount();\n                // Define configurable crop percentages for top and bottom\n                const CROP_MARGIN_TOP_PERCENT = 0.08; // Example: 8% from the top\n                const CROP_MARGIN_BOTTOM_PERCENT = 0.08; // Example: 8% from the bottom\n                for(let i = 0; i < numPages; i++){\n                    const page = pdfDoc.getPage(i);\n                    const { width: originalWidth, height: originalHeight } = page.getSize();\n                    const cropAmountBottom = originalHeight * CROP_MARGIN_BOTTOM_PERCENT;\n                    const cropAmountTop = originalHeight * CROP_MARGIN_TOP_PERCENT;\n                    let newY = cropAmountBottom;\n                    let newHeight = originalHeight - cropAmountBottom - cropAmountTop;\n                    // Ensure newHeight is not negative\n                    if (newHeight < 0) {\n                        console.warn(`Page ${i + 1}: Calculated newHeight (${newHeight}) is negative. Margins (${CROP_MARGIN_TOP_PERCENT * 100}%, ${CROP_MARGIN_BOTTOM_PERCENT * 100}%) might be too large for page height ${originalHeight}. Resetting to prevent error, page will not be cropped effectively.`);\n                        newY = 0; // Reset y to prevent invalid box\n                        newHeight = originalHeight; // Reset height\n                    }\n                    // Set the crop box [x, y, width, height]\n                    // Origin (0,0) is bottom-left\n                    page.setCropBox(0, newY, originalWidth, newHeight // height: the new height of the page content area\n                    );\n                }\n                // Save the modified PDF to a new buffer\n                const modifiedPdfBuffer = await pdfDoc.save();\n                console.log('PDF modificado, tamaño del buffer:', modifiedPdfBuffer.length);\n                // Convert to Buffer and pass to pdf-parse\n                const pdfBuffer = Buffer.from(modifiedPdfBuffer);\n                console.log('Buffer creado para pdf-parse, tamaño:', pdfBuffer.length);\n                // Import pdf-parse library directly to avoid debug mode\n                const pdfParseLib = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/pdf-parse\"), __webpack_require__.e(\"_rsc_node_modules_pdf-parse_lib_pdf_js_sync_recursive_build_pdf_js_\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! pdf-parse/lib/pdf-parse.js */ \"(rsc)/./node_modules/pdf-parse/lib/pdf-parse.js\", 23));\n                const pdfParse = pdfParseLib.default || pdfParseLib;\n                // Pass the buffer to pdf-parse\n                const pdf = await pdfParse(pdfBuffer);\n                extractedText = pdf.text;\n                originalFileType = 'pdf';\n                console.log('Texto extraído, longitud:', extractedText.length);\n            } catch (parseError) {\n                console.error('Error al procesar PDF:', parseError);\n                const errorMessage = parseError instanceof Error ? parseError.message : 'Error desconocido al procesar PDF';\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Error al procesar el archivo PDF.',\n                    details: errorMessage\n                }, {\n                    status: 422\n                } // Unprocessable Entity\n                );\n            }\n        } else if (file.type === 'text/plain') {\n            extractedText = Buffer.from(fileBuffer).toString('utf-8');\n            originalFileType = 'txt';\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Tipo de archivo no soportado: ${file.type}. Solo se permiten .txt y .pdf.`\n            }, {\n                status: 415\n            } // Unsupported Media Type\n            );\n        }\n        if (!extractedText || extractedText.trim() === '') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'El contenido extraído del archivo está vacío.'\n            }, {\n                status: 422\n            } // Unprocessable Entity\n            );\n        }\n        // Importar guardarDocumentoServer para usar en el servidor\n        const { guardarDocumentoServer } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/next\"), __webpack_require__.e(\"vendor-chunks/@supabase\"), __webpack_require__.e(\"vendor-chunks/tr46\"), __webpack_require__.e(\"vendor-chunks/whatwg-url\"), __webpack_require__.e(\"vendor-chunks/cookie\"), __webpack_require__.e(\"vendor-chunks/webidl-conversions\"), __webpack_require__.e(\"_rsc_src_lib_supabase_documentosService_server_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/documentosService.server */ \"(rsc)/./src/lib/supabase/documentosService.server.ts\"));\n        const documentId = await guardarDocumentoServer({\n            titulo: titulo || file.name,\n            contenido: extractedText,\n            categoria: categoria,\n            numero_tema: numeroTema,\n            tipo_original: originalFileType\n        });\n        if (documentId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: 'Documento procesado y guardado con éxito.',\n                documentId\n            }, {\n                status: 201\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Error al guardar el documento en la base de datos.'\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error('Error en el endpoint de subida:', error);\n        const errorMessage = error instanceof Error ? error.message : 'Error desconocido';\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error interno del servidor.',\n            details: errorMessage\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/document/upload/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdocument%2Fupload%2Froute&page=%2Fapi%2Fdocument%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdocument%2Fupload%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();