"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/flashcards/FlashcardCollectionList.tsx":
/*!***************************************************************!*\
  !*** ./src/components/flashcards/FlashcardCollectionList.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiInbox_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiInbox!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n\n\n\nconst FlashcardCollectionList = (param)=>{\n    let { colecciones, coleccionSeleccionada, onSeleccionarColeccion, onEliminarColeccion, isLoading, deletingId } = param;\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-40\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (colecciones.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center p-8 border-2 border-dashed border-gray-300 rounded-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiInbox_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiInbox, {\n                    className: \"mx-auto text-6xl text-gray-400 mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500 text-lg\",\n                    children: \"No hay colecciones de flashcards disponibles.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-400 mt-1\",\n                    children: \"Crea una nueva colecci\\xf3n para empezar a estudiar.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6\",\n        children: colecciones.map((coleccion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border rounded-lg p-4 cursor-pointer transition-all flex flex-col justify-between \".concat((coleccionSeleccionada === null || coleccionSeleccionada === void 0 ? void 0 : coleccionSeleccionada.id) === coleccion.id ? 'border-orange-500 bg-orange-50 shadow-lg' : 'border-gray-200 hover:border-orange-300 hover:bg-orange-50/50 hover:shadow-md'),\n                onClick: ()=>onSeleccionarColeccion(coleccion),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-lg mb-1\",\n                                children: coleccion.titulo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, undefined),\n                            coleccion.descripcion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-2 break-words\",\n                                children: coleccion.descripcion\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 mt-2\",\n                                children: [\n                                    \"Flashcards: \",\n                                    typeof coleccion.numero_flashcards === 'number' ? coleccion.numero_flashcards : 'N/A'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-400\",\n                                children: [\n                                    \"Creada: \",\n                                    new Date(coleccion.creado_en).toLocaleDateString()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, coleccion.id, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, undefined);\n};\n_c = FlashcardCollectionList;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlashcardCollectionList);\nvar _c;\n$RefreshReg$(_c, \"FlashcardCollectionList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/flashcards/FlashcardCollectionList.tsx\n"));

/***/ })

});