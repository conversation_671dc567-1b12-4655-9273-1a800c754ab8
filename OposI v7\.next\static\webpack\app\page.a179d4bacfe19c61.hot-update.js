"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiCheck,FiCheckSquare,FiChevronRight,FiFileText,FiLayers,FiList,FiLogOut,FiMessageSquare,FiUpload!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _components_DocumentSelector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/DocumentSelector */ \"(app-pages-browser)/./src/components/DocumentSelector.tsx\");\n/* harmony import */ var _components_QuestionForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/QuestionForm */ \"(app-pages-browser)/./src/components/QuestionForm.tsx\");\n/* harmony import */ var _components_DocumentUploader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/DocumentUploader */ \"(app-pages-browser)/./src/components/DocumentUploader.tsx\");\n/* harmony import */ var _components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/MindMapGenerator */ \"(app-pages-browser)/./src/components/MindMapGenerator.tsx\");\n/* harmony import */ var _components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/FlashcardGenerator */ \"(app-pages-browser)/./src/components/FlashcardGenerator.tsx\");\n/* harmony import */ var _components_flashcards_FlashcardViewer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/flashcards/FlashcardViewer */ \"(app-pages-browser)/./src/components/flashcards/FlashcardViewer.tsx\");\n/* harmony import */ var _components_TestGenerator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../components/TestGenerator */ \"(app-pages-browser)/./src/components/TestGenerator.tsx\");\n/* harmony import */ var _components_TestViewer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../components/TestViewer */ \"(app-pages-browser)/./src/components/TestViewer.tsx\");\n/* harmony import */ var _components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../components/MobileDebugInfo */ \"(app-pages-browser)/./src/components/MobileDebugInfo.tsx\");\n/* harmony import */ var _components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../components/DiagnosticPanel */ \"(app-pages-browser)/./src/components/DiagnosticPanel.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\", _this = undefined, _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar TabButton = function TabButton(_ref) {\n    var active = _ref.active, onClick = _ref.onClick, icon = _ref.icon, label = _ref.label, color = _ref.color;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        className: \"flex items-center px-4 py-3 rounded-lg transition-all duration-200 font-medium text-sm \".concat(active ? \"text-white \".concat(color, \" shadow-md\") : 'text-gray-600 hover:bg-gray-100 hover:text-gray-800'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 5\n            }, _this),\n            label,\n            active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiChevronRight, {\n                className: \"ml-2\"\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 16\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 3\n    }, _this);\n};\n_c1 = TabButton;\n_c = TabButton;\nfunction Home() {\n    _s();\n    _s1();\n    var _user$email, _this2 = this;\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]), documentosSeleccionados = _useState[0], setDocumentosSeleccionados = _useState[1];\n    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), mostrarUploader = _useState2[0], setMostrarUploader = _useState2[1];\n    var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('preguntas'), activeTab = _useState3[0], setActiveTab = _useState3[1];\n    var _useState4 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), showUploadSuccess = _useState4[0], setShowUploadSuccess = _useState4[1];\n    var _useAuth = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_14__.useAuth)(), cerrarSesion = _useAuth.cerrarSesion, user = _useAuth.user, isLoading = _useAuth.isLoading;\n    var router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // No necesitamos verificar autenticación aquí, el middleware ya lo hace\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Home.useEffect\": function() {\n            console.log('[HomePage] Auth state - isLoading:', isLoading, 'User:', !!user);\n        }\n    }[\"Home.useEffect\"], [\n        user,\n        isLoading\n    ]);\n    // Si está cargando o no hay usuario, mostrar pantalla de carga\n    if (isLoading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 7\n        }, this);\n    }\n    var handleUploadSuccess = function handleUploadSuccess() {\n        setShowUploadSuccess(true);\n        setTimeout(function() {\n            return setShowUploadSuccess(false);\n        }, 3000);\n    };\n    var handleLogout = /*#__PURE__*/ function() {\n        var _ref2 = (0,C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee() {\n            return C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee$(_context) {\n                while(1)switch(_context.prev = _context.next){\n                    case 0:\n                        _context.next = 2;\n                        return cerrarSesion();\n                    case 2:\n                    case \"end\":\n                        return _context.stop();\n                }\n            }, _callee);\n        }));\n        return function handleLogout() {\n            return _ref2.apply(this, arguments);\n        };\n    }();\n    var tabs = [\n        {\n            id: 'preguntas',\n            label: 'Preguntas y Respuestas',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiMessageSquare, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 63\n            }, this),\n            color: 'bg-blue-600'\n        },\n        {\n            id: 'mapas',\n            label: 'Mapas Mentales',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiLayers, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 51\n            }, this),\n            color: 'bg-purple-600'\n        },\n        {\n            id: 'flashcards',\n            label: 'Generar Flashcards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiFileText, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 60\n            }, this),\n            color: 'bg-orange-500'\n        },\n        {\n            id: 'tests',\n            label: 'Generar Tests',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiList, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 50\n            }, this),\n            color: 'bg-indigo-600'\n        },\n        {\n            id: 'misFlashcards',\n            label: 'Mis Flashcards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiBook, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 59\n            }, this),\n            color: 'bg-emerald-600'\n        },\n        {\n            id: 'misTests',\n            label: 'Mis Tests',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiCheckSquare, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 49\n            }, this),\n            color: 'bg-pink-600'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                        children: \"OposiAI\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Tu asistente inteligente para oposiciones\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Hola, \",\n                                            (_user$email = user.email) === null || _user$email === void 0 ? void 0 : _user$email.split('@')[0]\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 102,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"button\", {\n                                        onClick: function onClick() {\n                                            return setMostrarUploader(!mostrarUploader);\n                                        },\n                                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiUpload, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this),\n                                            mostrarUploader ? 'Ocultar formulario' : 'Nuevo documento'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiLogOut, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cerrar sesi\\xF3n\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    mostrarUploader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                        className: \"mb-8 transition-all duration-300 ease-in-out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            onSuccess: handleUploadSuccess\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this),\n                    showUploadSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiCheck, {\n                                className: \"text-green-500 mr-2\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this),\n                            \"Documento subido exitosamente\"\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm p-4 sticky top-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"h2\", {\n                                            className: \"text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4 px-2\",\n                                            children: \"Men\\xFA de Estudio\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"nav\", {\n                                            className: \"space-y-1\",\n                                            children: tabs.map(function(tab) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(TabButton, {\n                                                    active: activeTab === tab.id,\n                                                    onClick: function onClick() {\n                                                        return setActiveTab(tab.id);\n                                                    },\n                                                    icon: tab.icon,\n                                                    label: tab.label,\n                                                    color: tab.color\n                                                }, tab.id, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, _this2);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                                            className: \"mt-8 pt-6 border-t border-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-2\",\n                                                    children: \"Documentos Seleccionados\"\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 161,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    onSelectionChange: setDocumentosSeleccionados\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            activeTab === 'preguntas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_components_QuestionForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === 'mapas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === 'flashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 182,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === 'tests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_components_TestGenerator__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 186,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === 'misTests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_components_TestViewer__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 189,\n                                                columnNumber: 46\n                                            }, this),\n                                            activeTab === 'misFlashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_components_flashcards_FlashcardViewer__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 191,\n                                                columnNumber: 51\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200 mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500 text-sm\",\n                                    children: [\n                                        \"\\xA9 \",\n                                        new Date().getFullYear(),\n                                        \" OposiAI - Asistente para Oposiciones\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                                className: \"mt-4 md:mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"T\\xE9rminos\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Privacidad\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Contacto\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"clDTlQWFFTIVZfcpGjL/YJjs5II=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_14__.useAuth,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_14__.useAuth,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_14__.useAuth,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_14__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c3 = Home;\n_s1(Home, \"LF6hBl5V53q5abOaRwPSUcde4Ro=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_14__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c2 = Home;\nvar _c, _c2;\n$RefreshReg$(_c, \"TabButton\");\n$RefreshReg$(_c2, \"Home\");\nvar _c1, _c3;\n$RefreshReg$(_c1, \"TabButton\");\n$RefreshReg$(_c3, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});