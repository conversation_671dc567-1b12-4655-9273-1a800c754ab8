"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/flashcards/FlashcardViewer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/flashcards/FlashcardViewer.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./FlashcardCollectionList */ \"(app-pages-browser)/./src/components/flashcards/FlashcardCollectionList.tsx\");\n/* harmony import */ var _FlashcardStatistics__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FlashcardStatistics */ \"(app-pages-browser)/./src/components/flashcards/FlashcardStatistics.tsx\");\n/* harmony import */ var _FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./FlashcardStudyMode */ \"(app-pages-browser)/./src/components/flashcards/FlashcardStudyMode.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst FlashcardViewer = ()=>{\n    _s();\n    // Estado para las colecciones\n    const [colecciones, setColecciones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coleccionSeleccionada, setColeccionSeleccionada] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para las flashcards\n    const [flashcards, setFlashcards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [mostrarRespuesta, setMostrarRespuesta] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [respondiendo, setRespondiendo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estado para el modo de estudio\n    const [modoEstudio, setModoEstudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estado para estadísticas\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para carga y errores\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Estado para edición y eliminación\n    const [flashcardEditando, setFlashcardEditando] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDeleteConfirm, setShowDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Cargar colecciones al montar el componente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashcardViewer.useEffect\": ()=>{\n            const cargarColecciones = {\n                \"FlashcardViewer.useEffect.cargarColecciones\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n                        setColecciones(data);\n                    } catch (error) {\n                        console.error('Error al cargar colecciones:', error);\n                        setError('No se pudieron cargar las colecciones de flashcards');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"FlashcardViewer.useEffect.cargarColecciones\"];\n            cargarColecciones();\n        }\n    }[\"FlashcardViewer.useEffect\"], []);\n    // Manejar la selección de una colección\n    const handleSeleccionarColeccion = async (coleccion)=>{\n        setIsLoading(true);\n        setError('');\n        setColeccionSeleccionada(coleccion);\n        setActiveIndex(0);\n        setMostrarRespuesta(false);\n        setRespondiendo(false);\n        setModoEstudio(false);\n        try {\n            // Cargar flashcards con su progreso\n            const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccion.id);\n            // Ordenar las flashcards: primero las que deben estudiarse hoy, luego el resto\n            const ordenadas = [\n                ...data\n            ].sort((a, b)=>{\n                if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                return 0;\n            });\n            setFlashcards(ordenadas);\n            // Cargar estadísticas\n            const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccion.id);\n            setEstadisticas(stats);\n        } catch (error) {\n            console.error('Error al cargar flashcards:', error);\n            setError('No se pudieron cargar las flashcards de esta colección');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Iniciar el modo de estudio\n    const iniciarModoEstudio = async ()=>{\n        setIsLoading(true);\n        try {\n            if (coleccionSeleccionada) {\n                // Recargar las flashcards para asegurarnos de tener los datos más recientes\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                // Filtrar solo las flashcards que deben estudiarse hoy\n                const flashcardsParaEstudiar = data.filter((flashcard)=>flashcard.debeEstudiar);\n                // Actualizar estadísticas\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n                // Verificar si el número de flashcards para estudiar coincide con las estadísticas\n                if (flashcardsParaEstudiar.length !== stats.paraHoy) {\n                    console.warn(\"Discrepancia en el conteo: \".concat(flashcardsParaEstudiar.length, \" flashcards filtradas vs \").concat(stats.paraHoy, \" en estad\\xedsticas\"));\n                }\n                // Si no hay flashcards para hoy, mostrar un mensaje\n                if (flashcardsParaEstudiar.length === 0) {\n                    if (data.length === 0) {\n                        alert('No hay flashcards en esta colección.');\n                    } else {\n                        alert('No hay flashcards programadas para estudiar hoy. Vuelve mañana o ajusta el progreso de las tarjetas.');\n                    }\n                    return; // Salir sin iniciar el modo estudio\n                }\n                // Usar solo las flashcards programadas para hoy\n                setFlashcards(flashcardsParaEstudiar);\n                // Iniciar el modo de estudio\n                setModoEstudio(true);\n                setActiveIndex(0);\n                setMostrarRespuesta(false);\n                setRespondiendo(false);\n            }\n        } catch (error) {\n            console.error('Error al iniciar modo de estudio:', error);\n            setError('No se pudo iniciar el modo de estudio');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Manejar la navegación entre flashcards\n    const handleNavigate = (direction)=>{\n        if (direction === 'prev' && activeIndex > 0) {\n            setActiveIndex(activeIndex - 1);\n            setMostrarRespuesta(false);\n        } else if (direction === 'next' && activeIndex < flashcards.length - 1) {\n            setActiveIndex(activeIndex + 1);\n            setMostrarRespuesta(false);\n        }\n    };\n    // Manejar la respuesta a una flashcard\n    const handleRespuesta = async (dificultad)=>{\n        if (!coleccionSeleccionada || flashcards.length === 0) return;\n        const flashcardId = flashcards[activeIndex].id;\n        setRespondiendo(true);\n        try {\n            // Registrar la respuesta\n            await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.registrarRespuestaFlashcard)(flashcardId, dificultad);\n            // Recargar las flashcards y estadísticas si estamos en la última tarjeta\n            if (activeIndex >= flashcards.length - 1 && coleccionSeleccionada) {\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                const flashcardsParaEstudiar = data.filter((flashcard)=>flashcard.debeEstudiar);\n                // Actualizar estadísticas\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n                if (flashcardsParaEstudiar.length > 0) {\n                    setFlashcards(flashcardsParaEstudiar);\n                    setActiveIndex(0);\n                } else {\n                    // Si no hay más flashcards para hoy, mostrar mensaje y salir del modo de estudio\n                    alert('¡Has completado todas las flashcards para hoy! Vuelve mañana para continuar estudiando.');\n                    setModoEstudio(false);\n                    // Ordenar las flashcards: primero las que deben estudiarse (aunque ya no haya ninguna), luego el resto\n                    const ordenadas = [\n                        ...data\n                    ].sort((a, b)=>{\n                        if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                        if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                        return 0;\n                    });\n                    setFlashcards(ordenadas);\n                }\n            } else {\n                // Avanzar a la siguiente flashcard\n                handleNavigate('next');\n            }\n        } catch (error) {\n            console.error('Error al registrar respuesta:', error);\n            setError('No se pudo registrar la respuesta');\n        } finally{\n            setRespondiendo(false);\n            setMostrarRespuesta(false);\n        }\n    };\n    // Salir del modo de estudio\n    const handleSalirModoEstudio = ()=>{\n        setModoEstudio(false);\n    };\n    // Manejar la edición de una flashcard\n    const handleEditarFlashcard = (flashcard)=>{\n        setFlashcardEditando(flashcard);\n        setShowEditModal(true);\n    };\n    // Manejar el guardado de una flashcard editada\n    const handleGuardarFlashcard = (flashcardActualizada)=>{\n        // Actualizar la flashcard en la lista local\n        setFlashcards((prev)=>prev.map((fc)=>fc.id === flashcardActualizada.id ? flashcardActualizada : fc));\n    };\n    // Manejar la eliminación de una flashcard\n    const handleEliminarFlashcard = async (flashcardId)=>{\n        setDeletingId(flashcardId);\n        let loadingToastId;\n        try {\n            loadingToastId = react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].loading('Eliminando flashcard...');\n            const success = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.eliminarFlashcard)(flashcardId);\n            if (success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].success('Flashcard eliminada exitosamente', {\n                    id: loadingToastId\n                });\n                // Actualizar la lista local\n                setFlashcards((prev)=>prev.filter((fc)=>fc.id !== flashcardId));\n                // Recargar estadísticas\n                if (coleccionSeleccionada) {\n                    const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                    setEstadisticas(stats);\n                }\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('Error al eliminar la flashcard', {\n                    id: loadingToastId\n                });\n            }\n        } catch (error) {\n            console.error('Error al eliminar flashcard:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('Error al eliminar la flashcard', {\n                id: loadingToastId\n            });\n        } finally{\n            setDeletingId(null);\n            setShowDeleteConfirm(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 264,\n                columnNumber: 9\n            }, undefined),\n            modoEstudio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                flashcards: flashcards,\n                activeIndex: activeIndex,\n                respondiendo: respondiendo,\n                onRespuesta: handleRespuesta,\n                onNavigate: handleNavigate,\n                onVolver: handleSalirModoEstudio\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 270,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"Mis Flashcards\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        colecciones: colecciones,\n                        coleccionSeleccionada: coleccionSeleccionada,\n                        onSeleccionarColeccion: handleSeleccionarColeccion,\n                        isLoading: isLoading\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 11\n                    }, undefined),\n                    coleccionSeleccionada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: coleccionSeleccionada.titulo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardStatistics__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                estadisticas: estadisticas\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: iniciarModoEstudio,\n                                            className: \"bg-orange-500 hover:bg-orange-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                                            children: \"Estudiar (\".concat(estadisticas ? estadisticas.paraHoy : 0, \" para hoy)\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{},\n                                            className: \"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                                            children: \"Ver estad\\xedsticas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 15\n                            }, undefined),\n                            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center items-center h-40\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 17\n                            }, undefined) : flashcards.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"No hay flashcards en esta colecci\\xf3n.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: flashcards.map((flashcard, index)=>{\n                                    var _flashcard_progreso, _flashcard_progreso1;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4 \".concat(flashcard.debeEstudiar ? 'border-orange-300 bg-orange-50' : 'border-gray-200'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            \"Tarjeta \",\n                                                            index + 1\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    ((_flashcard_progreso = flashcard.progreso) === null || _flashcard_progreso === void 0 ? void 0 : _flashcard_progreso.estado) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs \".concat(flashcard.progreso.estado === 'nuevo' ? 'bg-blue-100 text-blue-800' : flashcard.progreso.estado === 'aprendiendo' ? 'bg-yellow-100 text-yellow-800' : flashcard.progreso.estado === 'repasando' ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'),\n                                                        children: flashcard.progreso.estado\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    !((_flashcard_progreso1 = flashcard.progreso) === null || _flashcard_progreso1 === void 0 ? void 0 : _flashcard_progreso1.estado) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs\",\n                                                        children: \"nuevo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-2\",\n                                                children: flashcard.pregunta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-700 line-clamp-2\",\n                                                children: flashcard.respuesta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, flashcard.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 21\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 279,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n        lineNumber: 262,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlashcardViewer, \"Xzhcu/FYdriaw0rdUfnEbNW9n1c=\");\n_c = FlashcardViewer;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlashcardViewer);\nvar _c;\n$RefreshReg$(_c, \"FlashcardViewer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/flashcards/FlashcardViewer.tsx\n"));

/***/ })

});