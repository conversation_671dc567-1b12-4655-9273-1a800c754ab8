"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activarConversacion: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.activarConversacion),\n/* harmony export */   actualizarConversacion: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.actualizarConversacion),\n/* harmony export */   actualizarProgresoFlashcard: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.actualizarProgresoFlashcard),\n/* harmony export */   crearColeccionFlashcards: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.crearColeccionFlashcards),\n/* harmony export */   crearConversacion: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.crearConversacion),\n/* harmony export */   crearFlashcard: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.crearFlashcard),\n/* harmony export */   crearPreguntaTest: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.crearPreguntaTest),\n/* harmony export */   crearTest: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.crearTest),\n/* harmony export */   createClient: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.createClient),\n/* harmony export */   desactivarTodasLasConversaciones: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.desactivarTodasLasConversaciones),\n/* harmony export */   eliminarDocumento: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.eliminarDocumento),\n/* harmony export */   guardarDocumento: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.guardarDocumento),\n/* harmony export */   guardarFlashcards: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.guardarFlashcards),\n/* harmony export */   guardarMensaje: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.guardarMensaje),\n/* harmony export */   guardarPreguntasTest: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.guardarPreguntasTest),\n/* harmony export */   guardarRevisionHistorial: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.guardarRevisionHistorial),\n/* harmony export */   obtenerColeccionFlashcardsPorId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerColeccionFlashcardsPorId),\n/* harmony export */   obtenerColeccionesFlashcards: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerColeccionesFlashcards),\n/* harmony export */   obtenerConversacionActiva: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerConversacionActiva),\n/* harmony export */   obtenerConversacionPorId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerConversacionPorId),\n/* harmony export */   obtenerConversaciones: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerConversaciones),\n/* harmony export */   obtenerDocumentoPorId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerDocumentoPorId),\n/* harmony export */   obtenerDocumentos: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerDocumentos),\n/* harmony export */   obtenerEstadisticasColeccion: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerEstadisticasColeccion),\n/* harmony export */   obtenerEstadisticasDetalladas: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerEstadisticasDetalladas),\n/* harmony export */   obtenerEstadisticasEstudio: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerEstadisticasEstudio),\n/* harmony export */   obtenerEstadisticasGeneralesTests: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerEstadisticasGeneralesTests),\n/* harmony export */   obtenerEstadisticasTest: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerEstadisticasTest),\n/* harmony export */   obtenerFlashcardPorId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerFlashcardPorId),\n/* harmony export */   obtenerFlashcardsParaEstudiar: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerFlashcardsParaEstudiar),\n/* harmony export */   obtenerFlashcardsPorColeccion: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerFlashcardsPorColeccion),\n/* harmony export */   obtenerFlashcardsPorColeccionId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerFlashcardsPorColeccionId),\n/* harmony export */   obtenerHistorialRevisiones: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerHistorialRevisiones),\n/* harmony export */   obtenerMensajesPorConversacionId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerMensajesPorConversacionId),\n/* harmony export */   obtenerPreguntasPorTestId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerPreguntasPorTestId),\n/* harmony export */   obtenerPreguntasTestCount: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerPreguntasTestCount),\n/* harmony export */   obtenerProgresoFlashcard: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerProgresoFlashcard),\n/* harmony export */   obtenerTestPorId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerTestPorId),\n/* harmony export */   obtenerTests: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerTests),\n/* harmony export */   registrarRespuestaFlashcard: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.registrarRespuestaFlashcard),\n/* harmony export */   registrarRespuestaTest: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.registrarRespuestaTest),\n/* harmony export */   reiniciarProgresoFlashcard: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.reiniciarProgresoFlashcard),\n/* harmony export */   supabase: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase/index */ \"(app-pages-browser)/./src/lib/supabase/index.ts\");\n// Este archivo es un punto de entrada para mantener la compatibilidad con el código existente\n// Redirige todas las exportaciones a la nueva estructura modular\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc3VwYWJhc2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSw4RkFBOEY7QUFDOUYsaUVBQWlFO0FBRWhDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcc3JjXFxsaWJcXHN1cGFiYXNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEVzdGUgYXJjaGl2byBlcyB1biBwdW50byBkZSBlbnRyYWRhIHBhcmEgbWFudGVuZXIgbGEgY29tcGF0aWJpbGlkYWQgY29uIGVsIGPDs2RpZ28gZXhpc3RlbnRlXG4vLyBSZWRpcmlnZSB0b2RhcyBsYXMgZXhwb3J0YWNpb25lcyBhIGxhIG51ZXZhIGVzdHJ1Y3R1cmEgbW9kdWxhclxuXG5leHBvcnQgKiBmcm9tIFwiLi9zdXBhYmFzZS9pbmRleFwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/supabase/documentosService.ts":
/*!***********************************************!*\
  !*** ./src/lib/supabase/documentosService.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   eliminarDocumento: () => (/* binding */ eliminarDocumento),\n/* harmony export */   guardarDocumento: () => (/* binding */ guardarDocumento),\n/* harmony export */   obtenerDocumentoPorId: () => (/* binding */ obtenerDocumentoPorId),\n/* harmony export */   obtenerDocumentos: () => (/* binding */ obtenerDocumentos)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./authService */ \"(app-pages-browser)/./src/lib/supabase/authService.ts\");\n\n\n/**\n * Obtiene todos los documentos del usuario actual ordenados por número de tema\n */ async function obtenerDocumentos() {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return [];\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('documentos').select('*').eq('user_id', user.id).order('numero_tema', {\n            ascending: true\n        });\n        if (error) {\n            console.error('Error al obtener documentos:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error al obtener documentos:', error);\n        return [];\n    }\n}\n/**\n * Obtiene un documento específico por su ID (solo si pertenece al usuario actual)\n */ async function obtenerDocumentoPorId(id) {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('documentos').select('*').eq('id', id).eq('user_id', user.id).single();\n        if (error) {\n            console.error('Error al obtener documento:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error al obtener documento:', error);\n        return null;\n    }\n}\n/**\n * Guarda un nuevo documento en la base de datos asociado al usuario actual\n */ async function guardarDocumento(documento) {\n    try {\n        var _data_;\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        // Añadir el user_id y tipo_original al documento\n        const documentoConUsuario = {\n            ...documento,\n            user_id: user.id,\n            tipo_original: documento.tipo_original // Asegurarse que tipo_original se pasa aquí\n        };\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('documentos').insert([\n            documentoConUsuario\n        ]).select();\n        if (error) {\n            console.error('Error al guardar documento:', error);\n            return null;\n        }\n        return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n    } catch (error) {\n        console.error('Error al guardar documento:', error);\n        return null;\n    }\n}\n/**\n * Elimina un documento específico del usuario actual\n */ async function eliminarDocumento(documentoId) {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return false;\n        }\n        const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('documentos').delete().eq('id', documentoId).eq('user_id', user.id); // Asegurar que solo se eliminen documentos del usuario actual\n        if (error) {\n            console.error('Error al eliminar documento:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al eliminar documento:', error);\n        return false;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/documentosService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/supabase/index.ts":
/*!***********************************!*\
  !*** ./src/lib/supabase/index.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activarConversacion: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.activarConversacion),\n/* harmony export */   actualizarConversacion: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.actualizarConversacion),\n/* harmony export */   actualizarProgresoFlashcard: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.actualizarProgresoFlashcard),\n/* harmony export */   crearColeccionFlashcards: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.crearColeccionFlashcards),\n/* harmony export */   crearConversacion: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.crearConversacion),\n/* harmony export */   crearFlashcard: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.crearFlashcard),\n/* harmony export */   crearPreguntaTest: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.crearPreguntaTest),\n/* harmony export */   crearTest: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.crearTest),\n/* harmony export */   createClient: () => (/* reexport safe */ _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.createClient),\n/* harmony export */   desactivarTodasLasConversaciones: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.desactivarTodasLasConversaciones),\n/* harmony export */   eliminarDocumento: () => (/* reexport safe */ _documentosService__WEBPACK_IMPORTED_MODULE_1__.eliminarDocumento),\n/* harmony export */   guardarDocumento: () => (/* reexport safe */ _documentosService__WEBPACK_IMPORTED_MODULE_1__.guardarDocumento),\n/* harmony export */   guardarFlashcards: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.guardarFlashcards),\n/* harmony export */   guardarMensaje: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.guardarMensaje),\n/* harmony export */   guardarPreguntasTest: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.guardarPreguntasTest),\n/* harmony export */   guardarRevisionHistorial: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.guardarRevisionHistorial),\n/* harmony export */   obtenerColeccionFlashcardsPorId: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerColeccionFlashcardsPorId),\n/* harmony export */   obtenerColeccionesFlashcards: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerColeccionesFlashcards),\n/* harmony export */   obtenerConversacionActiva: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.obtenerConversacionActiva),\n/* harmony export */   obtenerConversacionPorId: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.obtenerConversacionPorId),\n/* harmony export */   obtenerConversaciones: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.obtenerConversaciones),\n/* harmony export */   obtenerDocumentoPorId: () => (/* reexport safe */ _documentosService__WEBPACK_IMPORTED_MODULE_1__.obtenerDocumentoPorId),\n/* harmony export */   obtenerDocumentos: () => (/* reexport safe */ _documentosService__WEBPACK_IMPORTED_MODULE_1__.obtenerDocumentos),\n/* harmony export */   obtenerEstadisticasColeccion: () => (/* reexport safe */ _estadisticasService__WEBPACK_IMPORTED_MODULE_4__.obtenerEstadisticasColeccion),\n/* harmony export */   obtenerEstadisticasDetalladas: () => (/* reexport safe */ _estadisticasService__WEBPACK_IMPORTED_MODULE_4__.obtenerEstadisticasDetalladas),\n/* harmony export */   obtenerEstadisticasEstudio: () => (/* reexport safe */ _estadisticasService__WEBPACK_IMPORTED_MODULE_4__.obtenerEstadisticasEstudio),\n/* harmony export */   obtenerEstadisticasGeneralesTests: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.obtenerEstadisticasGeneralesTests),\n/* harmony export */   obtenerEstadisticasTest: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.obtenerEstadisticasTest),\n/* harmony export */   obtenerFlashcardPorId: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerFlashcardPorId),\n/* harmony export */   obtenerFlashcardsParaEstudiar: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerFlashcardsParaEstudiar),\n/* harmony export */   obtenerFlashcardsPorColeccion: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerFlashcardsPorColeccion),\n/* harmony export */   obtenerFlashcardsPorColeccionId: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerFlashcardsPorColeccionId),\n/* harmony export */   obtenerHistorialRevisiones: () => (/* reexport safe */ _estadisticasService__WEBPACK_IMPORTED_MODULE_4__.obtenerHistorialRevisiones),\n/* harmony export */   obtenerMensajesPorConversacionId: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.obtenerMensajesPorConversacionId),\n/* harmony export */   obtenerPreguntasPorTestId: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.obtenerPreguntasPorTestId),\n/* harmony export */   obtenerPreguntasTestCount: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.obtenerPreguntasTestCount),\n/* harmony export */   obtenerProgresoFlashcard: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerProgresoFlashcard),\n/* harmony export */   obtenerTestPorId: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.obtenerTestPorId),\n/* harmony export */   obtenerTests: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.obtenerTests),\n/* harmony export */   registrarRespuestaFlashcard: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.registrarRespuestaFlashcard),\n/* harmony export */   registrarRespuestaTest: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.registrarRespuestaTest),\n/* harmony export */   reiniciarProgresoFlashcard: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.reiniciarProgresoFlashcard),\n/* harmony export */   supabase: () => (/* reexport safe */ _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _documentosService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./documentosService */ \"(app-pages-browser)/./src/lib/supabase/documentosService.ts\");\n/* harmony import */ var _conversacionesService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./conversacionesService */ \"(app-pages-browser)/./src/lib/supabase/conversacionesService.ts\");\n/* harmony import */ var _flashcardsService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./flashcardsService */ \"(app-pages-browser)/./src/lib/supabase/flashcardsService.ts\");\n/* harmony import */ var _estadisticasService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./estadisticasService */ \"(app-pages-browser)/./src/lib/supabase/estadisticasService.ts\");\n/* harmony import */ var _testsService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./testsService */ \"(app-pages-browser)/./src/lib/supabase/testsService.ts\");\n// Exportar todo desde los archivos individuales\n\n\n\n\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc3VwYWJhc2UvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGdEQUFnRDtBQUNmO0FBQ0c7QUFDSTtBQUNKO0FBQ0U7QUFDUCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcT3Bvc0kgdjdcXHNyY1xcbGliXFxzdXBhYmFzZVxcaW5kZXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0YXIgdG9kbyBkZXNkZSBsb3MgYXJjaGl2b3MgaW5kaXZpZHVhbGVzXG5leHBvcnQgKiBmcm9tICcuL3N1cGFiYXNlQ2xpZW50JztcbmV4cG9ydCAqIGZyb20gJy4vZG9jdW1lbnRvc1NlcnZpY2UnO1xuZXhwb3J0ICogZnJvbSAnLi9jb252ZXJzYWNpb25lc1NlcnZpY2UnO1xuZXhwb3J0ICogZnJvbSAnLi9mbGFzaGNhcmRzU2VydmljZSc7XG5leHBvcnQgKiBmcm9tICcuL2VzdGFkaXN0aWNhc1NlcnZpY2UnO1xuZXhwb3J0ICogZnJvbSAnLi90ZXN0c1NlcnZpY2UnO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/index.ts\n"));

/***/ })

});