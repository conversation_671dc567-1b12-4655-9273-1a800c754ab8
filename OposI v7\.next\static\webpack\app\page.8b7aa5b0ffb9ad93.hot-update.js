"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiCheck,FiCheckSquare,FiChevronRight,FiFileText,FiLayers,FiList,FiLogOut,FiMessageSquare,FiUpload!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _components_DocumentSelector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/DocumentSelector */ \"(app-pages-browser)/./src/components/DocumentSelector.tsx\");\n/* harmony import */ var _components_QuestionForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/QuestionForm */ \"(app-pages-browser)/./src/components/QuestionForm.tsx\");\n/* harmony import */ var _components_DocumentUploader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/DocumentUploader */ \"(app-pages-browser)/./src/components/DocumentUploader.tsx\");\n/* harmony import */ var _components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/MindMapGenerator */ \"(app-pages-browser)/./src/components/MindMapGenerator.tsx\");\n/* harmony import */ var _components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/FlashcardGenerator */ \"(app-pages-browser)/./src/components/FlashcardGenerator.tsx\");\n/* harmony import */ var _components_flashcards_FlashcardViewer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/flashcards/FlashcardViewer */ \"(app-pages-browser)/./src/components/flashcards/FlashcardViewer.tsx\");\n/* harmony import */ var _components_TestGenerator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../components/TestGenerator */ \"(app-pages-browser)/./src/components/TestGenerator.tsx\");\n/* harmony import */ var _components_TestViewer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../components/TestViewer */ \"(app-pages-browser)/./src/components/TestViewer.tsx\");\n/* harmony import */ var _components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../components/MobileDebugInfo */ \"(app-pages-browser)/./src/components/MobileDebugInfo.tsx\");\n/* harmony import */ var _components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../components/DiagnosticPanel */ \"(app-pages-browser)/./src/components/DiagnosticPanel.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\", _this = undefined, _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar TabButton = function TabButton(_ref) {\n    var active = _ref.active, onClick = _ref.onClick, icon = _ref.icon, label = _ref.label, color = _ref.color;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        className: \"flex items-center px-4 py-3 rounded-lg transition-all duration-200 font-medium text-sm \".concat(active ? \"text-white \".concat(color, \" shadow-md\") : 'text-gray-600 hover:bg-gray-100 hover:text-gray-800'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 5\n            }, _this),\n            label,\n            active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiChevronRight, {\n                className: \"ml-2\"\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 16\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 3\n    }, _this);\n};\n_c1 = TabButton;\n_c = TabButton;\nfunction Home() {\n    _s();\n    _s1();\n    var _user$email, _this2 = this;\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]), documentosSeleccionados = _useState[0], setDocumentosSeleccionados = _useState[1];\n    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), mostrarUploader = _useState2[0], setMostrarUploader = _useState2[1];\n    var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('preguntas'), activeTab = _useState3[0], setActiveTab = _useState3[1];\n    var _useState4 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), showUploadSuccess = _useState4[0], setShowUploadSuccess = _useState4[1];\n    var _useAuth = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_14__.useAuth)(), cerrarSesion = _useAuth.cerrarSesion, user = _useAuth.user, isLoading = _useAuth.isLoading;\n    var router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // No necesitamos verificar autenticación aquí, el middleware ya lo hace\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Home.useEffect\": function() {\n            console.log('[HomePage] Auth state - isLoading:', isLoading, 'User:', !!user);\n        }\n    }[\"Home.useEffect\"], [\n        user,\n        isLoading\n    ]);\n    // Si está cargando o no hay usuario, mostrar pantalla de carga\n    if (isLoading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 7\n        }, this);\n    }\n    var handleUploadSuccess = function handleUploadSuccess() {\n        setShowUploadSuccess(true);\n        setTimeout(function() {\n            return setShowUploadSuccess(false);\n        }, 3000);\n    };\n    var handleLogout = /*#__PURE__*/ function() {\n        var _ref2 = (0,C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee() {\n            return C_Users_naata_Documents_augment_projects_OposI_OposI_v7_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee$(_context) {\n                while(1)switch(_context.prev = _context.next){\n                    case 0:\n                        _context.next = 2;\n                        return cerrarSesion();\n                    case 2:\n                    case \"end\":\n                        return _context.stop();\n                }\n            }, _callee);\n        }));\n        return function handleLogout() {\n            return _ref2.apply(this, arguments);\n        };\n    }();\n    var tabs = [\n        {\n            id: 'preguntas',\n            label: 'Preguntas y Respuestas',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiMessageSquare, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 63\n            }, this),\n            color: 'bg-blue-600'\n        },\n        {\n            id: 'mapas',\n            label: 'Mapas Mentales',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiLayers, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 51\n            }, this),\n            color: 'bg-purple-600'\n        },\n        {\n            id: 'flashcards',\n            label: 'Generar Flashcards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiFileText, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 60\n            }, this),\n            color: 'bg-orange-500'\n        },\n        {\n            id: 'tests',\n            label: 'Generar Tests',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiList, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 50\n            }, this),\n            color: 'bg-indigo-600'\n        },\n        {\n            id: 'misFlashcards',\n            label: 'Mis Flashcards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiBook, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 59\n            }, this),\n            color: 'bg-emerald-600'\n        },\n        {\n            id: 'misTests',\n            label: 'Mis Tests',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiCheckSquare, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 49\n            }, this),\n            color: 'bg-pink-600'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                        children: \"OposiAI\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Tu asistente inteligente para oposiciones\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Hola, \",\n                                            (_user$email = user.email) === null || _user$email === void 0 ? void 0 : _user$email.split('@')[0]\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"button\", {\n                                        onClick: function onClick() {\n                                            return setMostrarUploader(!mostrarUploader);\n                                        },\n                                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiUpload, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this),\n                                            mostrarUploader ? 'Ocultar formulario' : 'Nuevo documento'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiLogOut, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cerrar sesi\\xF3n\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    mostrarUploader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                        className: \"mb-8 transition-all duration-300 ease-in-out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            onSuccess: handleUploadSuccess\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 128,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this),\n                    showUploadSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiCheck, {\n                                className: \"text-green-500 mr-2\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this),\n                            \"Documento subido exitosamente\"\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm p-4 sticky top-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"h2\", {\n                                            className: \"text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4 px-2\",\n                                            children: \"Men\\xFA de Estudio\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"nav\", {\n                                            className: \"space-y-1\",\n                                            children: tabs.map(function(tab) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(TabButton, {\n                                                    active: activeTab === tab.id,\n                                                    onClick: function onClick() {\n                                                        return setActiveTab(tab.id);\n                                                    },\n                                                    icon: tab.icon,\n                                                    label: tab.label,\n                                                    color: tab.color\n                                                }, tab.id, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 148,\n                                                    columnNumber: 19\n                                                }, _this2);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                                            className: \"mt-8 pt-6 border-t border-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-2\",\n                                                    children: \"Documentos Seleccionados\"\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 160,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    onSelectionChange: setDocumentosSeleccionados\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 163,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            activeTab === 'preguntas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_components_QuestionForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === 'mapas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 177,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === 'flashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 181,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === 'tests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_components_TestGenerator__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, this),\n                                            activeTab === 'misTests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_components_TestViewer__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 188,\n                                                columnNumber: 46\n                                            }, this),\n                                            activeTab === 'misFlashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_components_flashcards_FlashcardViewer__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 190,\n                                                columnNumber: 51\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200 mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500 text-sm\",\n                                    children: [\n                                        \"\\xA9 \",\n                                        new Date().getFullYear(),\n                                        \" OposiAI - Asistente para Oposiciones\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"div\", {\n                                className: \"mt-4 md:mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"T\\xE9rminos\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Privacidad\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Contacto\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_15__.jsxDEV)(_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"clDTlQWFFTIVZfcpGjL/YJjs5II=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_14__.useAuth,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_14__.useAuth,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_14__.useAuth,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_14__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c3 = Home;\n_s1(Home, \"LF6hBl5V53q5abOaRwPSUcde4Ro=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_14__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c2 = Home;\nvar _c, _c2;\n$RefreshReg$(_c, \"TabButton\");\n$RefreshReg$(_c2, \"Home\");\nvar _c1, _c3;\n$RefreshReg$(_c1, \"TabButton\");\n$RefreshReg$(_c3, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});