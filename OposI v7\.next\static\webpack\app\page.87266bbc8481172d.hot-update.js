"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/QuestionForm.tsx":
/*!*****************************************!*\
  !*** ./src/components/QuestionForm.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuestionForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _ConversationHistory__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ConversationHistory */ \"(app-pages-browser)/./src/components/ConversationHistory.tsx\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _lib_formSchemas__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/formSchemas */ \"(app-pages-browser)/./src/lib/formSchemas.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction QuestionForm(param) {\n    let { documentosSeleccionados } = param;\n    _s();\n    const [mensajes, setMensajes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [conversacionActualId, setConversacionActualId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mostrarHistorial, setMostrarHistorial] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [guardandoConversacion, setGuardandoConversacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cargandoConversacionActiva, setCargandoConversacionActiva] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { register, handleSubmit: handleSubmitForm, formState: { errors, isValid }, reset, setValue, watch } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(_lib_formSchemas__WEBPACK_IMPORTED_MODULE_5__.preguntaFormSchema),\n        defaultValues: {\n            pregunta: '',\n            documentos: documentosSeleccionados\n        }\n    });\n    // Observar cambios en el formulario para debugging\n    const watchedValues = watch();\n    // Log de debugging para el estado del formulario\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionForm.useEffect\": ()=>{\n            console.log('📝 Estado del formulario:', {\n                values: watchedValues,\n                errors,\n                isValid,\n                documentosSeleccionados: documentosSeleccionados.length\n            });\n        }\n    }[\"QuestionForm.useEffect\"], [\n        watchedValues,\n        errors,\n        isValid,\n        documentosSeleccionados\n    ]);\n    // Sincronizar documentos seleccionados con el formulario, asegurando tipos correctos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionForm.useEffect\": ()=>{\n            const documentosValidados = documentosSeleccionados.map({\n                \"QuestionForm.useEffect.documentosValidados\": (doc)=>({\n                        ...doc,\n                        numero_tema: doc.numero_tema !== undefined && doc.numero_tema !== null ? Number(doc.numero_tema) : undefined\n                    })\n            }[\"QuestionForm.useEffect.documentosValidados\"]);\n            setValue('documentos', documentosValidados);\n        }\n    }[\"QuestionForm.useEffect\"], [\n        documentosSeleccionados,\n        setValue\n    ]);\n    // Efecto para cargar la conversación activa al iniciar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionForm.useEffect\": ()=>{\n            const cargarConversacionActiva = {\n                \"QuestionForm.useEffect.cargarConversacionActiva\": async ()=>{\n                    setCargandoConversacionActiva(true);\n                    try {\n                        const conversacionActiva = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerConversacionActiva)();\n                        if (conversacionActiva) {\n                            console.log('Conversación activa encontrada:', conversacionActiva.id);\n                            setConversacionActualId(conversacionActiva.id);\n                            await cargarConversacion(conversacionActiva.id);\n                        } else {\n                            console.log('No hay conversación activa - esto es normal para usuarios nuevos');\n                            setMensajes([]);\n                            setConversacionActualId(null);\n                        }\n                    } catch (error) {\n                        console.warn('No se pudo cargar la conversación activa (esto es normal para usuarios nuevos):', error);\n                        // No mostrar error al usuario, simplemente inicializar sin conversación\n                        setMensajes([]);\n                        setConversacionActualId(null);\n                    } finally{\n                        setCargandoConversacionActiva(false);\n                    }\n                }\n            }[\"QuestionForm.useEffect.cargarConversacionActiva\"];\n            cargarConversacionActiva();\n        }\n    }[\"QuestionForm.useEffect\"], []);\n    // Efecto para hacer scroll al último mensaje cuando se añade uno nuevo\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionForm.useEffect\": ()=>{\n            if (chatContainerRef.current) {\n                chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n            }\n        }\n    }[\"QuestionForm.useEffect\"], [\n        mensajes\n    ]);\n    // Función para cargar una conversación desde Supabase\n    const cargarConversacion = async (conversacionId)=>{\n        try {\n            setIsLoading(true);\n            // Activar la conversación seleccionada\n            await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.activarConversacion)(conversacionId);\n            // Obtener los mensajes de la conversación\n            const mensajesDB = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerMensajesPorConversacionId)(conversacionId);\n            // Convertir los mensajes de la base de datos al formato local\n            const mensajesFormateados = mensajesDB.map((msg)=>({\n                    id: msg.id,\n                    tipo: msg.tipo,\n                    contenido: msg.contenido,\n                    timestamp: new Date(msg.timestamp)\n                }));\n            // Actualizar el estado\n            setMensajes(mensajesFormateados);\n            setConversacionActualId(conversacionId);\n            setError('');\n            console.log(\"Conversaci\\xf3n \".concat(conversacionId, \" cargada y activada\"));\n        } catch (error) {\n            console.error('Error al cargar la conversación:', error);\n            setError('No se pudo cargar la conversación');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Función para guardar un mensaje en Supabase\n    const guardarMensajeEnDB = async (mensaje)=>{\n        try {\n            setGuardandoConversacion(true);\n            // Si no hay una conversación actual, crear una nueva\n            if (!conversacionActualId) {\n                // Solo crear una nueva conversación si es el primer mensaje del usuario\n                if (mensaje.tipo === 'usuario') {\n                    // Crear un título basado en la primera pregunta\n                    const titulo = \"Conversaci\\xf3n: \".concat(mensaje.contenido.substring(0, 50)).concat(mensaje.contenido.length > 50 ? '...' : '');\n                    // Crear una nueva conversación y marcarla como activa\n                    const nuevoId = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.crearConversacion)(titulo, true);\n                    if (!nuevoId) {\n                        throw new Error('No se pudo crear la conversación');\n                    }\n                    console.log(\"Nueva conversaci\\xf3n creada y activada: \".concat(nuevoId));\n                    // Guardar el ID de la conversación para futuros mensajes\n                    setConversacionActualId(nuevoId);\n                    // Guardar el mensaje en la nueva conversación\n                    await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.guardarMensaje)({\n                        conversacion_id: nuevoId,\n                        tipo: mensaje.tipo,\n                        contenido: mensaje.contenido\n                    });\n                } else {\n                    // Si es un mensaje de la IA pero no hay conversación actual,\n                    // algo salió mal. Intentar recuperar creando una nueva conversación.\n                    console.warn('No hay conversación actual para guardar el mensaje de la IA. Creando una nueva.');\n                    const titulo = 'Nueva conversación';\n                    const nuevoId = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.crearConversacion)(titulo, true);\n                    if (!nuevoId) {\n                        throw new Error('No se pudo crear la conversación');\n                    }\n                    console.log(\"Nueva conversaci\\xf3n de recuperaci\\xf3n creada: \".concat(nuevoId));\n                    setConversacionActualId(nuevoId);\n                    // Guardar el mensaje en la nueva conversación\n                    await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.guardarMensaje)({\n                        conversacion_id: nuevoId,\n                        tipo: mensaje.tipo,\n                        contenido: mensaje.contenido\n                    });\n                }\n            } else {\n                // Verificar que la conversación actual sigue siendo la activa\n                const conversacionActiva = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerConversacionActiva)();\n                if (!conversacionActiva || conversacionActiva.id !== conversacionActualId) {\n                    // Si la conversación actual no es la activa, activarla\n                    console.log(\"Reactivando conversaci\\xf3n: \".concat(conversacionActualId));\n                    await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.activarConversacion)(conversacionActualId);\n                }\n                // Guardar el mensaje en la conversación existente\n                await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.guardarMensaje)({\n                    conversacion_id: conversacionActualId,\n                    tipo: mensaje.tipo,\n                    contenido: mensaje.contenido\n                });\n            }\n        } catch (error) {\n            console.error('Error al guardar el mensaje:', error);\n        // No mostramos error al usuario para no interrumpir la experiencia\n        } finally{\n            setGuardandoConversacion(false);\n        }\n    };\n    // Función para iniciar una nueva conversación\n    const iniciarNuevaConversacion = async ()=>{\n        try {\n            setIsLoading(true);\n            // Desactivar todas las conversaciones en la base de datos\n            await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.desactivarTodasLasConversaciones)();\n            // Limpiar los mensajes actuales\n            setMensajes([]);\n            // Establecer el ID de conversación a null para que se cree una nueva en el próximo mensaje\n            setConversacionActualId(null);\n            setError('');\n            console.log('Nueva conversación iniciada. El próximo mensaje creará una nueva conversación en la base de datos.');\n        } catch (error) {\n            console.error('Error al iniciar nueva conversación:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Cambia handleSubmit para usar React Hook Form\n    const onSubmit = async (data)=>{\n        console.log('🎉 ¡FUNCIÓN onSubmit EJECUTADA!');\n        console.log('🚀 Formulario enviado con datos:', data);\n        console.log('📄 Documentos seleccionados:', documentosSeleccionados);\n        console.log('✅ Validación pasada correctamente');\n        setIsLoading(true);\n        setError('');\n        // Añadir la pregunta del usuario al historial\n        const preguntaUsuario = {\n            tipo: 'usuario',\n            contenido: data.pregunta,\n            timestamp: new Date()\n        };\n        setMensajes((prevMensajes)=>[\n                ...prevMensajes,\n                preguntaUsuario\n            ]);\n        setIsLoading(true);\n        setError('');\n        // Limpiar el campo de pregunta después de enviarla\n        reset({\n            pregunta: '',\n            documentos: documentosSeleccionados\n        });\n        try {\n            // Guardar la pregunta del usuario en Supabase\n            await guardarMensajeEnDB(preguntaUsuario);\n            // Pasar los documentos completos a la función obtenerRespuestaIA\n            // No solo el contenido, sino también el título, categoría y número de tema\n            // Obtener respuesta de la IA\n            const response = await fetch('/api/gemini', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    pregunta: preguntaUsuario.contenido,\n                    documentos: data.documentos\n                })\n            });\n            const respuestaIA = await response.json();\n            let respuestaTexto = '';\n            if (respuestaIA.result) {\n                respuestaTexto = typeof respuestaIA.result === 'string' ? respuestaIA.result : JSON.stringify(respuestaIA.result);\n            } else if (respuestaIA.error) {\n                respuestaTexto = typeof respuestaIA.error === 'string' ? respuestaIA.error : JSON.stringify(respuestaIA.error);\n            } else {\n                respuestaTexto = 'Error desconocido al obtener respuesta de la IA.';\n            }\n            // Añadir la respuesta de la IA al historial\n            const mensajeIA = {\n                tipo: 'ia',\n                contenido: respuestaTexto,\n                timestamp: new Date()\n            };\n            setMensajes((prevMensajes)=>[\n                    ...prevMensajes,\n                    mensajeIA\n                ]);\n            // Guardar la respuesta de la IA en Supabase\n            await guardarMensajeEnDB(mensajeIA);\n            // Si es la primera pregunta, actualizar el título de la conversación con un título más descriptivo\n            if (mensajes.length === 0 && conversacionActualId) {\n                const tituloMejorado = \"Conversaci\\xf3n: \".concat(data.pregunta.substring(0, 50)).concat(data.pregunta.length > 50 ? '...' : '');\n                await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.actualizarConversacion)(conversacionActualId, tituloMejorado);\n            }\n        } catch (error) {\n            console.error('Error al obtener respuesta:', error);\n            // Determinar el tipo de error y mostrar un mensaje más específico\n            let mensajeError = 'Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, inténtalo de nuevo.';\n            if (error instanceof Error) {\n                if (error.message.includes('API key')) {\n                    mensajeError = 'Error de configuración: La clave de API de Gemini no está configurada correctamente.';\n                } else if (error.message.includes('network') || error.message.includes('fetch')) {\n                    mensajeError = 'Error de conexión: No se pudo conectar con el servicio de IA. Verifica tu conexión a internet.';\n                } else if (error.message.includes('quota') || error.message.includes('limit')) {\n                    mensajeError = 'Se ha alcanzado el límite de uso del servicio de IA. Inténtalo más tarde.';\n                } else {\n                    mensajeError = \"Error: \".concat(error.message);\n                }\n            }\n            setError(mensajeError);\n            // Añadir mensaje de error como respuesta de la IA\n            const mensajeErrorIA = {\n                tipo: 'ia',\n                contenido: mensajeError,\n                timestamp: new Date()\n            };\n            setMensajes((prevMensajes)=>[\n                    ...prevMensajes,\n                    mensajeErrorIA\n                ]);\n            // Intentar guardar el mensaje de error en Supabase (sin fallar si no se puede)\n            try {\n                await guardarMensajeEnDB(mensajeErrorIA);\n            } catch (dbError) {\n                console.error('Error al guardar mensaje de error en DB:', dbError);\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Formatear la fecha para mostrarla en el chat\n    const formatearFecha = (fecha)=>{\n        return fecha.toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-6 flex flex-col h-[600px]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>setMostrarHistorial(!mostrarHistorial),\n                        className: \"bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded inline-flex items-center\",\n                        children: mostrarHistorial ? 'Ocultar historial' : 'Ver historial de conversaciones'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: iniciarNuevaConversacion,\n                        className: \"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded inline-flex items-center\",\n                        disabled: isLoading,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"h-5 w-5 mr-2\",\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 11\n                            }, this),\n                            \"Nueva conversaci\\xf3n\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                lineNumber: 364,\n                columnNumber: 7\n            }, this),\n            mostrarHistorial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConversationHistory__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onSelectConversation: cargarConversacion,\n                conversacionActualId: conversacionActualId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                lineNumber: 388,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatContainerRef,\n                className: \"flex-grow overflow-y-auto mb-4 p-4 border rounded-lg bg-gray-50\",\n                style: {\n                    height: 'calc(100% - 180px)'\n                },\n                children: mensajes.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full text-gray-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Selecciona documentos y haz una pregunta para comenzar la conversaci\\xf3n.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                    lineNumber: 401,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        mensajes.map((mensaje, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex \".concat(mensaje.tipo === 'usuario' ? 'justify-end' : 'justify-start'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-[80%] p-3 rounded-lg \".concat(mensaje.tipo === 'usuario' ? 'bg-blue-500 text-white rounded-br-none' : 'bg-white border border-gray-300 rounded-bl-none'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"whitespace-pre-wrap\",\n                                            children: mensaje.contenido\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs mt-1 text-right \".concat(mensaje.tipo === 'usuario' ? 'text-blue-100' : 'text-gray-500'),\n                                            children: formatearFecha(mensaje.timestamp)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 17\n                                }, this)\n                            }, mensaje.id || index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 15\n                            }, this)),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-3 rounded-lg border border-gray-300 rounded-bl-none\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-bounce h-2 w-2 bg-gray-500 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-bounce h-2 w-2 bg-gray-500 rounded-full\",\n                                            style: {\n                                                animationDelay: '0.2s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-bounce h-2 w-2 bg-gray-500 rounded-full\",\n                                            style: {\n                                                animationDelay: '0.4s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 15\n                        }, this),\n                        guardandoConversacion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 text-center py-1\",\n                            children: \"Guardando conversaci\\xf3n...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                    lineNumber: 405,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                lineNumber: 395,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: (e)=>{\n                    console.log('📋 Evento onSubmit del formulario detectado');\n                    console.log('🎯 Llamando a handleSubmitForm...');\n                    handleSubmitForm(onSubmit)(e);\n                },\n                className: \"mt-auto\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-500 text-sm mb-2\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-600 mb-2\",\n                        children: documentosSeleccionados.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-600\",\n                            children: [\n                                \"✓ \",\n                                documentosSeleccionados.length,\n                                \" documento\",\n                                documentosSeleccionados.length !== 1 ? 's' : '',\n                                \" seleccionado\",\n                                documentosSeleccionados.length !== 1 ? 's' : ''\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-600\",\n                            children: \"⚠ No hay documentos seleccionados. Selecciona al menos uno para hacer preguntas.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-grow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"pregunta\",\n                                        className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                        rows: 2,\n                                        ...register('pregunta'),\n                                        placeholder: \"Escribe tu pregunta sobre los documentos seleccionados...\",\n                                        disabled: isLoading,\n                                        onKeyDown: (e)=>{\n                                            if (e.key === 'Enter' && !e.shiftKey) {\n                                                e.preventDefault();\n                                                handleSubmitForm(onSubmit)(); // Ejecutar la función devuelta\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.pregunta && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-500 text-xs mt-1\",\n                                        children: errors.pregunta.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: \"Presiona Enter para enviar, Shift+Enter para nueva l\\xednea\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-full h-10 w-10 flex items-center justify-center focus:outline-none focus:shadow-outline disabled:opacity-50 disabled:cursor-not-allowed\",\n                                disabled: isLoading || documentosSeleccionados.length === 0,\n                                title: documentosSeleccionados.length === 0 ? 'Selecciona al menos un documento para hacer una pregunta' : 'Enviar pregunta',\n                                onClick: ()=>{\n                                    console.log('🖱️ Click en botón de envío detectado');\n                                    console.log('🔒 Botón deshabilitado?', isLoading || documentosSeleccionados.length === 0);\n                                    console.log('⏳ isLoading:', isLoading);\n                                    console.log('📄 documentos seleccionados:', documentosSeleccionados.length);\n                                },\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin h-5 w-5 text-white\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 008-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-5 w-5 text-white\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 477,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                lineNumber: 452,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n        lineNumber: 362,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionForm, \"I42aJdgBQNzGUtf/645NjTDxehY=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm\n    ];\n});\n_c = QuestionForm;\nvar _c;\n$RefreshReg$(_c, \"QuestionForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/QuestionForm.tsx\n"));

/***/ })

});