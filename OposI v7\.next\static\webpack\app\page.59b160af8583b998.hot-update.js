"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/flashcards/FlashcardViewer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/flashcards/FlashcardViewer.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _barrel_optimize_names_FiAlertTriangle_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlertTriangle,FiEdit2,FiRefreshCw,FiTrash2!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./FlashcardCollectionList */ \"(app-pages-browser)/./src/components/flashcards/FlashcardCollectionList.tsx\");\n/* harmony import */ var _FlashcardStatistics__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FlashcardStatistics */ \"(app-pages-browser)/./src/components/flashcards/FlashcardStatistics.tsx\");\n/* harmony import */ var _FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./FlashcardStudyMode */ \"(app-pages-browser)/./src/components/flashcards/FlashcardStudyMode.tsx\");\n/* harmony import */ var _FlashcardEditModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./FlashcardEditModal */ \"(app-pages-browser)/./src/components/flashcards/FlashcardEditModal.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst FlashcardViewer = ()=>{\n    _s();\n    // Estado para las colecciones\n    const [colecciones, setColecciones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coleccionSeleccionada, setColeccionSeleccionada] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para las flashcards\n    const [flashcards, setFlashcards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [mostrarRespuesta, setMostrarRespuesta] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [respondiendo, setRespondiendo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estado para el modo de estudio\n    const [modoEstudio, setModoEstudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estado para estadísticas\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para carga y errores\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Estado para edición y eliminación de flashcards\n    const [flashcardEditando, setFlashcardEditando] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDeleteConfirm, setShowDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para eliminación de colecciones\n    const [showDeleteCollectionConfirm, setShowDeleteCollectionConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletingCollectionId, setDeletingCollectionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Cargar colecciones al montar el componente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashcardViewer.useEffect\": ()=>{\n            const cargarColecciones = {\n                \"FlashcardViewer.useEffect.cargarColecciones\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n                        setColecciones(data);\n                    } catch (error) {\n                        console.error('Error al cargar colecciones:', error);\n                        setError('No se pudieron cargar las colecciones de flashcards');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"FlashcardViewer.useEffect.cargarColecciones\"];\n            cargarColecciones();\n        }\n    }[\"FlashcardViewer.useEffect\"], []);\n    // Manejar la selección de una colección\n    const handleSeleccionarColeccion = async (coleccion)=>{\n        setIsLoading(true);\n        setError('');\n        setColeccionSeleccionada(coleccion);\n        setActiveIndex(0);\n        setMostrarRespuesta(false);\n        setRespondiendo(false);\n        setModoEstudio(false);\n        try {\n            // Cargar flashcards con su progreso\n            const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccion.id);\n            // Ordenar las flashcards: primero las que deben estudiarse hoy, luego el resto\n            const ordenadas = [\n                ...data\n            ].sort((a, b)=>{\n                if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                return 0;\n            });\n            setFlashcards(ordenadas);\n            // Cargar estadísticas\n            const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccion.id);\n            setEstadisticas(stats);\n        } catch (error) {\n            console.error('Error al cargar flashcards:', error);\n            setError('No se pudieron cargar las flashcards de esta colección');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Iniciar el modo de estudio\n    const iniciarModoEstudio = async ()=>{\n        setIsLoading(true);\n        try {\n            if (coleccionSeleccionada) {\n                // Recargar las flashcards para asegurarnos de tener los datos más recientes\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                // Filtrar solo las flashcards que deben estudiarse hoy\n                const flashcardsParaEstudiar = data.filter((flashcard)=>flashcard.debeEstudiar);\n                // Actualizar estadísticas\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n                // Verificar si el número de flashcards para estudiar coincide con las estadísticas\n                if (flashcardsParaEstudiar.length !== stats.paraHoy) {\n                    console.warn(\"Discrepancia en el conteo: \".concat(flashcardsParaEstudiar.length, \" flashcards filtradas vs \").concat(stats.paraHoy, \" en estad\\xedsticas\"));\n                }\n                // Si no hay flashcards para hoy, mostrar un mensaje\n                if (flashcardsParaEstudiar.length === 0) {\n                    if (data.length === 0) {\n                        alert('No hay flashcards en esta colección.');\n                    } else {\n                        alert('No hay flashcards programadas para estudiar hoy. Vuelve mañana o ajusta el progreso de las tarjetas.');\n                    }\n                    return; // Salir sin iniciar el modo estudio\n                }\n                // Usar solo las flashcards programadas para hoy\n                setFlashcards(flashcardsParaEstudiar);\n                // Iniciar el modo de estudio\n                setModoEstudio(true);\n                setActiveIndex(0);\n                setMostrarRespuesta(false);\n                setRespondiendo(false);\n            }\n        } catch (error) {\n            console.error('Error al iniciar modo de estudio:', error);\n            setError('No se pudo iniciar el modo de estudio');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Manejar la navegación entre flashcards\n    const handleNavigate = (direction)=>{\n        if (direction === 'prev' && activeIndex > 0) {\n            setActiveIndex(activeIndex - 1);\n            setMostrarRespuesta(false);\n        } else if (direction === 'next' && activeIndex < flashcards.length - 1) {\n            setActiveIndex(activeIndex + 1);\n            setMostrarRespuesta(false);\n        }\n    };\n    // Manejar la respuesta a una flashcard\n    const handleRespuesta = async (dificultad)=>{\n        if (!coleccionSeleccionada || flashcards.length === 0) return;\n        const flashcardId = flashcards[activeIndex].id;\n        setRespondiendo(true);\n        try {\n            // Registrar la respuesta\n            await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.registrarRespuestaFlashcard)(flashcardId, dificultad);\n            // Recargar las flashcards y estadísticas si estamos en la última tarjeta\n            if (activeIndex >= flashcards.length - 1 && coleccionSeleccionada) {\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                const flashcardsParaEstudiar = data.filter((flashcard)=>flashcard.debeEstudiar);\n                // Actualizar estadísticas\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n                if (flashcardsParaEstudiar.length > 0) {\n                    setFlashcards(flashcardsParaEstudiar);\n                    setActiveIndex(0);\n                } else {\n                    // Si no hay más flashcards para hoy, mostrar mensaje y salir del modo de estudio\n                    alert('¡Has completado todas las flashcards para hoy! Vuelve mañana para continuar estudiando.');\n                    setModoEstudio(false);\n                    // Ordenar las flashcards: primero las que deben estudiarse (aunque ya no haya ninguna), luego el resto\n                    const ordenadas = [\n                        ...data\n                    ].sort((a, b)=>{\n                        if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                        if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                        return 0;\n                    });\n                    setFlashcards(ordenadas);\n                }\n            } else {\n                // Avanzar a la siguiente flashcard\n                handleNavigate('next');\n            }\n        } catch (error) {\n            console.error('Error al registrar respuesta:', error);\n            setError('No se pudo registrar la respuesta');\n        } finally{\n            setRespondiendo(false);\n            setMostrarRespuesta(false);\n        }\n    };\n    // Salir del modo de estudio\n    const handleSalirModoEstudio = ()=>{\n        setModoEstudio(false);\n    };\n    // Manejar la edición de una flashcard\n    const handleEditarFlashcard = (flashcard)=>{\n        setFlashcardEditando(flashcard);\n        setShowEditModal(true);\n    };\n    // Manejar el guardado de una flashcard editada\n    const handleGuardarFlashcard = (flashcardActualizada)=>{\n        // Actualizar la flashcard en la lista local\n        setFlashcards((prev)=>prev.map((fc)=>fc.id === flashcardActualizada.id ? flashcardActualizada : fc));\n    };\n    // Manejar la eliminación de una flashcard\n    const handleEliminarFlashcard = async (flashcardId)=>{\n        setDeletingId(flashcardId);\n        let loadingToastId;\n        try {\n            loadingToastId = react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].loading('Eliminando flashcard...');\n            const success = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.eliminarFlashcard)(flashcardId);\n            if (success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success('Flashcard eliminada exitosamente', {\n                    id: loadingToastId\n                });\n                // Actualizar la lista local\n                setFlashcards((prev)=>prev.filter((fc)=>fc.id !== flashcardId));\n                // Recargar estadísticas\n                if (coleccionSeleccionada) {\n                    const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                    setEstadisticas(stats);\n                }\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error('Error al eliminar la flashcard', {\n                    id: loadingToastId\n                });\n            }\n        } catch (error) {\n            console.error('Error al eliminar flashcard:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error('Error al eliminar la flashcard', {\n                id: loadingToastId\n            });\n        } finally{\n            setDeletingId(null);\n            setShowDeleteConfirm(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 269,\n                columnNumber: 9\n            }, undefined),\n            modoEstudio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                flashcards: flashcards,\n                activeIndex: activeIndex,\n                respondiendo: respondiendo,\n                onRespuesta: handleRespuesta,\n                onNavigate: handleNavigate,\n                onVolver: handleSalirModoEstudio\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 275,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"Mis Flashcards\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        colecciones: colecciones,\n                        coleccionSeleccionada: coleccionSeleccionada,\n                        onSeleccionarColeccion: handleSeleccionarColeccion,\n                        isLoading: isLoading\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 11\n                    }, undefined),\n                    coleccionSeleccionada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: coleccionSeleccionada.titulo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardStatistics__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                estadisticas: estadisticas\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: iniciarModoEstudio,\n                                            className: \"bg-orange-500 hover:bg-orange-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                                            children: \"Estudiar (\".concat(estadisticas ? estadisticas.paraHoy : 0, \" para hoy)\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{},\n                                            className: \"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                                            children: \"Ver estad\\xedsticas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 15\n                            }, undefined),\n                            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center items-center h-40\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 17\n                            }, undefined) : flashcards.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"No hay flashcards en esta colecci\\xf3n.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: flashcards.map((flashcard, index)=>{\n                                    var _flashcard_progreso, _flashcard_progreso1;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4 \".concat(flashcard.debeEstudiar ? 'border-orange-300 bg-orange-50' : 'border-gray-200'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            \"Tarjeta \",\n                                                            index + 1\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            ((_flashcard_progreso = flashcard.progreso) === null || _flashcard_progreso === void 0 ? void 0 : _flashcard_progreso.estado) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full text-xs \".concat(flashcard.progreso.estado === 'nuevo' ? 'bg-blue-100 text-blue-800' : flashcard.progreso.estado === 'aprendiendo' ? 'bg-yellow-100 text-yellow-800' : flashcard.progreso.estado === 'repasando' ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'),\n                                                                children: flashcard.progreso.estado\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            !((_flashcard_progreso1 = flashcard.progreso) === null || _flashcard_progreso1 === void 0 ? void 0 : _flashcard_progreso1.estado) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs\",\n                                                                children: \"nuevo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleEditarFlashcard(flashcard),\n                                                                        className: \"p-1 text-blue-500 hover:bg-blue-50 rounded transition-colors\",\n                                                                        title: \"Editar flashcard\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiEdit2, {\n                                                                            size: 14\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                            lineNumber: 369,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                        lineNumber: 364,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setShowDeleteConfirm(flashcard.id),\n                                                                        disabled: deletingId === flashcard.id,\n                                                                        className: \"p-1 text-red-500 hover:bg-red-50 rounded transition-colors disabled:opacity-50\",\n                                                                        title: \"Eliminar flashcard\",\n                                                                        children: deletingId === flashcard.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiRefreshCw, {\n                                                                            size: 14,\n                                                                            className: \"animate-spin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                            lineNumber: 378,\n                                                                            columnNumber: 33\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiTrash2, {\n                                                                            size: 14\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                            lineNumber: 380,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                        lineNumber: 371,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-2\",\n                                                children: flashcard.pregunta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-700 line-clamp-2\",\n                                                children: flashcard.respuesta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, flashcard.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 21\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 284,\n                columnNumber: 9\n            }, undefined),\n            flashcardEditando && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardEditModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                flashcard: flashcardEditando,\n                isOpen: showEditModal,\n                onClose: ()=>{\n                    setShowEditModal(false);\n                    setFlashcardEditando(null);\n                },\n                onSave: handleGuardarFlashcard\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 399,\n                columnNumber: 9\n            }, undefined),\n            showDeleteConfirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 max-w-md w-full mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiAlertTriangle, {\n                                    className: \"text-red-500 mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"Confirmar eliminaci\\xf3n\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"\\xbfEst\\xe1s seguro de que quieres eliminar esta flashcard? Esta acci\\xf3n no se puede deshacer y se perder\\xe1 todo el progreso asociado.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowDeleteConfirm(null),\n                                    className: \"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\",\n                                    disabled: deletingId !== null,\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleEliminarFlashcard(showDeleteConfirm),\n                                    className: \"px-4 py-2 bg-red-500 text-white hover:bg-red-600 rounded-lg transition-colors\",\n                                    disabled: deletingId !== null,\n                                    children: \"Eliminar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 412,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n        lineNumber: 267,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlashcardViewer, \"+HTyYSOPk7h+QHp9CprRrCNOQms=\");\n_c = FlashcardViewer;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlashcardViewer);\nvar _c;\n$RefreshReg$(_c, \"FlashcardViewer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/flashcards/FlashcardViewer.tsx\n"));

/***/ })

});