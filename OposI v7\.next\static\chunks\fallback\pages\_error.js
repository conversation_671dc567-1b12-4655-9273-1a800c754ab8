/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/_error"],{

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error! ***!
  \************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/_error\",\n      function () {\n        return __webpack_require__(/*! next/dist/pages/_error */ \"(pages-dir-browser)/./node_modules/next/dist/pages/_error.js\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/_error\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtY2xpZW50LXBhZ2VzLWxvYWRlci5qcz9hYnNvbHV0ZVBhZ2VQYXRoPW5leHQlMkZkaXN0JTJGcGFnZXMlMkZfZXJyb3ImcGFnZT0lMkZfZXJyb3IhIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsNEZBQXdCO0FBQy9DO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL19lcnJvclwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIm5leHQvZGlzdC9wYWdlcy9fZXJyb3JcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL19lcnJvclwiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/pages/_error.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/pages/_error.js ***!
  \************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nvar _classCallCheck = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/classCallCheck.js */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/classCallCheck.js\");\nvar _createClass = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/createClass.js */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/createClass.js\");\nvar _inherits = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/inherits.js */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/inherits.js\");\nvar _possibleConstructorReturn = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/possibleConstructorReturn.js */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/possibleConstructorReturn.js\");\nvar _getPrototypeOf = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/getPrototypeOf.js */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/getPrototypeOf.js\");\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n  enumerable: true,\n  get: function get() {\n    return Error;\n  }\n}));\nvar _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nvar _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nvar _react = /*#__PURE__*/_interop_require_default._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nvar _head = /*#__PURE__*/_interop_require_default._(__webpack_require__(/*! ../shared/lib/head */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/head.js\"));\nvar statusCodes = {\n  400: 'Bad Request',\n  404: 'This page could not be found',\n  405: 'Method Not Allowed',\n  500: 'Internal Server Error'\n};\nfunction _getInitialProps(param) {\n  var req = param.req,\n    res = param.res,\n    err = param.err;\n  var statusCode = res && res.statusCode ? res.statusCode : err ? err.statusCode : 404;\n  var hostname;\n  if (true) {\n    hostname = window.location.hostname;\n  } else { var url, initUrl, _require, getRequestMeta; }\n  return {\n    statusCode: statusCode,\n    hostname: hostname\n  };\n}\nvar styles = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily: 'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  desc: {\n    lineHeight: '48px'\n  },\n  h1: {\n    display: 'inline-block',\n    margin: '0 20px 0 0',\n    paddingRight: 23,\n    fontSize: 24,\n    fontWeight: 500,\n    verticalAlign: 'top'\n  },\n  h2: {\n    fontSize: 14,\n    fontWeight: 400,\n    lineHeight: '28px'\n  },\n  wrap: {\n    display: 'inline-block'\n  }\n};\nvar Error = /*#__PURE__*/function (_react$default$Compon) {\n  _inherits(Error, _react$default$Compon);\n  var _super = _createSuper(Error);\n  function Error() {\n    _classCallCheck(this, Error);\n    return _super.apply(this, arguments);\n  }\n  _createClass(Error, [{\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        statusCode = _this$props.statusCode,\n        _this$props$withDarkM = _this$props.withDarkMode,\n        withDarkMode = _this$props$withDarkM === void 0 ? true : _this$props$withDarkM;\n      var title = this.props.title || statusCodes[statusCode] || 'An unexpected error has occurred';\n      return /*#__PURE__*/(0, _jsxruntime.jsxs)(\"div\", {\n        style: styles.error,\n        children: [/*#__PURE__*/(0, _jsxruntime.jsx)(_head[\"default\"], {\n          children: /*#__PURE__*/(0, _jsxruntime.jsx)(\"title\", {\n            children: statusCode ? statusCode + \": \" + title : 'Application error: a client-side exception has occurred'\n          })\n        }), /*#__PURE__*/(0, _jsxruntime.jsxs)(\"div\", {\n          style: styles.desc,\n          children: [/*#__PURE__*/(0, _jsxruntime.jsx)(\"style\", {\n            dangerouslySetInnerHTML: {\n              /* CSS minified from\n              body { margin: 0; color: #000; background: #fff; }\n              .next-error-h1 {\n              border-right: 1px solid rgba(0, 0, 0, .3);\n              }\n              ${\n              withDarkMode\n              ? `@media (prefers-color-scheme: dark) {\n              body { color: #fff; background: #000; }\n              .next-error-h1 {\n              border-right: 1px solid rgba(255, 255, 255, .3);\n              }\n              }`\n              : ''\n              }\n              */\n              __html: \"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}\" + (withDarkMode ? '@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}' : '')\n            }\n          }), statusCode ? /*#__PURE__*/(0, _jsxruntime.jsx)(\"h1\", {\n            className: \"next-error-h1\",\n            style: styles.h1,\n            children: statusCode\n          }) : null, /*#__PURE__*/(0, _jsxruntime.jsx)(\"div\", {\n            style: styles.wrap,\n            children: /*#__PURE__*/(0, _jsxruntime.jsxs)(\"h2\", {\n              style: styles.h2,\n              children: [this.props.title || statusCode ? title : /*#__PURE__*/(0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\"Application error: a client-side exception has occurred\", ' ', Boolean(this.props.hostname) && /*#__PURE__*/(0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                  children: [\"while loading \", this.props.hostname]\n                }), ' ', \"(see the browser console for more information)\"]\n              }), \".\"]\n            })\n          })]\n        })]\n      });\n    }\n  }]);\n  return Error;\n}(_react[\"default\"].Component);\nError.displayName = 'ErrorPage';\nError.getInitialProps = _getInitialProps;\nError.origGetInitialProps = _getInitialProps;\nif ((typeof exports[\"default\"] === 'function' || typeof exports[\"default\"] === 'object' && exports[\"default\"] !== null) && typeof exports[\"default\"].__esModule === 'undefined') {\n  Object.defineProperty(exports[\"default\"], '__esModule', {\n    value: true\n  });\n  Object.assign(exports[\"default\"], exports);\n  module.exports = exports[\"default\"];\n}\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/pages/_error.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js ***!
  \*************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"AmpStateContext\", ({\n  enumerable: true,\n  get: function get() {\n    return AmpStateContext;\n  }\n}));\nvar _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nvar _react = /*#__PURE__*/_interop_require_default._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nvar AmpStateContext = _react[\"default\"].createContext({});\nif (true) {\n  AmpStateContext.displayName = 'AmpStateContext';\n}\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-mode.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/amp-mode.js ***!
  \*******************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"isInAmpMode\", ({\n  enumerable: true,\n  get: function get() {\n    return isInAmpMode;\n  }\n}));\nfunction isInAmpMode(param) {\n  var _ref = param === void 0 ? {} : param,\n    _ref$ampFirst = _ref.ampFirst,\n    ampFirst = _ref$ampFirst === void 0 ? false : _ref$ampFirst,\n    _ref$hybrid = _ref.hybrid,\n    hybrid = _ref$hybrid === void 0 ? false : _ref$hybrid,\n    _ref$hasQuery = _ref.hasQuery,\n    hasQuery = _ref$hasQuery === void 0 ? false : _ref$hasQuery;\n  return ampFirst || hybrid && hasQuery;\n}\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9hbXAtbW9kZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFDYkEsOENBQTZDO0VBQ3pDRyxLQUFLLEVBQUU7QUFDWCxDQUFDLEVBQUM7QUFDRkgsK0NBQThDO0VBQzFDSSxVQUFVLEVBQUUsSUFBSTtFQUNoQkMsR0FBRyxFQUFFLFNBQUFBLElBQUEsRUFBVztJQUNaLE9BQU9DLFdBQVc7RUFDdEI7QUFDSixDQUFDLEVBQUM7QUFDRixTQUFTQSxXQUFXQSxDQUFDQyxLQUFLLEVBQUU7RUFDeEIsSUFBQUMsSUFBQSxHQUE2REQsS0FBSyxLQUFLLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFHQSxLQUFLO0lBQUFFLGFBQUEsR0FBQUQsSUFBQSxDQUFwRkUsUUFBUTtJQUFSQSxRQUFRLEdBQUFELGFBQUEsY0FBRyxLQUFLLEdBQUFBLGFBQUE7SUFBQUUsV0FBQSxHQUFBSCxJQUFBLENBQUVJLE1BQU07SUFBTkEsTUFBTSxHQUFBRCxXQUFBLGNBQUcsS0FBSyxHQUFBQSxXQUFBO0lBQUFFLGFBQUEsR0FBQUwsSUFBQSxDQUFFTSxRQUFRO0lBQVJBLFFBQVEsR0FBQUQsYUFBQSxjQUFHLEtBQUssR0FBQUEsYUFBQTtFQUN4RCxPQUFPSCxRQUFRLElBQUlFLE1BQU0sSUFBSUUsUUFBUTtBQUN6QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcT3Bvc0kgdjdcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcc2hhcmVkXFxsaWJcXGFtcC1tb2RlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiaXNJbkFtcE1vZGVcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIGlzSW5BbXBNb2RlO1xuICAgIH1cbn0pO1xuZnVuY3Rpb24gaXNJbkFtcE1vZGUocGFyYW0pIHtcbiAgICBsZXQgeyBhbXBGaXJzdCA9IGZhbHNlLCBoeWJyaWQgPSBmYWxzZSwgaGFzUXVlcnkgPSBmYWxzZSB9ID0gcGFyYW0gPT09IHZvaWQgMCA/IHt9IDogcGFyYW07XG4gICAgcmV0dXJuIGFtcEZpcnN0IHx8IGh5YnJpZCAmJiBoYXNRdWVyeTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YW1wLW1vZGUuanMubWFwIl0sIm5hbWVzIjpbIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiZW51bWVyYWJsZSIsImdldCIsImlzSW5BbXBNb2RlIiwicGFyYW0iLCJfcmVmIiwiX3JlZiRhbXBGaXJzdCIsImFtcEZpcnN0IiwiX3JlZiRoeWJyaWQiLCJoeWJyaWQiLCJfcmVmJGhhc1F1ZXJ5IiwiaGFzUXVlcnkiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-mode.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/head.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/head.js ***!
  \***************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("'use client';\n\"use strict\";\n\nvar _defineProperty = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/defineProperty.js */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/defineProperty.js\");\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n  for (var name in all) Object.defineProperty(target, name, {\n    enumerable: true,\n    get: all[name]\n  });\n}\n_export(exports, {\n  \"default\": function _default() {\n    return _default2;\n  },\n  defaultHead: function defaultHead() {\n    return _defaultHead;\n  }\n});\nvar _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nvar _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nvar _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nvar _react = /*#__PURE__*/_interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nvar _sideeffect = /*#__PURE__*/_interop_require_default._(__webpack_require__(/*! ./side-effect */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/side-effect.js\"));\nvar _ampcontextsharedruntime = __webpack_require__(/*! ./amp-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js\");\nvar _headmanagercontextsharedruntime = __webpack_require__(/*! ./head-manager-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nvar _ampmode = __webpack_require__(/*! ./amp-mode */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-mode.js\");\nvar _warnonce = __webpack_require__(/*! ./utils/warn-once */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nfunction _defaultHead(inAmpMode) {\n  if (inAmpMode === void 0) inAmpMode = false;\n  var head = [/*#__PURE__*/(0, _jsxruntime.jsx)(\"meta\", {\n    charSet: \"utf-8\"\n  }, \"charset\")];\n  if (!inAmpMode) {\n    head.push( /*#__PURE__*/(0, _jsxruntime.jsx)(\"meta\", {\n      name: \"viewport\",\n      content: \"width=device-width\"\n    }, \"viewport\"));\n  }\n  return head;\n}\nfunction onlyReactElement(list, child) {\n  // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n  if (typeof child === 'string' || typeof child === 'number') {\n    return list;\n  }\n  // Adds support for React.Fragment\n  if (child.type === _react[\"default\"].Fragment) {\n    return list.concat(\n    // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n    _react[\"default\"].Children.toArray(child.props.children).reduce(\n    // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n    function (fragmentList, fragmentChild) {\n      if (typeof fragmentChild === 'string' || typeof fragmentChild === 'number') {\n        return fragmentList;\n      }\n      return fragmentList.concat(fragmentChild);\n    }, []));\n  }\n  return list.concat(child);\n}\nvar METATYPES = ['name', 'httpEquiv', 'charSet', 'itemProp'];\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/\nfunction unique() {\n  var keys = new Set();\n  var tags = new Set();\n  var metaTypes = new Set();\n  var metaCategories = {};\n  return function (h) {\n    var isUnique = true;\n    var hasKey = false;\n    if (h.key && typeof h.key !== 'number' && h.key.indexOf('$') > 0) {\n      hasKey = true;\n      var key = h.key.slice(h.key.indexOf('$') + 1);\n      if (keys.has(key)) {\n        isUnique = false;\n      } else {\n        keys.add(key);\n      }\n    }\n    // eslint-disable-next-line default-case\n    switch (h.type) {\n      case 'title':\n      case 'base':\n        if (tags.has(h.type)) {\n          isUnique = false;\n        } else {\n          tags.add(h.type);\n        }\n        break;\n      case 'meta':\n        for (var i = 0, len = METATYPES.length; i < len; i++) {\n          var metatype = METATYPES[i];\n          if (!h.props.hasOwnProperty(metatype)) continue;\n          if (metatype === 'charSet') {\n            if (metaTypes.has(metatype)) {\n              isUnique = false;\n            } else {\n              metaTypes.add(metatype);\n            }\n          } else {\n            var category = h.props[metatype];\n            var categories = metaCategories[metatype] || new Set();\n            if ((metatype !== 'name' || !hasKey) && categories.has(category)) {\n              isUnique = false;\n            } else {\n              categories.add(category);\n              metaCategories[metatype] = categories;\n            }\n          }\n        }\n        break;\n    }\n    return isUnique;\n  };\n}\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */\nfunction reduceComponents(headChildrenElements, props) {\n  var inAmpMode = props.inAmpMode;\n  return headChildrenElements.reduce(onlyReactElement, []).reverse().concat(_defaultHead(inAmpMode).reverse()).filter(unique()).reverse().map(function (c, i) {\n    var key = c.key || i;\n    if (false) { var newProps; }\n    if (true) {\n      // omit JSON-LD structured data snippets from the warning\n      if (c.type === 'script' && c.props['type'] !== 'application/ld+json') {\n        var srcMessage = c.props['src'] ? '<script> tag with src=\"' + c.props['src'] + '\"' : \"inline <script>\";\n        (0, _warnonce.warnOnce)(\"Do not add <script> tags using next/head (see \" + srcMessage + \"). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component\");\n      } else if (c.type === 'link' && c.props['rel'] === 'stylesheet') {\n        (0, _warnonce.warnOnce)('Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"' + c.props['href'] + '\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component');\n      }\n    }\n    return /*#__PURE__*/_react[\"default\"].cloneElement(c, {\n      key: key\n    });\n  });\n}\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */\nfunction Head(param) {\n  var children = param.children;\n  var ampState = (0, _react.useContext)(_ampcontextsharedruntime.AmpStateContext);\n  var headManager = (0, _react.useContext)(_headmanagercontextsharedruntime.HeadManagerContext);\n  return /*#__PURE__*/(0, _jsxruntime.jsx)(_sideeffect[\"default\"], {\n    reduceComponentsToState: reduceComponents,\n    headManager: headManager,\n    inAmpMode: (0, _ampmode.isInAmpMode)(ampState),\n    children: children\n  });\n}\n_c = Head;\nvar _default2 = Head;\nif ((typeof exports[\"default\"] === 'function' || typeof exports[\"default\"] === 'object' && exports[\"default\"] !== null) && typeof exports[\"default\"].__esModule === 'undefined') {\n  Object.defineProperty(exports[\"default\"], '__esModule', {\n    value: true\n  });\n  Object.assign(exports[\"default\"], exports);\n  module.exports = exports[\"default\"];\n}\nvar _c;\n$RefreshReg$(_c, \"Head\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/head.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/side-effect.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/side-effect.js ***!
  \**********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nvar _s = $RefreshSig$();\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n  enumerable: true,\n  get: function get() {\n    return SideEffect;\n  }\n}));\nvar _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nvar isServer = false;\nvar useClientOnlyLayoutEffect = isServer ? function () {} : _react.useLayoutEffect;\nvar useClientOnlyEffect = isServer ? function () {} : _react.useEffect;\nfunction SideEffect(props) {\n  _s();\n  var headManager = props.headManager,\n    reduceComponentsToState = props.reduceComponentsToState;\n  function emitChange() {\n    if (headManager && headManager.mountedInstances) {\n      var headElements = _react.Children.toArray(Array.from(headManager.mountedInstances).filter(Boolean));\n      headManager.updateHead(reduceComponentsToState(headElements, props));\n    }\n  }\n  if (isServer) {\n    var _headManager_mountedInstances;\n    headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.add(props.children);\n    emitChange();\n  }\n  useClientOnlyLayoutEffect(function () {\n    var _headManager_mountedInstances;\n    headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.add(props.children);\n    return function () {\n      var _headManager_mountedInstances;\n      headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances[\"delete\"](props.children);\n    };\n  });\n  // We need to call `updateHead` method whenever the `SideEffect` is trigger in all\n  // life-cycles: mount, update, unmount. However, if there are multiple `SideEffect`s\n  // being rendered, we only trigger the method from the last one.\n  // This is ensured by keeping the last unflushed `updateHead` in the `_pendingUpdate`\n  // singleton in the layout effect pass, and actually trigger it in the effect pass.\n  useClientOnlyLayoutEffect(function () {\n    if (headManager) {\n      headManager._pendingUpdate = emitChange;\n    }\n    return function () {\n      if (headManager) {\n        headManager._pendingUpdate = emitChange;\n      }\n    };\n  });\n  useClientOnlyEffect(function () {\n    if (headManager && headManager._pendingUpdate) {\n      headManager._pendingUpdate();\n      headManager._pendingUpdate = null;\n    }\n    return function () {\n      if (headManager && headManager._pendingUpdate) {\n        headManager._pendingUpdate();\n        headManager._pendingUpdate = null;\n      }\n    };\n  });\n  return null;\n}\n_s(SideEffect, \"gHVkikNHNxjVdD11eJBzaqkCiPY=\", false, function () {\n  return [useClientOnlyLayoutEffect, useClientOnlyLayoutEffect, useClientOnlyEffect];\n});\n_c = SideEffect;\nvar _c;\n$RefreshReg$(_c, \"SideEffect\");\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/side-effect.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils/warn-once.js ***!
  \**************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"warnOnce\", ({\n  enumerable: true,\n  get: function get() {\n    return warnOnce;\n  }\n}));\nvar warnOnce = function warnOnce(_) {};\nif (true) {\n  var warnings = new Set();\n  warnOnce = function warnOnce(msg) {\n    if (!warnings.has(msg)) {\n      console.warn(msg);\n    }\n    warnings.add(msg);\n  };\n}\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi91dGlscy93YXJuLW9uY2UuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBQ2JBLDhDQUE2QztFQUN6Q0csS0FBSyxFQUFFO0FBQ1gsQ0FBQyxFQUFDO0FBQ0ZILDRDQUEyQztFQUN2Q0ksVUFBVSxFQUFFLElBQUk7RUFDaEJDLEdBQUcsRUFBRSxTQUFBQSxJQUFBLEVBQVc7SUFDWixPQUFPQyxRQUFRO0VBQ25CO0FBQ0osQ0FBQyxFQUFDO0FBQ0YsSUFBSUEsUUFBUSxHQUFHLFNBQUFBLFNBQUNDLENBQUMsRUFBRyxDQUFDLENBQUM7QUFDdEIsSUFBSSxNQUF1QztFQUN2QyxJQUFNQyxRQUFRLEdBQUcsSUFBSUMsR0FBRyxDQUFDLENBQUM7RUFDMUJILFFBQVEsR0FBRyxTQUFBQSxTQUFDSSxHQUFHLEVBQUc7SUFDZCxJQUFJLENBQUNGLFFBQVEsQ0FBQ0csR0FBRyxDQUFDRCxHQUFHLENBQUMsRUFBRTtNQUNwQkUsT0FBTyxDQUFDQyxJQUFJLENBQUNILEdBQUcsQ0FBQztJQUNyQjtJQUNBRixRQUFRLENBQUNNLEdBQUcsQ0FBQ0osR0FBRyxDQUFDO0VBQ3JCLENBQUM7QUFDTCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcT3Bvc0kgdjdcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcc2hhcmVkXFxsaWJcXHV0aWxzXFx3YXJuLW9uY2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJ3YXJuT25jZVwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gd2Fybk9uY2U7XG4gICAgfVxufSk7XG5sZXQgd2Fybk9uY2UgPSAoXyk9Pnt9O1xuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgICBjb25zdCB3YXJuaW5ncyA9IG5ldyBTZXQoKTtcbiAgICB3YXJuT25jZSA9IChtc2cpPT57XG4gICAgICAgIGlmICghd2FybmluZ3MuaGFzKG1zZykpIHtcbiAgICAgICAgICAgIGNvbnNvbGUud2Fybihtc2cpO1xuICAgICAgICB9XG4gICAgICAgIHdhcm5pbmdzLmFkZChtc2cpO1xuICAgIH07XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXdhcm4tb25jZS5qcy5tYXAiXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJlbnVtZXJhYmxlIiwiZ2V0Iiwid2Fybk9uY2UiLCJfIiwid2FybmluZ3MiLCJTZXQiLCJtc2ciLCJoYXMiLCJjb25zb2xlIiwid2FybiIsImFkZCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["pages/_app","main"], () => (__webpack_exec__("(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);