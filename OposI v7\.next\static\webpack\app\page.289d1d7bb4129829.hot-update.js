"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/flashcards/FlashcardCollectionList.tsx":
/*!***************************************************************!*\
  !*** ./src/components/flashcards/FlashcardCollectionList.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiInbox_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiInbox!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n\n\n\nconst FlashcardCollectionList = (param)=>{\n    let { colecciones, coleccionSeleccionada, onSeleccionarColeccion, isLoading } = param;\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-40\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (colecciones.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center p-8 border-2 border-dashed border-gray-300 rounded-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiInbox_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiInbox, {\n                    className: \"mx-auto text-6xl text-gray-400 mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500 text-lg\",\n                    children: \"No hay colecciones de flashcards disponibles.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-400 mt-1\",\n                    children: \"Crea una nueva colecci\\xf3n para empezar a estudiar.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6\",\n        children: colecciones.map((coleccion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border rounded-lg p-4 cursor-pointer transition-all flex flex-col justify-between \".concat((coleccionSeleccionada === null || coleccionSeleccionada === void 0 ? void 0 : coleccionSeleccionada.id) === coleccion.id ? 'border-orange-500 bg-orange-50 shadow-lg' : 'border-gray-200 hover:border-orange-300 hover:bg-orange-50/50 hover:shadow-md'),\n                onClick: ()=>onSeleccionarColeccion(coleccion),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-lg mb-1\",\n                                children: coleccion.titulo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, undefined),\n                            coleccion.descripcion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-2 break-words\",\n                                children: coleccion.descripcion\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 mt-2\",\n                                children: [\n                                    \"Flashcards: \",\n                                    typeof coleccion.numero_flashcards === 'number' ? coleccion.numero_flashcards : 'N/A'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-400\",\n                                children: [\n                                    \"Creada: \",\n                                    new Date(coleccion.creado_en).toLocaleDateString()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, coleccion.id, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionList.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n_c = FlashcardCollectionList;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlashcardCollectionList);\nvar _c;\n$RefreshReg$(_c, \"FlashcardCollectionList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/flashcards/FlashcardCollectionList.tsx\n"));

/***/ })

});