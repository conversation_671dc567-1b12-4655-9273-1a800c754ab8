"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/flashcards/FlashcardViewer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/flashcards/FlashcardViewer.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./FlashcardCollectionList */ \"(app-pages-browser)/./src/components/flashcards/FlashcardCollectionList.tsx\");\n/* harmony import */ var _FlashcardStatistics__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FlashcardStatistics */ \"(app-pages-browser)/./src/components/flashcards/FlashcardStatistics.tsx\");\n/* harmony import */ var _FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./FlashcardStudyMode */ \"(app-pages-browser)/./src/components/flashcards/FlashcardStudyMode.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst FlashcardViewer = ()=>{\n    _s();\n    // Estado para las colecciones\n    const [colecciones, setColecciones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coleccionSeleccionada, setColeccionSeleccionada] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para las flashcards\n    const [flashcards, setFlashcards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [mostrarRespuesta, setMostrarRespuesta] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [respondiendo, setRespondiendo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estado para el modo de estudio\n    const [modoEstudio, setModoEstudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estado para estadísticas\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para carga y errores\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Estado para edición y eliminación\n    const [flashcardEditando, setFlashcardEditando] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDeleteConfirm, setShowDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Cargar colecciones al montar el componente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashcardViewer.useEffect\": ()=>{\n            const cargarColecciones = {\n                \"FlashcardViewer.useEffect.cargarColecciones\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n                        setColecciones(data);\n                    } catch (error) {\n                        console.error('Error al cargar colecciones:', error);\n                        setError('No se pudieron cargar las colecciones de flashcards');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"FlashcardViewer.useEffect.cargarColecciones\"];\n            cargarColecciones();\n        }\n    }[\"FlashcardViewer.useEffect\"], []);\n    // Manejar la selección de una colección\n    const handleSeleccionarColeccion = async (coleccion)=>{\n        setIsLoading(true);\n        setError('');\n        setColeccionSeleccionada(coleccion);\n        setActiveIndex(0);\n        setMostrarRespuesta(false);\n        setRespondiendo(false);\n        setModoEstudio(false);\n        try {\n            // Cargar flashcards con su progreso\n            const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccion.id);\n            // Ordenar las flashcards: primero las que deben estudiarse hoy, luego el resto\n            const ordenadas = [\n                ...data\n            ].sort((a, b)=>{\n                if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                return 0;\n            });\n            setFlashcards(ordenadas);\n            // Cargar estadísticas\n            const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccion.id);\n            setEstadisticas(stats);\n        } catch (error) {\n            console.error('Error al cargar flashcards:', error);\n            setError('No se pudieron cargar las flashcards de esta colección');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Iniciar el modo de estudio\n    const iniciarModoEstudio = async ()=>{\n        setIsLoading(true);\n        try {\n            if (coleccionSeleccionada) {\n                // Recargar las flashcards para asegurarnos de tener los datos más recientes\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                // Filtrar solo las flashcards que deben estudiarse hoy\n                const flashcardsParaEstudiar = data.filter((flashcard)=>flashcard.debeEstudiar);\n                // Actualizar estadísticas\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n                // Verificar si el número de flashcards para estudiar coincide con las estadísticas\n                if (flashcardsParaEstudiar.length !== stats.paraHoy) {\n                    console.warn(\"Discrepancia en el conteo: \".concat(flashcardsParaEstudiar.length, \" flashcards filtradas vs \").concat(stats.paraHoy, \" en estad\\xedsticas\"));\n                }\n                // Si no hay flashcards para hoy, mostrar un mensaje\n                if (flashcardsParaEstudiar.length === 0) {\n                    if (data.length === 0) {\n                        alert('No hay flashcards en esta colección.');\n                    } else {\n                        alert('No hay flashcards programadas para estudiar hoy. Vuelve mañana o ajusta el progreso de las tarjetas.');\n                    }\n                    return; // Salir sin iniciar el modo estudio\n                }\n                // Usar solo las flashcards programadas para hoy\n                setFlashcards(flashcardsParaEstudiar);\n                // Iniciar el modo de estudio\n                setModoEstudio(true);\n                setActiveIndex(0);\n                setMostrarRespuesta(false);\n                setRespondiendo(false);\n            }\n        } catch (error) {\n            console.error('Error al iniciar modo de estudio:', error);\n            setError('No se pudo iniciar el modo de estudio');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Manejar la navegación entre flashcards\n    const handleNavigate = (direction)=>{\n        if (direction === 'prev' && activeIndex > 0) {\n            setActiveIndex(activeIndex - 1);\n            setMostrarRespuesta(false);\n        } else if (direction === 'next' && activeIndex < flashcards.length - 1) {\n            setActiveIndex(activeIndex + 1);\n            setMostrarRespuesta(false);\n        }\n    };\n    // Manejar la respuesta a una flashcard\n    const handleRespuesta = async (dificultad)=>{\n        if (!coleccionSeleccionada || flashcards.length === 0) return;\n        const flashcardId = flashcards[activeIndex].id;\n        setRespondiendo(true);\n        try {\n            // Registrar la respuesta\n            await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.registrarRespuestaFlashcard)(flashcardId, dificultad);\n            // Recargar las flashcards y estadísticas si estamos en la última tarjeta\n            if (activeIndex >= flashcards.length - 1 && coleccionSeleccionada) {\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                const flashcardsParaEstudiar = data.filter((flashcard)=>flashcard.debeEstudiar);\n                // Actualizar estadísticas\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n                if (flashcardsParaEstudiar.length > 0) {\n                    setFlashcards(flashcardsParaEstudiar);\n                    setActiveIndex(0);\n                } else {\n                    // Si no hay más flashcards para hoy, mostrar mensaje y salir del modo de estudio\n                    alert('¡Has completado todas las flashcards para hoy! Vuelve mañana para continuar estudiando.');\n                    setModoEstudio(false);\n                    // Ordenar las flashcards: primero las que deben estudiarse (aunque ya no haya ninguna), luego el resto\n                    const ordenadas = [\n                        ...data\n                    ].sort((a, b)=>{\n                        if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                        if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                        return 0;\n                    });\n                    setFlashcards(ordenadas);\n                }\n            } else {\n                // Avanzar a la siguiente flashcard\n                handleNavigate('next');\n            }\n        } catch (error) {\n            console.error('Error al registrar respuesta:', error);\n            setError('No se pudo registrar la respuesta');\n        } finally{\n            setRespondiendo(false);\n            setMostrarRespuesta(false);\n        }\n    };\n    // Salir del modo de estudio\n    const handleSalirModoEstudio = ()=>{\n        setModoEstudio(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 215,\n                columnNumber: 9\n            }, undefined),\n            modoEstudio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                flashcards: flashcards,\n                activeIndex: activeIndex,\n                respondiendo: respondiendo,\n                onRespuesta: handleRespuesta,\n                onNavigate: handleNavigate,\n                onVolver: handleSalirModoEstudio\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 221,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"Mis Flashcards\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        colecciones: colecciones,\n                        coleccionSeleccionada: coleccionSeleccionada,\n                        onSeleccionarColeccion: handleSeleccionarColeccion,\n                        isLoading: isLoading\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, undefined),\n                    coleccionSeleccionada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: coleccionSeleccionada.titulo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardStatistics__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                estadisticas: estadisticas\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: iniciarModoEstudio,\n                                            className: \"bg-orange-500 hover:bg-orange-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                                            children: \"Estudiar (\".concat(estadisticas ? estadisticas.paraHoy : 0, \" para hoy)\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{},\n                                            className: \"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                                            children: \"Ver estad\\xedsticas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 15\n                            }, undefined),\n                            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center items-center h-40\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 17\n                            }, undefined) : flashcards.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"No hay flashcards en esta colecci\\xf3n.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: flashcards.map((flashcard, index)=>{\n                                    var _flashcard_progreso, _flashcard_progreso1;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4 \".concat(flashcard.debeEstudiar ? 'border-orange-300 bg-orange-50' : 'border-gray-200'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            \"Tarjeta \",\n                                                            index + 1\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    ((_flashcard_progreso = flashcard.progreso) === null || _flashcard_progreso === void 0 ? void 0 : _flashcard_progreso.estado) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs \".concat(flashcard.progreso.estado === 'nuevo' ? 'bg-blue-100 text-blue-800' : flashcard.progreso.estado === 'aprendiendo' ? 'bg-yellow-100 text-yellow-800' : flashcard.progreso.estado === 'repasando' ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'),\n                                                        children: flashcard.progreso.estado\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    !((_flashcard_progreso1 = flashcard.progreso) === null || _flashcard_progreso1 === void 0 ? void 0 : _flashcard_progreso1.estado) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs\",\n                                                        children: \"nuevo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-2\",\n                                                children: flashcard.pregunta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-700 line-clamp-2\",\n                                                children: flashcard.respuesta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, flashcard.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 21\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 230,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n        lineNumber: 213,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlashcardViewer, \"Xzhcu/FYdriaw0rdUfnEbNW9n1c=\");\n_c = FlashcardViewer;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlashcardViewer);\nvar _c;\n$RefreshReg$(_c, \"FlashcardViewer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/flashcards/FlashcardViewer.tsx\n"));

/***/ })

});