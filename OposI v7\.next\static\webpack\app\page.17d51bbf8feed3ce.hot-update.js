"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase/testsService.ts":
/*!******************************************!*\
  !*** ./src/lib/supabase/testsService.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   crearPreguntaTest: () => (/* binding */ crearPreguntaTest),\n/* harmony export */   crearTest: () => (/* binding */ crearTest),\n/* harmony export */   guardarPreguntasTest: () => (/* binding */ guardarPreguntasTest),\n/* harmony export */   obtenerEstadisticasGeneralesTests: () => (/* binding */ obtenerEstadisticasGeneralesTests),\n/* harmony export */   obtenerEstadisticasTest: () => (/* binding */ obtenerEstadisticasTest),\n/* harmony export */   obtenerPreguntasPorTestId: () => (/* binding */ obtenerPreguntasPorTestId),\n/* harmony export */   obtenerPreguntasTestCount: () => (/* binding */ obtenerPreguntasTestCount),\n/* harmony export */   obtenerTestPorId: () => (/* binding */ obtenerTestPorId),\n/* harmony export */   obtenerTests: () => (/* binding */ obtenerTests),\n/* harmony export */   registrarRespuestaTest: () => (/* binding */ registrarRespuestaTest)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./authService */ \"(app-pages-browser)/./src/lib/supabase/authService.ts\");\n\n\n/**\n * Crea un nuevo test\n */ async function crearTest(titulo, descripcion, documentosIds) {\n    try {\n        var _data_, _data_1;\n        console.log('📝 Creando nuevo test:', titulo);\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('❌ No hay usuario autenticado para crear test');\n            return null;\n        }\n        console.log('👤 Usuario autenticado:', user.id);\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('tests').insert([\n            {\n                titulo,\n                descripcion,\n                documentos_ids: documentosIds,\n                user_id: user.id\n            }\n        ]).select();\n        if (error) {\n            console.error('❌ Error al crear test:', error);\n            return null;\n        }\n        console.log('✅ Test creado exitosamente:', data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id);\n        return (data === null || data === void 0 ? void 0 : (_data_1 = data[0]) === null || _data_1 === void 0 ? void 0 : _data_1.id) || null;\n    } catch (error) {\n        console.error('💥 Error inesperado al crear test:', error);\n        return null;\n    }\n}\n/**\n * Obtiene todos los tests\n */ async function obtenerTests() {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('tests').select('*').order('creado_en', {\n        ascending: false\n    });\n    if (error) {\n        console.error('Error al obtener tests:', error);\n        return [];\n    }\n    return data || [];\n}\n/**\n * Obtiene un test por su ID\n */ async function obtenerTestPorId(id) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('tests').select('*').eq('id', id).single();\n    if (error) {\n        console.error('Error al obtener test:', error);\n        return null;\n    }\n    return data;\n}\n/**\n * Crea una nueva pregunta para un test\n */ async function crearPreguntaTest(testId, pregunta, opcionA, opcionB, opcionC, opcionD, respuestaCorrecta) {\n    var _data_;\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('preguntas_test').insert([\n        {\n            test_id: testId,\n            pregunta,\n            opcion_a: opcionA,\n            opcion_b: opcionB,\n            opcion_c: opcionC,\n            opcion_d: opcionD,\n            respuesta_correcta: respuestaCorrecta\n        }\n    ]).select();\n    if (error) {\n        console.error('Error al crear pregunta de test:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n}\n/**\n * Obtiene todas las preguntas de un test\n */ async function obtenerPreguntasPorTestId(testId) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('preguntas_test').select('*').eq('test_id', testId);\n    if (error) {\n        console.error('Error al obtener preguntas de test:', error);\n        return [];\n    }\n    return data || [];\n}\n/**\n * Obtiene el número de preguntas de un test\n */ async function obtenerPreguntasTestCount(testId) {\n    const { count, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('preguntas_test').select('*', {\n        count: 'exact',\n        head: true\n    }).eq('test_id', testId);\n    if (error) {\n        console.error('Error al obtener conteo de preguntas:', error);\n        return 0;\n    }\n    return count || 0;\n}\n/**\n * Guarda múltiples preguntas de test\n */ async function guardarPreguntasTest(preguntas) {\n    const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('preguntas_test').insert(preguntas);\n    if (error) {\n        console.error('Error al guardar preguntas de test:', error);\n        return false;\n    }\n    return true;\n}\n/**\n * Registra la respuesta de un usuario a una pregunta de test\n */ async function registrarRespuestaTest(testId, preguntaId, respuestaUsuario, esCorrecta) {\n    const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('estadisticas_test').insert([\n        {\n            test_id: testId,\n            pregunta_id: preguntaId,\n            respuesta_usuario: respuestaUsuario,\n            es_correcta: esCorrecta,\n            fecha_respuesta: new Date().toISOString()\n        }\n    ]);\n    if (error) {\n        console.error('Error al registrar respuesta de test:', error);\n        return false;\n    }\n    return true;\n}\n/**\n * Obtiene estadísticas generales de todos los tests\n */ async function obtenerEstadisticasGeneralesTests() {\n    // Obtener todas las respuestas\n    const { data: respuestas, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('estadisticas_test').select('*');\n    if (error) {\n        console.error('Error al obtener estadísticas de tests:', error);\n        return {\n            totalTests: 0,\n            totalPreguntas: 0,\n            totalRespuestasCorrectas: 0,\n            totalRespuestasIncorrectas: 0,\n            porcentajeAcierto: 0\n        };\n    }\n    // Obtener tests únicos respondidos\n    const testsUnicos = new Set((respuestas === null || respuestas === void 0 ? void 0 : respuestas.map((r)=>r.test_id)) || []);\n    // Obtener preguntas únicas respondidas\n    const preguntasUnicas = new Set((respuestas === null || respuestas === void 0 ? void 0 : respuestas.map((r)=>r.pregunta_id)) || []);\n    // Contar respuestas correctas e incorrectas\n    const correctas = (respuestas === null || respuestas === void 0 ? void 0 : respuestas.filter((r)=>r.es_correcta).length) || 0;\n    const incorrectas = ((respuestas === null || respuestas === void 0 ? void 0 : respuestas.length) || 0) - correctas;\n    // Calcular porcentaje de acierto\n    const porcentaje = respuestas && respuestas.length > 0 ? Math.round(correctas / respuestas.length * 100) : 0;\n    return {\n        totalTests: testsUnicos.size,\n        totalPreguntas: preguntasUnicas.size,\n        totalRespuestasCorrectas: correctas,\n        totalRespuestasIncorrectas: incorrectas,\n        porcentajeAcierto: porcentaje\n    };\n}\n/**\n * Obtiene estadísticas detalladas de un test específico\n */ async function obtenerEstadisticasTest(testId) {\n    // Obtener todas las respuestas del test\n    const { data: respuestas, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('estadisticas_test').select('*').eq('test_id', testId);\n    if (error) {\n        console.error('Error al obtener estadísticas del test:', error);\n        return {\n            totalPreguntas: 0,\n            totalCorrectas: 0,\n            totalIncorrectas: 0,\n            porcentajeAcierto: 0,\n            fechasRealizacion: [],\n            preguntasMasFalladas: []\n        };\n    }\n    // Obtener preguntas del test\n    const { data: preguntas } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('preguntas_test').select('*').eq('test_id', testId);\n    // Contar respuestas correctas e incorrectas\n    const correctas = (respuestas === null || respuestas === void 0 ? void 0 : respuestas.filter((r)=>r.es_correcta).length) || 0;\n    const incorrectas = ((respuestas === null || respuestas === void 0 ? void 0 : respuestas.length) || 0) - correctas;\n    // Calcular porcentaje de acierto\n    const porcentaje = respuestas && respuestas.length > 0 ? Math.round(correctas / respuestas.length * 100) : 0;\n    // Obtener fechas únicas de realización\n    const fechasSet = new Set();\n    respuestas === null || respuestas === void 0 ? void 0 : respuestas.forEach((r)=>{\n        const fecha = new Date(r.fecha_respuesta);\n        fechasSet.add(\"\".concat(fecha.getDate(), \"/\").concat(fecha.getMonth() + 1, \"/\").concat(fecha.getFullYear()));\n    });\n    const fechasUnicas = Array.from(fechasSet);\n    // Calcular preguntas más falladas\n    const fallosPorPregunta = new Map();\n    respuestas === null || respuestas === void 0 ? void 0 : respuestas.forEach((respuesta)=>{\n        const actual = fallosPorPregunta.get(respuesta.pregunta_id) || {\n            fallos: 0,\n            aciertos: 0\n        };\n        if (respuesta.es_correcta) {\n            actual.aciertos++;\n        } else {\n            actual.fallos++;\n        }\n        fallosPorPregunta.set(respuesta.pregunta_id, actual);\n    });\n    // Convertir a array y ordenar por fallos\n    const preguntasFalladas = Array.from(fallosPorPregunta.entries()).map((param)=>{\n        let [id, stats] = param;\n        var _preguntas_find;\n        return {\n            preguntaId: id,\n            totalFallos: stats.fallos,\n            totalAciertos: stats.aciertos,\n            // Encontrar la pregunta correspondiente\n            pregunta: (preguntas === null || preguntas === void 0 ? void 0 : (_preguntas_find = preguntas.find((p)=>p.id === id)) === null || _preguntas_find === void 0 ? void 0 : _preguntas_find.pregunta) || 'Desconocida'\n        };\n    }).sort((a, b)=>b.totalFallos - a.totalFallos).slice(0, 5); // Tomar las 5 más falladas\n    return {\n        totalPreguntas: (preguntas === null || preguntas === void 0 ? void 0 : preguntas.length) || 0,\n        totalCorrectas: correctas,\n        totalIncorrectas: incorrectas,\n        porcentajeAcierto: porcentaje,\n        fechasRealizacion: fechasUnicas,\n        preguntasMasFalladas: preguntasFalladas\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/testsService.ts\n"));

/***/ })

});