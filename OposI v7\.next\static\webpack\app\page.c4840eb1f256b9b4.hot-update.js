"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/QuestionForm.tsx":
/*!*****************************************!*\
  !*** ./src/components/QuestionForm.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuestionForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _ConversationHistory__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ConversationHistory */ \"(app-pages-browser)/./src/components/ConversationHistory.tsx\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _lib_formSchemas__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/formSchemas */ \"(app-pages-browser)/./src/lib/formSchemas.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction QuestionForm(param) {\n    let { documentosSeleccionados } = param;\n    _s();\n    const [mensajes, setMensajes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [conversacionActualId, setConversacionActualId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mostrarHistorial, setMostrarHistorial] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [guardandoConversacion, setGuardandoConversacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cargandoConversacionActiva, setCargandoConversacionActiva] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { register, handleSubmit: handleSubmitForm, formState: { errors, isValid }, reset, setValue, watch } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(_lib_formSchemas__WEBPACK_IMPORTED_MODULE_5__.preguntaFormSchema),\n        defaultValues: {\n            pregunta: '',\n            documentos: documentosSeleccionados\n        }\n    });\n    // Observar cambios en el formulario para debugging\n    const watchedValues = watch();\n    // Log de debugging para el estado del formulario\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionForm.useEffect\": ()=>{\n            console.log('📝 Estado del formulario:', {\n                values: watchedValues,\n                errors,\n                isValid,\n                documentosSeleccionados: documentosSeleccionados.length\n            });\n        }\n    }[\"QuestionForm.useEffect\"], [\n        watchedValues,\n        errors,\n        isValid,\n        documentosSeleccionados\n    ]);\n    // Sincronizar documentos seleccionados con el formulario, asegurando tipos correctos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionForm.useEffect\": ()=>{\n            const documentosValidados = documentosSeleccionados.map({\n                \"QuestionForm.useEffect.documentosValidados\": (doc)=>({\n                        ...doc,\n                        numero_tema: doc.numero_tema !== undefined && doc.numero_tema !== null ? Number(doc.numero_tema) : undefined\n                    })\n            }[\"QuestionForm.useEffect.documentosValidados\"]);\n            setValue('documentos', documentosValidados);\n        }\n    }[\"QuestionForm.useEffect\"], [\n        documentosSeleccionados,\n        setValue\n    ]);\n    // Efecto para cargar la conversación activa al iniciar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionForm.useEffect\": ()=>{\n            const cargarConversacionActiva = {\n                \"QuestionForm.useEffect.cargarConversacionActiva\": async ()=>{\n                    setCargandoConversacionActiva(true);\n                    try {\n                        const conversacionActiva = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerConversacionActiva)();\n                        if (conversacionActiva) {\n                            console.log('Conversación activa encontrada:', conversacionActiva.id);\n                            setConversacionActualId(conversacionActiva.id);\n                            await cargarConversacion(conversacionActiva.id);\n                        } else {\n                            console.log('No hay conversación activa - esto es normal para usuarios nuevos');\n                            setMensajes([]);\n                            setConversacionActualId(null);\n                        }\n                    } catch (error) {\n                        console.warn('No se pudo cargar la conversación activa (esto es normal para usuarios nuevos):', error);\n                        // No mostrar error al usuario, simplemente inicializar sin conversación\n                        setMensajes([]);\n                        setConversacionActualId(null);\n                    } finally{\n                        setCargandoConversacionActiva(false);\n                    }\n                }\n            }[\"QuestionForm.useEffect.cargarConversacionActiva\"];\n            cargarConversacionActiva();\n        }\n    }[\"QuestionForm.useEffect\"], []);\n    // Efecto para hacer scroll al último mensaje cuando se añade uno nuevo\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionForm.useEffect\": ()=>{\n            if (chatContainerRef.current) {\n                chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n            }\n        }\n    }[\"QuestionForm.useEffect\"], [\n        mensajes\n    ]);\n    // Función para cargar una conversación desde Supabase\n    const cargarConversacion = async (conversacionId)=>{\n        try {\n            setIsLoading(true);\n            // Activar la conversación seleccionada\n            await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.activarConversacion)(conversacionId);\n            // Obtener los mensajes de la conversación\n            const mensajesDB = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerMensajesPorConversacionId)(conversacionId);\n            // Convertir los mensajes de la base de datos al formato local\n            const mensajesFormateados = mensajesDB.map((msg)=>({\n                    id: msg.id,\n                    tipo: msg.tipo,\n                    contenido: msg.contenido,\n                    timestamp: new Date(msg.timestamp)\n                }));\n            // Actualizar el estado\n            setMensajes(mensajesFormateados);\n            setConversacionActualId(conversacionId);\n            setError('');\n            console.log(\"Conversaci\\xf3n \".concat(conversacionId, \" cargada y activada\"));\n        } catch (error) {\n            console.error('Error al cargar la conversación:', error);\n            setError('No se pudo cargar la conversación');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Función para guardar un mensaje en Supabase\n    const guardarMensajeEnDB = async (mensaje)=>{\n        try {\n            setGuardandoConversacion(true);\n            // Si no hay una conversación actual, crear una nueva\n            if (!conversacionActualId) {\n                // Solo crear una nueva conversación si es el primer mensaje del usuario\n                if (mensaje.tipo === 'usuario') {\n                    // Crear un título basado en la primera pregunta\n                    const titulo = \"Conversaci\\xf3n: \".concat(mensaje.contenido.substring(0, 50)).concat(mensaje.contenido.length > 50 ? '...' : '');\n                    // Crear una nueva conversación y marcarla como activa\n                    const nuevoId = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.crearConversacion)(titulo, true);\n                    if (!nuevoId) {\n                        throw new Error('No se pudo crear la conversación');\n                    }\n                    console.log(\"Nueva conversaci\\xf3n creada y activada: \".concat(nuevoId));\n                    // Guardar el ID de la conversación para futuros mensajes\n                    setConversacionActualId(nuevoId);\n                    // Guardar el mensaje en la nueva conversación\n                    await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.guardarMensaje)({\n                        conversacion_id: nuevoId,\n                        tipo: mensaje.tipo,\n                        contenido: mensaje.contenido\n                    });\n                } else {\n                    // Si es un mensaje de la IA pero no hay conversación actual,\n                    // algo salió mal. Intentar recuperar creando una nueva conversación.\n                    console.warn('No hay conversación actual para guardar el mensaje de la IA. Creando una nueva.');\n                    const titulo = 'Nueva conversación';\n                    const nuevoId = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.crearConversacion)(titulo, true);\n                    if (!nuevoId) {\n                        throw new Error('No se pudo crear la conversación');\n                    }\n                    console.log(\"Nueva conversaci\\xf3n de recuperaci\\xf3n creada: \".concat(nuevoId));\n                    setConversacionActualId(nuevoId);\n                    // Guardar el mensaje en la nueva conversación\n                    await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.guardarMensaje)({\n                        conversacion_id: nuevoId,\n                        tipo: mensaje.tipo,\n                        contenido: mensaje.contenido\n                    });\n                }\n            } else {\n                // Verificar que la conversación actual sigue siendo la activa\n                const conversacionActiva = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerConversacionActiva)();\n                if (!conversacionActiva || conversacionActiva.id !== conversacionActualId) {\n                    // Si la conversación actual no es la activa, activarla\n                    console.log(\"Reactivando conversaci\\xf3n: \".concat(conversacionActualId));\n                    await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.activarConversacion)(conversacionActualId);\n                }\n                // Guardar el mensaje en la conversación existente\n                await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.guardarMensaje)({\n                    conversacion_id: conversacionActualId,\n                    tipo: mensaje.tipo,\n                    contenido: mensaje.contenido\n                });\n            }\n        } catch (error) {\n            console.error('Error al guardar el mensaje:', error);\n        // No mostramos error al usuario para no interrumpir la experiencia\n        } finally{\n            setGuardandoConversacion(false);\n        }\n    };\n    // Función para iniciar una nueva conversación\n    const iniciarNuevaConversacion = async ()=>{\n        try {\n            setIsLoading(true);\n            // Desactivar todas las conversaciones en la base de datos\n            await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.desactivarTodasLasConversaciones)();\n            // Limpiar los mensajes actuales\n            setMensajes([]);\n            // Establecer el ID de conversación a null para que se cree una nueva en el próximo mensaje\n            setConversacionActualId(null);\n            setError('');\n            console.log('Nueva conversación iniciada. El próximo mensaje creará una nueva conversación en la base de datos.');\n        } catch (error) {\n            console.error('Error al iniciar nueva conversación:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Cambia handleSubmit para usar React Hook Form\n    const onSubmit = async (data)=>{\n        console.log('🚀 Formulario enviado con datos:', data);\n        console.log('📄 Documentos seleccionados:', documentosSeleccionados);\n        setIsLoading(true);\n        setError('');\n        // Añadir la pregunta del usuario al historial\n        const preguntaUsuario = {\n            tipo: 'usuario',\n            contenido: data.pregunta,\n            timestamp: new Date()\n        };\n        setMensajes((prevMensajes)=>[\n                ...prevMensajes,\n                preguntaUsuario\n            ]);\n        setIsLoading(true);\n        setError('');\n        // Limpiar el campo de pregunta después de enviarla\n        reset({\n            pregunta: '',\n            documentos: documentosSeleccionados\n        });\n        try {\n            // Guardar la pregunta del usuario en Supabase\n            await guardarMensajeEnDB(preguntaUsuario);\n            // Pasar los documentos completos a la función obtenerRespuestaIA\n            // No solo el contenido, sino también el título, categoría y número de tema\n            // Obtener respuesta de la IA\n            const response = await fetch('/api/gemini', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    pregunta: preguntaUsuario.contenido,\n                    documentos: data.documentos\n                })\n            });\n            const respuestaIA = await response.json();\n            let respuestaTexto = '';\n            if (respuestaIA.result) {\n                respuestaTexto = typeof respuestaIA.result === 'string' ? respuestaIA.result : JSON.stringify(respuestaIA.result);\n            } else if (respuestaIA.error) {\n                respuestaTexto = typeof respuestaIA.error === 'string' ? respuestaIA.error : JSON.stringify(respuestaIA.error);\n            } else {\n                respuestaTexto = 'Error desconocido al obtener respuesta de la IA.';\n            }\n            // Añadir la respuesta de la IA al historial\n            const mensajeIA = {\n                tipo: 'ia',\n                contenido: respuestaTexto,\n                timestamp: new Date()\n            };\n            setMensajes((prevMensajes)=>[\n                    ...prevMensajes,\n                    mensajeIA\n                ]);\n            // Guardar la respuesta de la IA en Supabase\n            await guardarMensajeEnDB(mensajeIA);\n            // Si es la primera pregunta, actualizar el título de la conversación con un título más descriptivo\n            if (mensajes.length === 0 && conversacionActualId) {\n                const tituloMejorado = \"Conversaci\\xf3n: \".concat(data.pregunta.substring(0, 50)).concat(data.pregunta.length > 50 ? '...' : '');\n                await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.actualizarConversacion)(conversacionActualId, tituloMejorado);\n            }\n        } catch (error) {\n            console.error('Error al obtener respuesta:', error);\n            // Determinar el tipo de error y mostrar un mensaje más específico\n            let mensajeError = 'Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, inténtalo de nuevo.';\n            if (error instanceof Error) {\n                if (error.message.includes('API key')) {\n                    mensajeError = 'Error de configuración: La clave de API de Gemini no está configurada correctamente.';\n                } else if (error.message.includes('network') || error.message.includes('fetch')) {\n                    mensajeError = 'Error de conexión: No se pudo conectar con el servicio de IA. Verifica tu conexión a internet.';\n                } else if (error.message.includes('quota') || error.message.includes('limit')) {\n                    mensajeError = 'Se ha alcanzado el límite de uso del servicio de IA. Inténtalo más tarde.';\n                } else {\n                    mensajeError = \"Error: \".concat(error.message);\n                }\n            }\n            setError(mensajeError);\n            // Añadir mensaje de error como respuesta de la IA\n            const mensajeErrorIA = {\n                tipo: 'ia',\n                contenido: mensajeError,\n                timestamp: new Date()\n            };\n            setMensajes((prevMensajes)=>[\n                    ...prevMensajes,\n                    mensajeErrorIA\n                ]);\n            // Intentar guardar el mensaje de error en Supabase (sin fallar si no se puede)\n            try {\n                await guardarMensajeEnDB(mensajeErrorIA);\n            } catch (dbError) {\n                console.error('Error al guardar mensaje de error en DB:', dbError);\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Formatear la fecha para mostrarla en el chat\n    const formatearFecha = (fecha)=>{\n        return fecha.toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-6 flex flex-col h-[600px]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>setMostrarHistorial(!mostrarHistorial),\n                        className: \"bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded inline-flex items-center\",\n                        children: mostrarHistorial ? 'Ocultar historial' : 'Ver historial de conversaciones'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: iniciarNuevaConversacion,\n                        className: \"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded inline-flex items-center\",\n                        disabled: isLoading,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"h-5 w-5 mr-2\",\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, this),\n                            \"Nueva conversaci\\xf3n\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                lineNumber: 362,\n                columnNumber: 7\n            }, this),\n            mostrarHistorial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConversationHistory__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onSelectConversation: cargarConversacion,\n                conversacionActualId: conversacionActualId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                lineNumber: 386,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatContainerRef,\n                className: \"flex-grow overflow-y-auto mb-4 p-4 border rounded-lg bg-gray-50\",\n                style: {\n                    height: 'calc(100% - 180px)'\n                },\n                children: mensajes.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full text-gray-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Selecciona documentos y haz una pregunta para comenzar la conversaci\\xf3n.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                    lineNumber: 399,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        mensajes.map((mensaje, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex \".concat(mensaje.tipo === 'usuario' ? 'justify-end' : 'justify-start'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-[80%] p-3 rounded-lg \".concat(mensaje.tipo === 'usuario' ? 'bg-blue-500 text-white rounded-br-none' : 'bg-white border border-gray-300 rounded-bl-none'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"whitespace-pre-wrap\",\n                                            children: mensaje.contenido\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs mt-1 text-right \".concat(mensaje.tipo === 'usuario' ? 'text-blue-100' : 'text-gray-500'),\n                                            children: formatearFecha(mensaje.timestamp)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 17\n                                }, this)\n                            }, mensaje.id || index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 15\n                            }, this)),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-3 rounded-lg border border-gray-300 rounded-bl-none\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-bounce h-2 w-2 bg-gray-500 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-bounce h-2 w-2 bg-gray-500 rounded-full\",\n                                            style: {\n                                                animationDelay: '0.2s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-bounce h-2 w-2 bg-gray-500 rounded-full\",\n                                            style: {\n                                                animationDelay: '0.4s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 15\n                        }, this),\n                        guardandoConversacion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 text-center py-1\",\n                            children: \"Guardando conversaci\\xf3n...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                            lineNumber: 441,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                    lineNumber: 403,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                lineNumber: 393,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmitForm(onSubmit),\n                className: \"mt-auto\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-500 text-sm mb-2\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-600 mb-2\",\n                        children: documentosSeleccionados.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-600\",\n                            children: [\n                                \"✓ \",\n                                documentosSeleccionados.length,\n                                \" documento\",\n                                documentosSeleccionados.length !== 1 ? 's' : '',\n                                \" seleccionado\",\n                                documentosSeleccionados.length !== 1 ? 's' : ''\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-600\",\n                            children: \"⚠ No hay documentos seleccionados. Selecciona al menos uno para hacer preguntas.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-grow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"pregunta\",\n                                        className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                        rows: 2,\n                                        ...register('pregunta'),\n                                        placeholder: \"Escribe tu pregunta sobre los documentos seleccionados...\",\n                                        disabled: isLoading,\n                                        onKeyDown: (e)=>{\n                                            if (e.key === 'Enter' && !e.shiftKey) {\n                                                e.preventDefault();\n                                                handleSubmitForm(onSubmit)(); // Ejecutar la función devuelta\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.pregunta && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-500 text-xs mt-1\",\n                                        children: errors.pregunta.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: \"Presiona Enter para enviar, Shift+Enter para nueva l\\xednea\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-full h-10 w-10 flex items-center justify-center focus:outline-none focus:shadow-outline disabled:opacity-50 disabled:cursor-not-allowed\",\n                                disabled: isLoading || documentosSeleccionados.length === 0,\n                                title: documentosSeleccionados.length === 0 ? 'Selecciona al menos un documento para hacer una pregunta' : 'Enviar pregunta',\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin h-5 w-5 text-white\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 008-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-5 w-5 text-white\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n                lineNumber: 450,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\QuestionForm.tsx\",\n        lineNumber: 360,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionForm, \"I42aJdgBQNzGUtf/645NjTDxehY=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm\n    ];\n});\n_c = QuestionForm;\nvar _c;\n$RefreshReg$(_c, \"QuestionForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/QuestionForm.tsx\n"));

/***/ })

});