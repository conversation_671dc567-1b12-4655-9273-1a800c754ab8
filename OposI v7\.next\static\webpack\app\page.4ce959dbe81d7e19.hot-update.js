"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/flashcards/FlashcardViewer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/flashcards/FlashcardViewer.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _barrel_optimize_names_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FiEdit2,FiRefreshCw,FiTrash2!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./FlashcardCollectionList */ \"(app-pages-browser)/./src/components/flashcards/FlashcardCollectionList.tsx\");\n/* harmony import */ var _FlashcardStatistics__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FlashcardStatistics */ \"(app-pages-browser)/./src/components/flashcards/FlashcardStatistics.tsx\");\n/* harmony import */ var _FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./FlashcardStudyMode */ \"(app-pages-browser)/./src/components/flashcards/FlashcardStudyMode.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst FlashcardViewer = ()=>{\n    _s();\n    // Estado para las colecciones\n    const [colecciones, setColecciones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coleccionSeleccionada, setColeccionSeleccionada] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para las flashcards\n    const [flashcards, setFlashcards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [mostrarRespuesta, setMostrarRespuesta] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [respondiendo, setRespondiendo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estado para el modo de estudio\n    const [modoEstudio, setModoEstudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estado para estadísticas\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para carga y errores\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Estado para edición y eliminación\n    const [flashcardEditando, setFlashcardEditando] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDeleteConfirm, setShowDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Cargar colecciones al montar el componente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashcardViewer.useEffect\": ()=>{\n            const cargarColecciones = {\n                \"FlashcardViewer.useEffect.cargarColecciones\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n                        setColecciones(data);\n                    } catch (error) {\n                        console.error('Error al cargar colecciones:', error);\n                        setError('No se pudieron cargar las colecciones de flashcards');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"FlashcardViewer.useEffect.cargarColecciones\"];\n            cargarColecciones();\n        }\n    }[\"FlashcardViewer.useEffect\"], []);\n    // Manejar la selección de una colección\n    const handleSeleccionarColeccion = async (coleccion)=>{\n        setIsLoading(true);\n        setError('');\n        setColeccionSeleccionada(coleccion);\n        setActiveIndex(0);\n        setMostrarRespuesta(false);\n        setRespondiendo(false);\n        setModoEstudio(false);\n        try {\n            // Cargar flashcards con su progreso\n            const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccion.id);\n            // Ordenar las flashcards: primero las que deben estudiarse hoy, luego el resto\n            const ordenadas = [\n                ...data\n            ].sort((a, b)=>{\n                if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                return 0;\n            });\n            setFlashcards(ordenadas);\n            // Cargar estadísticas\n            const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccion.id);\n            setEstadisticas(stats);\n        } catch (error) {\n            console.error('Error al cargar flashcards:', error);\n            setError('No se pudieron cargar las flashcards de esta colección');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Iniciar el modo de estudio\n    const iniciarModoEstudio = async ()=>{\n        setIsLoading(true);\n        try {\n            if (coleccionSeleccionada) {\n                // Recargar las flashcards para asegurarnos de tener los datos más recientes\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                // Filtrar solo las flashcards que deben estudiarse hoy\n                const flashcardsParaEstudiar = data.filter((flashcard)=>flashcard.debeEstudiar);\n                // Actualizar estadísticas\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n                // Verificar si el número de flashcards para estudiar coincide con las estadísticas\n                if (flashcardsParaEstudiar.length !== stats.paraHoy) {\n                    console.warn(\"Discrepancia en el conteo: \".concat(flashcardsParaEstudiar.length, \" flashcards filtradas vs \").concat(stats.paraHoy, \" en estad\\xedsticas\"));\n                }\n                // Si no hay flashcards para hoy, mostrar un mensaje\n                if (flashcardsParaEstudiar.length === 0) {\n                    if (data.length === 0) {\n                        alert('No hay flashcards en esta colección.');\n                    } else {\n                        alert('No hay flashcards programadas para estudiar hoy. Vuelve mañana o ajusta el progreso de las tarjetas.');\n                    }\n                    return; // Salir sin iniciar el modo estudio\n                }\n                // Usar solo las flashcards programadas para hoy\n                setFlashcards(flashcardsParaEstudiar);\n                // Iniciar el modo de estudio\n                setModoEstudio(true);\n                setActiveIndex(0);\n                setMostrarRespuesta(false);\n                setRespondiendo(false);\n            }\n        } catch (error) {\n            console.error('Error al iniciar modo de estudio:', error);\n            setError('No se pudo iniciar el modo de estudio');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Manejar la navegación entre flashcards\n    const handleNavigate = (direction)=>{\n        if (direction === 'prev' && activeIndex > 0) {\n            setActiveIndex(activeIndex - 1);\n            setMostrarRespuesta(false);\n        } else if (direction === 'next' && activeIndex < flashcards.length - 1) {\n            setActiveIndex(activeIndex + 1);\n            setMostrarRespuesta(false);\n        }\n    };\n    // Manejar la respuesta a una flashcard\n    const handleRespuesta = async (dificultad)=>{\n        if (!coleccionSeleccionada || flashcards.length === 0) return;\n        const flashcardId = flashcards[activeIndex].id;\n        setRespondiendo(true);\n        try {\n            // Registrar la respuesta\n            await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.registrarRespuestaFlashcard)(flashcardId, dificultad);\n            // Recargar las flashcards y estadísticas si estamos en la última tarjeta\n            if (activeIndex >= flashcards.length - 1 && coleccionSeleccionada) {\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                const flashcardsParaEstudiar = data.filter((flashcard)=>flashcard.debeEstudiar);\n                // Actualizar estadísticas\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n                if (flashcardsParaEstudiar.length > 0) {\n                    setFlashcards(flashcardsParaEstudiar);\n                    setActiveIndex(0);\n                } else {\n                    // Si no hay más flashcards para hoy, mostrar mensaje y salir del modo de estudio\n                    alert('¡Has completado todas las flashcards para hoy! Vuelve mañana para continuar estudiando.');\n                    setModoEstudio(false);\n                    // Ordenar las flashcards: primero las que deben estudiarse (aunque ya no haya ninguna), luego el resto\n                    const ordenadas = [\n                        ...data\n                    ].sort((a, b)=>{\n                        if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                        if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                        return 0;\n                    });\n                    setFlashcards(ordenadas);\n                }\n            } else {\n                // Avanzar a la siguiente flashcard\n                handleNavigate('next');\n            }\n        } catch (error) {\n            console.error('Error al registrar respuesta:', error);\n            setError('No se pudo registrar la respuesta');\n        } finally{\n            setRespondiendo(false);\n            setMostrarRespuesta(false);\n        }\n    };\n    // Salir del modo de estudio\n    const handleSalirModoEstudio = ()=>{\n        setModoEstudio(false);\n    };\n    // Manejar la edición de una flashcard\n    const handleEditarFlashcard = (flashcard)=>{\n        setFlashcardEditando(flashcard);\n        setShowEditModal(true);\n    };\n    // Manejar el guardado de una flashcard editada\n    const handleGuardarFlashcard = (flashcardActualizada)=>{\n        // Actualizar la flashcard en la lista local\n        setFlashcards((prev)=>prev.map((fc)=>fc.id === flashcardActualizada.id ? flashcardActualizada : fc));\n    };\n    // Manejar la eliminación de una flashcard\n    const handleEliminarFlashcard = async (flashcardId)=>{\n        setDeletingId(flashcardId);\n        let loadingToastId;\n        try {\n            loadingToastId = react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].loading('Eliminando flashcard...');\n            const success = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.eliminarFlashcard)(flashcardId);\n            if (success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].success('Flashcard eliminada exitosamente', {\n                    id: loadingToastId\n                });\n                // Actualizar la lista local\n                setFlashcards((prev)=>prev.filter((fc)=>fc.id !== flashcardId));\n                // Recargar estadísticas\n                if (coleccionSeleccionada) {\n                    const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                    setEstadisticas(stats);\n                }\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('Error al eliminar la flashcard', {\n                    id: loadingToastId\n                });\n            }\n        } catch (error) {\n            console.error('Error al eliminar flashcard:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('Error al eliminar la flashcard', {\n                id: loadingToastId\n            });\n        } finally{\n            setDeletingId(null);\n            setShowDeleteConfirm(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 264,\n                columnNumber: 9\n            }, undefined),\n            modoEstudio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                flashcards: flashcards,\n                activeIndex: activeIndex,\n                respondiendo: respondiendo,\n                onRespuesta: handleRespuesta,\n                onNavigate: handleNavigate,\n                onVolver: handleSalirModoEstudio\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 270,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"Mis Flashcards\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        colecciones: colecciones,\n                        coleccionSeleccionada: coleccionSeleccionada,\n                        onSeleccionarColeccion: handleSeleccionarColeccion,\n                        isLoading: isLoading\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 11\n                    }, undefined),\n                    coleccionSeleccionada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: coleccionSeleccionada.titulo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardStatistics__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                estadisticas: estadisticas\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: iniciarModoEstudio,\n                                            className: \"bg-orange-500 hover:bg-orange-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                                            children: \"Estudiar (\".concat(estadisticas ? estadisticas.paraHoy : 0, \" para hoy)\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{},\n                                            className: \"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                                            children: \"Ver estad\\xedsticas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 15\n                            }, undefined),\n                            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center items-center h-40\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 17\n                            }, undefined) : flashcards.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"No hay flashcards en esta colecci\\xf3n.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: flashcards.map((flashcard, index)=>{\n                                    var _flashcard_progreso, _flashcard_progreso1;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4 \".concat(flashcard.debeEstudiar ? 'border-orange-300 bg-orange-50' : 'border-gray-200'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            \"Tarjeta \",\n                                                            index + 1\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            ((_flashcard_progreso = flashcard.progreso) === null || _flashcard_progreso === void 0 ? void 0 : _flashcard_progreso.estado) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full text-xs \".concat(flashcard.progreso.estado === 'nuevo' ? 'bg-blue-100 text-blue-800' : flashcard.progreso.estado === 'aprendiendo' ? 'bg-yellow-100 text-yellow-800' : flashcard.progreso.estado === 'repasando' ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'),\n                                                                children: flashcard.progreso.estado\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            !((_flashcard_progreso1 = flashcard.progreso) === null || _flashcard_progreso1 === void 0 ? void 0 : _flashcard_progreso1.estado) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs\",\n                                                                children: \"nuevo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleEditarFlashcard(flashcard),\n                                                                        className: \"p-1 text-blue-500 hover:bg-blue-50 rounded transition-colors\",\n                                                                        title: \"Editar flashcard\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_7__.FiEdit2, {\n                                                                            size: 14\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                            lineNumber: 364,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setShowDeleteConfirm(flashcard.id),\n                                                                        disabled: deletingId === flashcard.id,\n                                                                        className: \"p-1 text-red-500 hover:bg-red-50 rounded transition-colors disabled:opacity-50\",\n                                                                        title: \"Eliminar flashcard\",\n                                                                        children: deletingId === flashcard.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_7__.FiRefreshCw, {\n                                                                            size: 14,\n                                                                            className: \"animate-spin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                            lineNumber: 373,\n                                                                            columnNumber: 33\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_7__.FiTrash2, {\n                                                                            size: 14\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                            lineNumber: 375,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                        lineNumber: 366,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-2\",\n                                                children: flashcard.pregunta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-700 line-clamp-2\",\n                                                children: flashcard.respuesta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, flashcard.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 21\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 279,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n        lineNumber: 262,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlashcardViewer, \"Xzhcu/FYdriaw0rdUfnEbNW9n1c=\");\n_c = FlashcardViewer;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlashcardViewer);\nvar _c;\n$RefreshReg$(_c, \"FlashcardViewer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/flashcards/FlashcardViewer.tsx\n"));

/***/ })

});