"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase/documentosService.ts":
/*!***********************************************!*\
  !*** ./src/lib/supabase/documentosService.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   eliminarDocumento: () => (/* binding */ eliminarDocumento),\n/* harmony export */   guardarDocumento: () => (/* binding */ guardarDocumento),\n/* harmony export */   obtenerDocumentoPorId: () => (/* binding */ obtenerDocumentoPorId),\n/* harmony export */   obtenerDocumentos: () => (/* binding */ obtenerDocumentos)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./authService */ \"(app-pages-browser)/./src/lib/supabase/authService.ts\");\n\n\n/**\n * Obtiene todos los documentos del usuario actual ordenados por número de tema\n */ async function obtenerDocumentos() {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return [];\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('documentos').select('*').eq('user_id', user.id).order('numero_tema', {\n            ascending: true\n        });\n        if (error) {\n            console.error('Error al obtener documentos:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error al obtener documentos:', error);\n        return [];\n    }\n}\n/**\n * Obtiene un documento específico por su ID (solo si pertenece al usuario actual)\n */ async function obtenerDocumentoPorId(id) {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('documentos').select('*').eq('id', id).eq('user_id', user.id).single();\n        if (error) {\n            console.error('Error al obtener documento:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error al obtener documento:', error);\n        return null;\n    }\n}\n/**\n * Guarda un nuevo documento en la base de datos asociado al usuario actual\n */ async function guardarDocumento(documento) {\n    try {\n        var _data_;\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        // Añadir el user_id y tipo_original al documento\n        const documentoConUsuario = {\n            ...documento,\n            user_id: user.id,\n            tipo_original: documento.tipo_original // Asegurarse que tipo_original se pasa aquí\n        };\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('documentos').insert([\n            documentoConUsuario\n        ]).select();\n        if (error) {\n            console.error('Error al guardar documento:', error);\n            return null;\n        }\n        return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n    } catch (error) {\n        console.error('Error al guardar documento:', error);\n        return null;\n    }\n}\n/**\n * Elimina un documento específico del usuario actual\n */ async function eliminarDocumento(documentoId) {\n    try {\n        console.log('🗑️ Iniciando eliminación de documento:', documentoId);\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('❌ No hay usuario autenticado para eliminar documento');\n            return false;\n        }\n        console.log('👤 Usuario autenticado:', user.id);\n        console.log('📄 Eliminando documento ID:', documentoId);\n        const { error, count } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('documentos').delete({\n            count: 'exact'\n        }).eq('id', documentoId).eq('user_id', user.id); // Asegurar que solo se eliminen documentos del usuario actual\n        if (error) {\n            console.error('❌ Error al eliminar documento de Supabase:', error);\n            return false;\n        }\n        console.log('✅ Documento eliminado exitosamente. Filas afectadas:', count);\n        if (count === 0) {\n            console.warn('⚠️ No se eliminó ningún documento. Posibles causas: documento no existe o no pertenece al usuario');\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('💥 Error inesperado al eliminar documento:', error);\n        return false;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/documentosService.ts\n"));

/***/ })

});