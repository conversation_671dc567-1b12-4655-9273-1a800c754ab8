"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(middleware)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1__);\n// src/middleware.ts\n\n\nasync function middleware(req) {\n    try {\n        // IMPORTANTE: Crear una respuesta base que Supabase puede modificar para añadir cookies\n        const res = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n        // IMPORTANTE: Inicializar el cliente Supabase para el middleware con req y res\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1__.createMiddlewareClient)({\n            req,\n            res\n        });\n        // IMPORTANTE: Obtener la sesión. Esto también refrescará el token si es necesario\n        // y actualizará las cookies en `res` si hay cambios.\n        const { data: { session } } = await supabase.auth.getSession();\n        const requestedPath = req.nextUrl.pathname;\n        // Verificar si hay una cookie de token de Supabase\n        const cookieHeader = req.headers.get('cookie') || '';\n        const hasSupabaseCookie = cookieHeader.includes('supabase.auth.token');\n        // Para dispositivos móviles, ser más permisivo con la detección de sesión\n        // Verificar también headers adicionales que pueden indicar una sesión activa\n        const authHeader = req.headers.get('authorization');\n        const hasAuthHeader = !!authHeader && authHeader.startsWith('Bearer ');\n        // Determinar si hay una sesión basada en múltiples fuentes\n        const hasSession = !!session || hasSupabaseCookie || hasAuthHeader;\n        // Definir rutas públicas que no requieren autenticación\n        const publicPaths = [\n            '/login'\n        ];\n        // Excluir rutas de API o recursos estáticos\n        if (requestedPath.startsWith('/api/') || requestedPath.startsWith('/_next/')) {\n            return res;\n        }\n        // --- LÓGICA DE AUTENTICACIÓN Y REDIRECCIÓN ---\n        // CASO 1: Usuario autenticado intentando acceder a /login\n        if (hasSession && requestedPath === '/login') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/', req.url));\n        }\n        // CASO 2: Usuario no autenticado intentando acceder a una ruta protegida\n        if (!hasSession && !publicPaths.includes(requestedPath)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/login', req.url));\n        }\n        // CASO 3: Permitir el acceso en todos los demás casos\n        // IMPORTANTE: Devolver la respuesta que puede tener cookies actualizadas\n        return res;\n    } catch (error) {\n        console.error('Error en middleware:', error);\n        // En caso de error, permitir el acceso pero sin cookies actualizadas\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n}\n// Configuración del matcher para definir en qué rutas se ejecutará el middleware.\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     *\n     * También puedes excluir rutas API específicas aquí si prefieres no hacerlo en el código del middleware,\n     * pero manejarlo en el código da más flexibilidad si algunas rutas API necesitan auth y otras no.\n     * Si todas las rutas /api/* están protegidas o no, puedes gestionarlo arriba.\n     *\n     * La expresión regular abajo intenta cubrir los casos más comunes:\n     */ '/((?!_next/static|_next/image|favicon.ico|manifest.json|robots.txt|.*\\\\..*).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});