# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## v1.0.0 - 2024-12-28

### Commits

- Initial implementation, tests, readme, types [`6e85f82`](https://github.com/ljharb/safe-push-apply/commit/6e85f82b116286503ff377e15708cd1584531c5f)
- Initial commit [`93928d9`](https://github.com/ljharb/safe-push-apply/commit/93928d9a1304ccc25b799528a4bbca8615f7614e)
- npm init [`5da39da`](https://github.com/ljharb/safe-push-apply/commit/5da39da33bb096e633d1e631a9374cfa0d7dc06b)
- Only apps should have lockfiles [`83fc8b4`](https://github.com/ljharb/safe-push-apply/commit/83fc8b4be29d680a27225329cc1ef5505626effa)
